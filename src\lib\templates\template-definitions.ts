export interface TemplateDefinition {
  id: string;
  name: string;
  category: string;
  description: string;
  thumbnail: string;
  htmlTemplate: string;
  sampleData: InvoiceData;
  styling: {
    primaryColor: string;
    fontFamily: string;
    layout: string;
  };
}

export interface InvoiceData {
  companyName: string;
  companyAddress: string;
  companyPhone: string;
  companyEmail: string;
  companyLogo?: string;
  invoiceNumber: string;
  invoiceDate: string;
  dueDate: string;
  clientName: string;
  clientAddress: string;
  clientPhone: string;
  clientEmail: string;
  lineItems: LineItem[];
  subtotal: number;
  tax: number;
  taxRate: number;
  total: number;
  notes?: string;
  paymentTerms: string;
}

export interface LineItem {
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

export const templateDefinitions: TemplateDefinition[] = [
  {
    id: 'professional',
    name: 'Professional',
    category: 'business',
    description: 'Clean corporate design for established businesses',
    thumbnail: '/images/templates/professional-thumb.jpg',
    styling: {
      primaryColor: '#2563eb',
      fontFamily: 'Arial, sans-serif',
      layout: 'two-column'
    },
    htmlTemplate: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice {{invoiceNumber}}</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .invoice-container { 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 40px; 
            background: white;
          }
          .header { 
            display: flex; 
            justify-content: space-between; 
            align-items: flex-start;
            margin-bottom: 40px; 
            padding-bottom: 20px;
            border-bottom: 3px solid #2563eb;
          }
          .company-info { flex: 1; }
          .company-logo { 
            max-width: 200px; 
            height: auto; 
            margin-bottom: 20px;
          }
          .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
          }
          .company-details {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
          }
          .invoice-details { 
            text-align: right;
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
          }
          .invoice-title { 
            font-size: 32px; 
            color: #2563eb; 
            font-weight: bold; 
            margin-bottom: 15px;
          }
          .invoice-meta {
            font-size: 14px;
            color: #666;
            line-height: 1.8;
          }
          .invoice-meta strong {
            color: #333;
          }
          .client-section { 
            margin: 40px 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
          }
          .bill-to-section {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
          }
          .bill-to { 
            font-weight: bold; 
            color: #2563eb;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 15px; 
          }
          .client-info {
            font-size: 14px;
            line-height: 1.6;
          }
          .client-name {
            font-weight: bold;
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
          }
          .items-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 40px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
          }
          .items-table th { 
            background: #2563eb;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: bold;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .items-table td { 
            padding: 15px; 
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
          }
          .items-table tr:nth-child(even) {
            background: #f8fafc;
          }
          .items-table tr:hover {
            background: #f1f5f9;
          }
          .items-table .amount-cell {
            text-align: right;
            font-weight: 600;
          }
          .total-section { 
            margin-top: 40px; 
            display: flex;
            justify-content: flex-end;
          }
          .total-container {
            width: 300px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
          }
          .total-row { 
            display: flex; 
            justify-content: space-between; 
            padding: 12px 20px;
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
          }
          .total-row:last-child {
            border-bottom: none;
          }
          .subtotal-row {
            background: white;
          }
          .tax-row {
            background: white;
          }
          .total-amount { 
            background: #2563eb;
            color: white;
            font-size: 18px; 
            font-weight: bold;
          }
          .notes-section { 
            margin-top: 40px;
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
          }
          .notes-title {
            font-weight: bold;
            color: #065f46;
            margin-bottom: 10px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .notes-content {
            color: #374151;
            font-size: 14px;
            line-height: 1.6;
          }
          .payment-terms { 
            margin-top: 30px; 
            padding: 20px;
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            text-align: center;
          }
          .payment-terms-title {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 8px;
            font-size: 14px;
          }
          .payment-terms-text {
            color: #78350f;
            font-size: 14px;
          }
          .footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #666;
            font-size: 12px;
          }
          @media print { 
            .invoice-container { 
              margin: 0; 
              padding: 20px;
              max-width: none;
            }
            .header {
              page-break-inside: avoid;
            }
            .items-table {
              page-break-inside: avoid;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="header">
            <div class="company-info">
              {{#if companyLogo}}<img src="{{companyLogo}}" alt="Company Logo" class="company-logo">{{/if}}
              <div class="company-name">{{companyName}}</div>
              <div class="company-details">
                <div>{{companyAddress}}</div>
                <div>{{companyPhone}}</div>
                <div>{{companyEmail}}</div>
              </div>
            </div>
            <div class="invoice-details">
              <div class="invoice-title">INVOICE</div>
              <div class="invoice-meta">
                <div><strong>Invoice #:</strong> {{invoiceNumber}}</div>
                <div><strong>Date:</strong> {{invoiceDate}}</div>
                <div><strong>Due Date:</strong> {{dueDate}}</div>
              </div>
            </div>
          </div>
          
          <div class="client-section">
            <div class="bill-to-section">
              <div class="bill-to">Bill To:</div>
              <div class="client-info">
                <div class="client-name">{{clientName}}</div>
                <div>{{clientAddress}}</div>
                <div>{{clientPhone}}</div>
                <div>{{clientEmail}}</div>
              </div>
            </div>
          </div>
          
          <table class="items-table">
            <thead>
              <tr>
                <th>Description</th>
                <th style="text-align: center; width: 80px;">Qty</th>
                <th style="text-align: right; width: 100px;">Rate</th>
                <th style="text-align: right; width: 120px;">Amount</th>
              </tr>
            </thead>
            <tbody>
              {{#each lineItems}}
              <tr>
                <td>{{description}}</td>
                <td style="text-align: center;">{{quantity}}</td>
                <td style="text-align: right;">\${{rate}}</td>
                <td class="amount-cell">\${{amount}}</td>
              </tr>
              {{/each}}
            </tbody>
          </table>
          
          <div class="total-section">
            <div class="total-container">
              <div class="total-row subtotal-row">
                <span>Subtotal:</span>
                <span>\${{subtotal}}</span>
              </div>
              {{#if tax}}
              <div class="total-row tax-row">
                <span>Tax ({{taxRate}}%):</span>
                <span>\${{tax}}</span>
              </div>
              {{/if}}
              <div class="total-row total-amount">
                <span>Total:</span>
                <span>\${{total}}</span>
              </div>
            </div>
          </div>
          
          {{#if notes}}
          <div class="notes-section">
            <div class="notes-title">Notes</div>
            <div class="notes-content">{{notes}}</div>
          </div>
          {{/if}}
          
          <div class="payment-terms">
            <div class="payment-terms-title">Payment Terms</div>
            <div class="payment-terms-text">{{paymentTerms}}</div>
          </div>
          
          <div class="footer">
            Thank you for your business!
          </div>
        </div>
      </body>
      </html>
    `,
    sampleData: {
      companyName: 'Acme Corporation',
      companyAddress: '123 Business Street, Suite 100, New York, NY 10001',
      companyPhone: '(*************',
      companyEmail: '<EMAIL>',
      invoiceNumber: 'INV-001',
      invoiceDate: '2024-12-10',
      dueDate: '2024-12-25',
      clientName: 'John Smith',
      clientAddress: '456 Client Avenue, Los Angeles, CA 90001',
      clientPhone: '(*************',
      clientEmail: '<EMAIL>',
      lineItems: [
        { description: 'Professional Consulting Services', quantity: 10, rate: 150, amount: 1500 },
        { description: 'Strategic Planning Session', quantity: 5, rate: 200, amount: 1000 }
      ],
      subtotal: 2500,
      tax: 250,
      taxRate: 10,
      total: 2750,
      notes: 'Thank you for choosing our professional services. We look forward to continuing our partnership.',
      paymentTerms: 'Payment is due within 30 days of invoice date. Late payments may incur a 1.5% monthly service charge.'
    }
  },

  {
    id: 'modern',
    name: 'Modern',
    category: 'creative',
    description: 'Contemporary design with bold typography and vibrant colors',
    thumbnail: '/images/templates/modern-thumb.jpg',
    styling: {
      primaryColor: '#7c3aed',
      fontFamily: 'Inter, sans-serif',
      layout: 'split-design'
    },
    htmlTemplate: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice {{invoiceNumber}}</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { 
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
          }
          .invoice-container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          }
          .header-section {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            color: white;
            padding: 40px;
            position: relative;
            overflow: hidden;
          }
          .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s ease-in-out infinite;
          }
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
          }
          .header-content {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
          }
          .company-section {
            flex: 1;
          }
          .company-logo { 
            max-width: 150px; 
            height: auto; 
            margin-bottom: 20px;
            filter: brightness(0) invert(1);
          }
          .company-name {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 15px;
            letter-spacing: -0.5px;
          }
          .company-details {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.6;
          }
          .invoice-info {
            text-align: right;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
          }
          .invoice-title {
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 20px;
            letter-spacing: -1px;
          }
          .invoice-meta {
            font-size: 14px;
            line-height: 1.8;
          }
          .main-content {
            padding: 40px;
          }
          .client-section {
            margin-bottom: 40px;
          }
          .client-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #7c3aed;
          }
          .bill-to {
            font-weight: 700;
            color: #7c3aed;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 15px;
          }
          .client-name {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 10px;
          }
          .client-details {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.6;
          }
          .items-section {
            background: #1f2937;
            margin: 0 -40px 40px -40px;
            padding: 0;
            border-radius: 0;
          }
          .items-table {
            width: 100%;
            border-collapse: collapse;
            color: white;
          }
          .items-table th {
            background: #111827;
            padding: 20px;
            text-align: left;
            font-weight: 700;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .items-table td {
            padding: 20px;
            border-bottom: 1px solid #374151;
            font-size: 14px;
          }
          .items-table tr:hover {
            background: #374151;
          }
          .amount-cell {
            text-align: right;
            font-weight: 600;
            color: #a855f7;
          }
          .total-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 40px;
          }
          .total-container {
            width: 350px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            padding: 15px 25px;
            border-bottom: 1px solid #d1d5db;
            font-size: 14px;
          }
          .total-row:last-child {
            border-bottom: none;
          }
          .total-final {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            color: white;
            font-size: 20px;
            font-weight: 800;
            padding: 20px 25px;
          }
          .notes-section {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #a7f3d0;
            margin-bottom: 30px;
          }
          .notes-title {
            font-weight: 700;
            color: #065f46;
            margin-bottom: 12px;
            font-size: 16px;
          }
          .notes-content {
            color: #047857;
            font-size: 14px;
            line-height: 1.7;
          }
          .payment-section {
            text-align: center;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #f59e0b;
          }
          .payment-title {
            font-weight: 700;
            color: #92400e;
            margin-bottom: 10px;
            font-size: 16px;
          }
          .payment-text {
            color: #78350f;
            font-size: 14px;
            line-height: 1.6;
          }
          @media print {
            body { background: white; padding: 0; }
            .invoice-container { 
              box-shadow: none; 
              border-radius: 0;
              max-width: none;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="header-section">
            <div class="header-content">
              <div class="company-section">
                {{#if companyLogo}}<img src="{{companyLogo}}" alt="Company Logo" class="company-logo">{{/if}}
                <div class="company-name">{{companyName}}</div>
                <div class="company-details">
                  <div>{{companyAddress}}</div>
                  <div>{{companyPhone}}</div>
                  <div>{{companyEmail}}</div>
                </div>
              </div>
              <div class="invoice-info">
                <div class="invoice-title">INVOICE</div>
                <div class="invoice-meta">
                  <div><strong>{{invoiceNumber}}</strong></div>
                  <div>{{invoiceDate}}</div>
                  <div style="color: #fbbf24; font-weight: 600;">Due: {{dueDate}}</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="main-content">
            <div class="client-section">
              <div class="client-card">
                <div class="bill-to">Bill To</div>
                <div class="client-name">{{clientName}}</div>
                <div class="client-details">
                  <div>{{clientAddress}}</div>
                  <div>{{clientPhone}}</div>
                  <div>{{clientEmail}}</div>
                </div>
              </div>
            </div>
            
            <div class="items-section">
              <table class="items-table">
                <thead>
                  <tr>
                    <th>Service Description</th>
                    <th style="text-align: center; width: 80px;">Qty</th>
                    <th style="text-align: right; width: 100px;">Rate</th>
                    <th style="text-align: right; width: 120px;">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {{#each lineItems}}
                  <tr>
                    <td>{{description}}</td>
                    <td style="text-align: center;">{{quantity}}</td>
                    <td style="text-align: right;">\${{rate}}</td>
                    <td class="amount-cell">\${{amount}}</td>
                  </tr>
                  {{/each}}
                </tbody>
              </table>
            </div>
            
            <div class="total-section">
              <div class="total-container">
                <div class="total-row">
                  <span>Subtotal</span>
                  <span>\${{subtotal}}</span>
                </div>
                {{#if tax}}
                <div class="total-row">
                  <span>Tax ({{taxRate}}%)</span>
                  <span>\${{tax}}</span>
                </div>
                {{/if}}
                <div class="total-final">
                  <span>TOTAL</span>
                  <span>\${{total}}</span>
                </div>
              </div>
            </div>
            
            {{#if notes}}
            <div class="notes-section">
              <div class="notes-title">Project Notes</div>
              <div class="notes-content">{{notes}}</div>
            </div>
            {{/if}}
            
            <div class="payment-section">
              <div class="payment-title">Payment Terms</div>
              <div class="payment-text">{{paymentTerms}}</div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    sampleData: {
      companyName: 'Creative Studio',
      companyAddress: '789 Design Avenue, Creative District, SF 94102',
      companyPhone: '(*************',
      companyEmail: '<EMAIL>',
      invoiceNumber: 'CS-2024-001',
      invoiceDate: '2024-12-10',
      dueDate: '2024-12-24',
      clientName: 'Tech Startup Inc.',
      clientAddress: '456 Innovation Street, Silicon Valley, CA 94301',
      clientPhone: '(*************',
      clientEmail: '<EMAIL>',
      lineItems: [
        { description: 'Brand Identity Design Package', quantity: 1, rate: 2500, amount: 2500 },
        { description: 'Website UI/UX Design', quantity: 1, rate: 3500, amount: 3500 },
        { description: 'Marketing Materials Design', quantity: 1, rate: 1200, amount: 1200 }
      ],
      subtotal: 7200,
      tax: 576,
      taxRate: 8,
      total: 7776,
      notes: 'This project includes complete brand identity package with logo variations, website design mockups, and marketing collateral. All source files will be delivered upon final payment.',
      paymentTerms: 'Payment due within 15 days. 50% deposit required to begin work. Late payments subject to 2% monthly fee.'
    }
  },

  {
    id: 'freelancer',
    name: 'Freelancer',
    category: 'freelance',
    description: 'Simple, clean design perfect for independent contractors',
    thumbnail: '/images/templates/freelancer-thumb.jpg',
    styling: {
      primaryColor: '#059669',
      fontFamily: 'Inter, sans-serif',
      layout: 'minimal'
    },
    htmlTemplate: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice {{invoiceNumber}}</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { 
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; 
            line-height: 1.6; 
            color: #1f2937;
            background: #f9fafb;
            padding: 40px 20px;
          }
          .invoice-container { 
            max-width: 700px; 
            margin: 0 auto; 
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }
          .header {
            background: white;
            padding: 40px 40px 30px 40px;
            border-bottom: 3px solid #059669;
            text-align: center;
          }
          .invoice-title {
            font-size: 32px;
            font-weight: 600;
            color: #059669;
            margin-bottom: 8px;
            letter-spacing: 2px;
          }
          .invoice-subtitle {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 30px;
          }
          .invoice-number {
            display: inline-block;
            background: #059669;
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
          }
          .main-content {
            padding: 40px;
          }
          .info-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
          }
          .info-block {
            background: #f9fafb;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #059669;
          }
          .info-title {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            color: #059669;
            letter-spacing: 1px;
            margin-bottom: 15px;
          }
          .company-name, .client-name {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
          }
          .contact-info {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.6;
          }
          .contact-info .email {
            color: #059669;
            font-weight: 500;
          }
          .dates-section {
            background: #ecfdf5;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bbf7d0;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .date-item {
            text-align: center;
          }
          .date-label {
            font-size: 12px;
            font-weight: 600;
            color: #065f46;
            margin-bottom: 4px;
          }
          .date-value {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
          }
          .due-date {
            color: #dc2626;
            font-weight: 700;
          }
          .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
          }
          .items-table th {
            background: #f9fafb;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            border-bottom: 1px solid #e5e7eb;
          }
          .items-table td {
            padding: 15px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 14px;
          }
          .items-table tr:last-child td {
            border-bottom: none;
          }
          .items-table tr:hover {
            background: #f9fafb;
          }
          .description-cell {
            font-weight: 500;
            color: #1f2937;
          }
          .amount-cell {
            text-align: right;
            font-weight: 600;
            color: #1f2937;
          }
          .total-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 40px;
          }
          .total-container {
            width: 300px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 20px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
          }
          .total-row:last-child {
            border-bottom: none;
          }
          .total-final {
            background: #059669;
            color: white;
            font-size: 18px;
            font-weight: 700;
            padding: 16px 20px;
          }
          .payment-info {
            background: #ecfdf5;
            padding: 25px;
            border-radius: 8px;
            border: 1px solid #bbf7d0;
            text-align: center;
            margin-bottom: 20px;
          }
          .payment-title {
            font-weight: 600;
            color: #065f46;
            margin-bottom: 8px;
            font-size: 16px;
          }
          .payment-text {
            color: #047857;
            font-size: 14px;
            line-height: 1.6;
          }
          .notes-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #6b7280;
          }
          .notes-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
            font-size: 14px;
          }
          .notes-content {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.6;
          }
          .footer {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            font-size: 12px;
            border-top: 1px solid #e5e7eb;
          }
          @media print {
            body { background: white; padding: 0; }
            .invoice-container { 
              box-shadow: none; 
              border-radius: 0;
              max-width: none;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="header">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-subtitle">Freelance Services</div>
            <div class="invoice-number">{{invoiceNumber}}</div>
          </div>
          
          <div class="main-content">
            <div class="info-section">
              <div class="info-block">
                <div class="info-title">From</div>
                <div class="company-name">{{companyName}}</div>
                <div class="contact-info">
                  <div>{{companyAddress}}</div>
                  <div>{{companyPhone}}</div>
                  <div class="email">{{companyEmail}}</div>
                </div>
              </div>
              
              <div class="info-block">
                <div class="info-title">To</div>
                <div class="client-name">{{clientName}}</div>
                <div class="contact-info">
                  <div>{{clientAddress}}</div>
                  <div>{{clientPhone}}</div>
                  <div class="email">{{clientEmail}}</div>
                </div>
              </div>
            </div>
            
            <div class="dates-section">
              <div class="date-item">
                <div class="date-label">Issue Date</div>
                <div class="date-value">{{invoiceDate}}</div>
              </div>
              <div class="date-item">
                <div class="date-label">Due Date</div>
                <div class="date-value due-date">{{dueDate}}</div>
              </div>
            </div>
            
            <table class="items-table">
              <thead>
                <tr>
                  <th>Service Description</th>
                  <th style="text-align: center; width: 80px;">Hours</th>
                  <th style="text-align: right; width: 100px;">Rate</th>
                  <th style="text-align: right; width: 100px;">Amount</th>
                </tr>
              </thead>
              <tbody>
                {{#each lineItems}}
                <tr>
                  <td class="description-cell">{{description}}</td>
                  <td style="text-align: center;">{{quantity}}</td>
                  <td style="text-align: right;">\${{rate}}</td>
                  <td class="amount-cell">\${{amount}}</td>
                </tr>
                {{/each}}
              </tbody>
            </table>
            
            <div class="total-section">
              <div class="total-container">
                <div class="total-row">
                  <span>Subtotal</span>
                  <span>\${{subtotal}}</span>
                </div>
                {{#if tax}}
                <div class="total-row">
                  <span>Tax ({{taxRate}}%)</span>
                  <span>\${{tax}}</span>
                </div>
                {{/if}}
                <div class="total-final">
                  <span>Total</span>
                  <span>\${{total}}</span>
                </div>
              </div>
            </div>
            
            <div class="payment-info">
              <div class="payment-title">Payment Information</div>
              <div class="payment-text">{{paymentTerms}}</div>
            </div>
            
            {{#if notes}}
            <div class="notes-section">
              <div class="notes-title">Notes</div>
              <div class="notes-content">{{notes}}</div>
            </div>
            {{/if}}
          </div>
          
          <div class="footer">
            Thank you for your business!
          </div>
        </div>
      </body>
      </html>
    `,
    sampleData: {
      companyName: 'Sarah Johnson',
      companyAddress: '123 Freelancer Lane, Remote Work City, RW 12345',
      companyPhone: '(*************',
      companyEmail: '<EMAIL>',
      invoiceNumber: 'SJ-2024-012',
      invoiceDate: '2024-12-10',
      dueDate: '2024-12-24',
      clientName: 'Small Business Co.',
      clientAddress: '789 Main Street, Business Town, BT 54321',
      clientPhone: '(*************',
      clientEmail: '<EMAIL>',
      lineItems: [
        { description: 'Website Development & Setup', quantity: 40, rate: 75, amount: 3000 },
        { description: 'Content Creation & SEO', quantity: 15, rate: 65, amount: 975 },
        { description: 'Training & Documentation', quantity: 8, rate: 70, amount: 560 }
      ],
      subtotal: 4535,
      tax: 362.8,
      taxRate: 8,
      total: 4897.8,
      notes: 'Project completed successfully with all deliverables met. Includes 30 days of free support post-launch.',
      paymentTerms: 'Payment due within 14 days via bank transfer or PayPal. Thank you for choosing my services!'
    }
  },

  {
    id: 'photography',
    name: 'Photography',
    category: 'creative',
    description: 'Elegant design tailored for photographers and visual artists',
    thumbnail: '/images/templates/photography-thumb.jpg',
    styling: {
      primaryColor: '#dc2626',
      fontFamily: 'Georgia, serif',
      layout: 'artistic'
    },
    htmlTemplate: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice {{invoiceNumber}}</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { 
            font-family: Georgia, serif; 
            background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
            padding: 40px 20px;
            line-height: 1.6;
          }
          .invoice-container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            overflow: hidden;
          }
          .header-section {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: white;
            padding: 50px 40px;
            position: relative;
          }
          .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M10 10h80v80h-80z" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></svg>') repeat;
          }
          .header-content {
            position: relative;
            z-index: 1;
            text-align: center;
          }
          .camera-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #dc2626, #ef4444);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 32px;
          }
          .company-name {
            font-size: 32px;
            font-weight: 300;
            margin-bottom: 15px;
            letter-spacing: 2px;
          }
          .tagline {
            font-size: 16px;
            opacity: 0.8;
            font-style: italic;
            margin-bottom: 30px;
          }
          .divider {
            width: 60px;
            height: 2px;
            background: #dc2626;
            margin: 0 auto 30px;
          }
          .contact-info {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.8;
          }
          .main-content {
            padding: 50px 40px;
          }
          .invoice-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            border: 1px solid #cbd5e1;
          }
          .invoice-info h2 {
            font-size: 24px;
            color: #1f2937;
            margin-bottom: 15px;
            font-weight: 300;
          }
          .invoice-meta {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.8;
          }
          .invoice-number {
            background: #dc2626;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 1px;
          }
          .client-section {
            background: #fef2f2;
            padding: 30px;
            border-radius: 12px;
            border: 1px solid #fecaca;
            margin-bottom: 40px;
          }
          .client-title {
            font-size: 18px;
            color: #991b1b;
            margin-bottom: 20px;
            font-weight: 600;
          }
          .client-name {
            font-size: 22px;
            color: #1f2937;
            margin-bottom: 10px;
            font-weight: 600;
          }
          .client-details {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.6;
          }
          .services-section {
            margin-bottom: 40px;
          }
          .services-title {
            font-size: 24px;
            color: #1f2937;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 300;
            position: relative;
          }
          .services-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: #dc2626;
          }
          .items-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .items-table th {
            background: #1f2937;
            color: white;
            padding: 20px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
          }
          .items-table td {
            padding: 20px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 14px;
          }
          .items-table tr:last-child td {
            border-bottom: none;
          }
          .items-table tr:nth-child(even) {
            background: #fafafa;
          }
          .service-description {
            font-weight: 500;
            color: #1f2937;
          }
          .amount-cell {
            text-align: right;
            font-weight: 600;
            color: #1f2937;
          }
          .total-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 40px;
          }
          .total-container {
            width: 350px;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            padding: 15px 25px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 16px;
          }
          .total-row:last-child {
            border-bottom: none;
          }
          .total-final {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            font-size: 20px;
            font-weight: 700;
            padding: 20px 25px;
          }
          .notes-section {
            background: #fef2f2;
            padding: 30px;
            border-radius: 12px;
            border: 1px solid #fecaca;
            text-align: center;
            margin-bottom: 30px;
          }
          .notes-title {
            font-size: 18px;
            color: #991b1b;
            margin-bottom: 15px;
            font-weight: 600;
          }
          .notes-content {
            color: #7f1d1d;
            font-size: 14px;
            line-height: 1.7;
            font-style: italic;
          }
          .footer {
            text-align: center;
            padding: 30px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
          }
          .signature {
            margin-top: 20px;
            font-style: italic;
            color: #dc2626;
          }
          @media print {
            body { background: white; padding: 0; }
            .invoice-container { 
              box-shadow: none; 
              border-radius: 0;
              max-width: none;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="header-section">
            <div class="header-content">
              <div class="camera-icon">📷</div>
              <div class="company-name">{{companyName}}</div>
              <div class="tagline">Professional Photography Services</div>
              <div class="divider"></div>
              <div class="contact-info">
                <div>{{companyAddress}}</div>
                <div>{{companyPhone}}</div>
                <div>{{companyEmail}}</div>
              </div>
            </div>
          </div>
          
          <div class="main-content">
            <div class="invoice-details">
              <div class="invoice-info">
                <h2>Photography Invoice</h2>
                <div class="invoice-meta">
                  <div><strong>Date:</strong> {{invoiceDate}}</div>
                  <div><strong>Due:</strong> {{dueDate}}</div>
                </div>
              </div>
              <div class="invoice-number">{{invoiceNumber}}</div>
            </div>
            
            <div class="client-section">
              <div class="client-title">Session Details For:</div>
              <div class="client-name">{{clientName}}</div>
              <div class="client-details">
                <div>{{clientAddress}}</div>
                <div>{{clientPhone}}</div>
                <div>{{clientEmail}}</div>
              </div>
            </div>
            
            <div class="services-section">
              <div class="services-title">Photography Services</div>
              
              <table class="items-table">
                <thead>
                  <tr>
                    <th>Service Description</th>
                    <th style="text-align: center; width: 100px;">Sessions</th>
                    <th style="text-align: right; width: 120px;">Rate</th>
                    <th style="text-align: right; width: 120px;">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {{#each lineItems}}
                  <tr>
                    <td class="service-description">{{description}}</td>
                    <td style="text-align: center;">{{quantity}}</td>
                    <td style="text-align: right;">\${{rate}}</td>
                    <td class="amount-cell">\${{amount}}</td>
                  </tr>
                  {{/each}}
                </tbody>
              </table>
            </div>
            
            <div class="total-section">
              <div class="total-container">
                <div class="total-row">
                  <span>Subtotal</span>
                  <span>\${{subtotal}}</span>
                </div>
                {{#if tax}}
                <div class="total-row">
                  <span>Tax ({{taxRate}}%)</span>
                  <span>\${{tax}}</span>
                </div>
                {{/if}}
                <div class="total-final">
                  <span>TOTAL</span>
                  <span>\${{total}}</span>
                </div>
              </div>
            </div>
            
            {{#if notes}}
            <div class="notes-section">
              <div class="notes-title">Session Notes</div>
              <div class="notes-content">{{notes}}</div>
            </div>
            {{/if}}
          </div>
          
          <div class="footer">
            <div>Thank you for choosing our photography services!</div>
            <div>{{paymentTerms}}</div>
            <div class="signature">Capturing your precious moments</div>
          </div>
        </div>
      </body>
      </html>
    `,
    sampleData: {
      companyName: 'Emma\'s Photography',
      companyAddress: '456 Creative Boulevard, Arts District, LA 90012',
      companyPhone: '(*************',
      companyEmail: '<EMAIL>',
      invoiceNumber: 'EP-2024-089',
      invoiceDate: '2024-12-10',
      dueDate: '2024-12-20',
      clientName: 'Jennifer & Mark Wilson',
      clientAddress: '789 Wedding Lane, Beverly Hills, CA 90210',
      clientPhone: '(*************',
      clientEmail: '<EMAIL>',
      lineItems: [
        { description: 'Wedding Photography Package (8 hours)', quantity: 1, rate: 2500, amount: 2500 },
        { description: 'Engagement Photo Session', quantity: 1, rate: 500, amount: 500 },
        { description: 'Professional Photo Editing & Retouching', quantity: 1, rate: 800, amount: 800 },
        { description: 'Digital Gallery & USB Drive', quantity: 1, rate: 200, amount: 200 }
      ],
      subtotal: 4000,
      tax: 320,
      taxRate: 8,
      total: 4320,
      notes: 'Wedding photography package includes full-day coverage, 500+ edited photos, online gallery, and print release. Engagement session photos will be delivered within 1 week, wedding photos within 4 weeks.',
      paymentTerms: '50% deposit due upon booking, remaining balance due 1 week before event. Final payment required before photo delivery.'
    }
  },

  {
    id: 'consulting',
    name: 'Consulting',
    category: 'professional',
    description: 'Executive template designed for consultants and advisors',
    thumbnail: '/images/templates/consulting-thumb.jpg',
    styling: {
      primaryColor: '#1e40af',
      fontFamily: 'Times New Roman, serif',
      layout: 'executive'
    },
    htmlTemplate: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice {{invoiceNumber}}</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { 
            font-family: 'Times New Roman', serif; 
            background: white;
            color: #1f2937;
            line-height: 1.6;
          }
          .invoice-container { 
            max-width: 800px; 
            margin: 0 auto; 
            border: 3px solid #1e40af;
            border-radius: 0;
          }
          .header-section {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
          }
          .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
              45deg,
              transparent,
              transparent 10px,
              rgba(255,255,255,0.05) 10px,
              rgba(255,255,255,0.05) 20px
            );
          }
          .header-content {
            position: relative;
            z-index: 1;
          }
          .firm-name {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: 2px;
          }
          .firm-tagline {
            font-size: 18px;
            margin-bottom: 30px;
            opacity: 0.9;
            font-style: italic;
          }
          .firm-details {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.8;
          }
          .main-content {
            padding: 40px;
            background: white;
          }
          .invoice-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid #cbd5e1;
          }
          .invoice-title {
            font-size: 28px;
            color: #1e40af;
            margin-bottom: 20px;
            font-weight: 700;
          }
          .invoice-subtitle {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 20px;
          }
          .invoice-number {
            display: inline-block;
            background: #1f2937;
            color: white;
            padding: 10px 30px;
            font-size: 20px;
            font-weight: 700;
            letter-spacing: 1px;
          }
          .parties-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
          }
          .party-block {
            background: #f8fafc;
            padding: 30px;
            border-left: 5px solid #1e40af;
          }
          .party-title {
            font-size: 16px;
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
          }
          .party-name {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 15px;
          }
          .party-details {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.8;
          }
          .dates-section {
            background: #fef3c7;
            padding: 25px;
            border: 2px solid #f59e0b;
            margin-bottom: 30px;
            text-align: center;
          }
          .dates-title {
            font-size: 16px;
            font-weight: 700;
            color: #92400e;
            margin-bottom: 15px;
          }
          .dates-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
          }
          .date-item {
            text-align: center;
          }
          .date-label {
            font-size: 12px;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 5px;
            text-transform: uppercase;
          }
          .date-value {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
          }
          .services-section {
            margin-bottom: 40px;
          }
          .services-title {
            font-size: 20px;
            color: #1e40af;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
          }
          .items-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #1e40af;
          }
          .items-table th {
            background: #1e40af;
            color: white;
            padding: 20px;
            text-align: left;
            font-weight: 700;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .items-table td {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
          }
          .items-table tr:nth-child(even) {
            background: #fafafa;
          }
          .service-description {
            font-weight: 600;
            color: #1f2937;
          }
          .amount-cell {
            text-align: right;
            font-weight: 700;
            color: #1f2937;
          }
          .total-section {
            margin-bottom: 40px;
            display: flex;
            justify-content: flex-end;
          }
          .total-container {
            width: 400px;
            border: 2px solid #1e40af;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            padding: 15px 25px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 16px;
            background: #f8fafc;
          }
          .total-row:last-child {
            border-bottom: none;
          }
          .total-final {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            font-size: 22px;
            font-weight: 700;
            padding: 20px 25px;
          }
          .terms-section {
            background: #eff6ff;
            padding: 30px;
            border: 2px solid #bfdbfe;
            margin-bottom: 30px;
          }
          .terms-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 15px;
            text-transform: uppercase;
          }
          .terms-content {
            color: #1e3a8a;
            font-size: 14px;
            line-height: 1.7;
          }
          .notes-section {
            background: #f9fafb;
            padding: 30px;
            border-left: 5px solid #6b7280;
            margin-bottom: 30px;
          }
          .notes-title {
            font-size: 16px;
            font-weight: 700;
            color: #374151;
            margin-bottom: 15px;
          }
          .notes-content {
            color: #4b5563;
            font-size: 14px;
            line-height: 1.7;
          }
          .footer-section {
            background: #1f2937;
            color: white;
            padding: 30px;
            text-align: center;
          }
          .footer-message {
            font-size: 16px;
            margin-bottom: 10px;
          }
          .footer-tagline {
            font-size: 12px;
            opacity: 0.8;
            font-style: italic;
          }
          @media print {
            .invoice-container { 
              border: 2px solid #1e40af;
              max-width: none;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="header-section">
            <div class="header-content">
              <div class="firm-name">{{companyName}}</div>
              <div class="firm-tagline">Strategic Business Consulting</div>
              <div class="firm-details">
                <div>{{companyAddress}}</div>
                <div>{{companyPhone}} | {{companyEmail}}</div>
              </div>
            </div>
          </div>
          
          <div class="main-content">
            <div class="invoice-header">
              <div class="invoice-title">CONSULTING INVOICE</div>
              <div class="invoice-subtitle">Professional Advisory Services</div>
              <div class="invoice-number">{{invoiceNumber}}</div>
            </div>
            
            <div class="parties-section">
              <div class="party-block">
                <div class="party-title">Consultant</div>
                <div class="party-name">{{companyName}}</div>
                <div class="party-details">
                  <div>{{companyAddress}}</div>
                  <div>{{companyPhone}}</div>
                  <div>{{companyEmail}}</div>
                </div>
              </div>
              
              <div class="party-block">
                <div class="party-title">Client</div>
                <div class="party-name">{{clientName}}</div>
                <div class="party-details">
                  <div>{{clientAddress}}</div>
                  <div>{{clientPhone}}</div>
                  <div>{{clientEmail}}</div>
                </div>
              </div>
            </div>
            
            <div class="dates-section">
              <div class="dates-title">Invoice Information</div>
              <div class="dates-grid">
                <div class="date-item">
                  <div class="date-label">Issue Date</div>
                  <div class="date-value">{{invoiceDate}}</div>
                </div>
                <div class="date-item">
                  <div class="date-label">Due Date</div>
                  <div class="date-value" style="color: #dc2626;">{{dueDate}}</div>
                </div>
                <div class="date-item">
                  <div class="date-label">Terms</div>
                  <div class="date-value" style="font-size: 14px;">Net 30</div>
                </div>
              </div>
            </div>
            
            <div class="services-section">
              <div class="services-title">Consulting Services Provided</div>
              
              <table class="items-table">
                <thead>
                  <tr>
                    <th>Service Description</th>
                    <th style="text-align: center; width: 100px;">Hours</th>
                    <th style="text-align: right; width: 120px;">Rate</th>
                    <th style="text-align: right; width: 120px;">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {{#each lineItems}}
                  <tr>
                    <td class="service-description">{{description}}</td>
                    <td style="text-align: center;">{{quantity}}</td>
                    <td style="text-align: right;">\${{rate}}</td>
                    <td class="amount-cell">\${{amount}}</td>
                  </tr>
                  {{/each}}
                </tbody>
              </table>
            </div>
            
            <div class="total-section">
              <div class="total-container">
                <div class="total-row">
                  <span>Subtotal</span>
                  <span>\${{subtotal}}</span>
                </div>
                {{#if tax}}
                <div class="total-row">
                  <span>Tax ({{taxRate}}%)</span>
                  <span>\${{tax}}</span>
                </div>
                {{/if}}
                <div class="total-final">
                  <span>TOTAL DUE</span>
                  <span>\${{total}}</span>
                </div>
              </div>
            </div>
            
            <div class="terms-section">
              <div class="terms-title">Payment Terms & Conditions</div>
              <div class="terms-content">{{paymentTerms}}</div>
            </div>
            
            {{#if notes}}
            <div class="notes-section">
              <div class="notes-title">Project Notes</div>
              <div class="notes-content">{{notes}}</div>
            </div>
            {{/if}}
          </div>
          
          <div class="footer-section">
            <div class="footer-message">Thank you for your business and trust in our expertise.</div>
            <div class="footer-tagline">Driving strategic success through expert consultation</div>
          </div>
        </div>
      </body>
      </html>
    `,
    sampleData: {
      companyName: 'Strategic Solutions Group',
      companyAddress: '1200 Executive Drive, Suite 500, Chicago, IL 60601',
      companyPhone: '(*************',
      companyEmail: '<EMAIL>',
      invoiceNumber: 'SSG-2024-045',
      invoiceDate: '2024-12-10',
      dueDate: '2025-01-09',
      clientName: 'Fortune Corp.',
      clientAddress: '5678 Corporate Plaza, Enterprise City, TX 75201',
      clientPhone: '(*************',
      clientEmail: '<EMAIL>',
      lineItems: [
        { description: 'Strategic Planning & Analysis', quantity: 25, rate: 250, amount: 6250 },
        { description: 'Market Research & Competitive Analysis', quantity: 18, rate: 200, amount: 3600 },
        { description: 'Implementation Strategy Development', quantity: 15, rate: 275, amount: 4125 },
        { description: 'Executive Presentation & Report', quantity: 8, rate: 300, amount: 2400 }
      ],
      subtotal: 16375,
      tax: 1310,
      taxRate: 8,
      total: 17685,
      notes: 'This engagement included comprehensive strategic analysis, market research, and implementation roadmap development. All deliverables have been completed according to the agreed timeline and scope of work.',
      paymentTerms: 'Payment is due within 30 days of invoice date. Interest charges of 1.5% per month will be applied to overdue amounts. All consulting work is confidential and proprietary.'
    }
  },

  {
    id: 'service-business',
    name: 'Service Business',
    category: 'service',
    description: 'Versatile template suitable for various service-based businesses',
    thumbnail: '/images/templates/service-business-thumb.jpg',
    styling: {
      primaryColor: '#7c2d12',
      fontFamily: 'Arial, sans-serif',
      layout: 'service-focused'
    },
    htmlTemplate: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice {{invoiceNumber}}</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #fafaf9 0%, #f5f5f4 100%);
            padding: 40px 20px;
            line-height: 1.6;
          }
          .invoice-container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
          }
          .header-section {
            background: linear-gradient(135deg, #7c2d12 0%, #a16207 50%, #ea580c 100%);
            padding: 40px;
            color: white;
            position: relative;
          }
          .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><circle cx="30" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
          }
          .header-content {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .company-section {
            flex: 1;
          }
          .company-name {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 10px;
            letter-spacing: 1px;
          }
          .company-tagline {
            font-size: 16px;
            margin-bottom: 20px;
            opacity: 0.9;
            font-weight: 500;
          }
          .company-details {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.8;
          }
          .invoice-badge {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
          }
          .invoice-title {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
          }
          .invoice-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 15px;
          }
          .invoice-date {
            font-size: 14px;
            opacity: 0.9;
          }
          .main-content {
            padding: 40px;
          }
          .client-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
          }
          .info-card {
            background: linear-gradient(135deg, #fef7ed 0%, #fed7aa 100%);
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #fed7aa;
          }
          .info-title {
            font-size: 14px;
            font-weight: 700;
            color: #9a3412;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
          }
          .company-name-card, .client-name-card {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 12px;
          }
          .contact-details {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.6;
          }
          .contact-details .email {
            color: #ea580c;
            font-weight: 600;
          }
          .date-info {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #fca5a5;
            margin-bottom: 30px;
            text-align: center;
          }
          .date-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
          }
          .date-item {
            text-align: center;
          }
          .date-label {
            font-size: 12px;
            font-weight: 600;
            color: #991b1b;
            margin-bottom: 5px;
            text-transform: uppercase;
          }
          .date-value {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
          }
          .due-date {
            color: #dc2626;
          }
          .services-section {
            background: #fafaf9;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            border: 1px solid #e7e5e4;
          }
          .services-title {
            font-size: 24px;
            color: #7c2d12;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 700;
            position: relative;
          }
          .services-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(135deg, #7c2d12, #ea580c);
            border-radius: 2px;
          }
          .items-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .items-table th {
            background: linear-gradient(135deg, #7c2d12, #a16207);
            color: white;
            padding: 20px;
            text-align: left;
            font-weight: 700;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .items-table td {
            padding: 20px;
            border-bottom: 2px solid #f5f5f4;
            font-size: 15px;
          }
          .items-table tr:last-child td {
            border-bottom: none;
          }
          .items-table tr:hover {
            background: #fefefe;
          }
          .service-description {
            font-weight: 600;
            color: #1f2937;
          }
          .amount-cell {
            text-align: right;
            font-weight: 700;
            color: #1f2937;
          }
          .total-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 40px;
          }
          .total-container {
            width: 400px;
            background: white;
            border: 2px solid #e7e5e4;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            padding: 16px 25px;
            border-bottom: 1px solid #d6d3d1;
            font-size: 16px;
            background: #fafaf9;
          }
          .total-row:last-child {
            border-bottom: none;
          }
          .total-final {
            background: linear-gradient(135deg, #7c2d12, #a16207);
            color: white;
            font-size: 20px;
            font-weight: 800;
            padding: 20px 25px;
          }
          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
          }
          .info-panel {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
          }
          .info-panel.guarantee {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border-left-color: #3b82f6;
          }
          .panel-title {
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 8px;
            text-transform: uppercase;
          }
          .panel-title.terms { color: #047857; }
          .panel-title.guarantee { color: #1d4ed8; }
          .panel-text {
            font-size: 14px;
            line-height: 1.6;
          }
          .panel-text.terms { color: #065f46; }
          .panel-text.guarantee { color: #1e3a8a; }
          .notes-section {
            background: #fef7ed;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #fed7aa;
            margin-bottom: 30px;
          }
          .notes-title {
            font-size: 16px;
            font-weight: 700;
            color: #9a3412;
            margin-bottom: 12px;
          }
          .notes-content {
            color: #7c2d12;
            font-size: 14px;
            line-height: 1.7;
          }
          .footer-section {
            background: linear-gradient(135deg, #1f2937, #374151);
            color: white;
            padding: 30px;
            text-align: center;
          }
          .footer-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
          }
          .footer-text {
            font-size: 14px;
            opacity: 0.8;
          }
          @media print {
            body { background: white; padding: 0; }
            .invoice-container { 
              box-shadow: none; 
              border-radius: 0;
              max-width: none;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="header-section">
            <div class="header-content">
              <div class="company-section">
                <div class="company-name">{{companyName}}</div>
                <div class="company-tagline">Professional Service Excellence</div>
                <div class="company-details">
                  <div>{{companyAddress}}</div>
                  <div>{{companyPhone}} | {{companyEmail}}</div>
                </div>
              </div>
              <div class="invoice-badge">
                <div class="invoice-title">Invoice</div>
                <div class="invoice-number">{{invoiceNumber}}</div>
                <div class="invoice-date">{{invoiceDate}}</div>
              </div>
            </div>
          </div>
          
          <div class="main-content">
            <div class="client-section">
              <div class="info-card">
                <div class="info-title">Service Provider</div>
                <div class="company-name-card">{{companyName}}</div>
                <div class="contact-details">
                  <div>{{companyAddress}}</div>
                  <div>{{companyPhone}}</div>
                  <div class="email">{{companyEmail}}</div>
                </div>
              </div>
              
              <div class="info-card">
                <div class="info-title">Bill To</div>
                <div class="client-name-card">{{clientName}}</div>
                <div class="contact-details">
                  <div>{{clientAddress}}</div>
                  <div>{{clientPhone}}</div>
                  <div class="email">{{clientEmail}}</div>
                </div>
              </div>
            </div>
            
            <div class="date-info">
              <div class="date-grid">
                <div class="date-item">
                  <div class="date-label">Invoice Date</div>
                  <div class="date-value">{{invoiceDate}}</div>
                </div>
                <div class="date-item">
                  <div class="date-label">Payment Due</div>
                  <div class="date-value due-date">{{dueDate}}</div>
                </div>
              </div>
            </div>
            
            <div class="services-section">
              <div class="services-title">Services Provided</div>
              
              <table class="items-table">
                <thead>
                  <tr>
                    <th>Service Details</th>
                    <th style="text-align: center; width: 100px;">Qty</th>
                    <th style="text-align: right; width: 120px;">Rate</th>
                    <th style="text-align: right; width: 120px;">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {{#each lineItems}}
                  <tr>
                    <td class="service-description">{{description}}</td>
                    <td style="text-align: center;">{{quantity}}</td>
                    <td style="text-align: right;">\${{rate}}</td>
                    <td class="amount-cell">\${{amount}}</td>
                  </tr>
                  {{/each}}
                </tbody>
              </table>
            </div>
            
            <div class="total-section">
              <div class="total-container">
                <div class="total-row">
                  <span>Subtotal</span>
                  <span>\${{subtotal}}</span>
                </div>
                {{#if tax}}
                <div class="total-row">
                  <span>Tax ({{taxRate}}%)</span>
                  <span>\${{tax}}</span>
                </div>
                {{/if}}
                <div class="total-final">
                  <span>TOTAL DUE</span>
                  <span>\${{total}}</span>
                </div>
              </div>
            </div>
            
            <div class="info-grid">
              <div class="info-panel">
                <div class="panel-title terms">Payment Terms</div>
                <div class="panel-text terms">{{paymentTerms}}</div>
              </div>
              
              <div class="info-panel guarantee">
                <div class="panel-title guarantee">Service Guarantee</div>
                <div class="panel-text guarantee">100% satisfaction guaranteed on all services provided</div>
              </div>
            </div>
            
            {{#if notes}}
            <div class="notes-section">
              <div class="notes-title">Additional Information</div>
              <div class="notes-content">{{notes}}</div>
            </div>
            {{/if}}
          </div>
          
          <div class="footer-section">
            <div class="footer-title">Thank you for your business!</div>
            <div class="footer-text">We appreciate the opportunity to serve you</div>
          </div>
        </div>
      </body>
      </html>
    `,
    sampleData: {
      companyName: 'Premier Service Co.',
      companyAddress: '987 Service Street, Professional Plaza, Miami, FL 33101',
      companyPhone: '(*************',
      companyEmail: '<EMAIL>',
      invoiceNumber: 'PSC-2024-156',
      invoiceDate: '2024-12-10',
      dueDate: '2024-12-25',
      clientName: 'Residential Property LLC',
      clientAddress: '456 Property Lane, Homeowner Heights, FL 33102',
      clientPhone: '(*************',
      clientEmail: '<EMAIL>',
      lineItems: [
        { description: 'Complete HVAC System Maintenance', quantity: 1, rate: 350, amount: 350 },
        { description: 'Plumbing Inspection & Repairs', quantity: 1, rate: 280, amount: 280 },
        { description: 'Electrical Safety Inspection', quantity: 1, rate: 200, amount: 200 },
        { description: 'Emergency Service Call Fee', quantity: 1, rate: 75, amount: 75 }
      ],
      subtotal: 905,
      tax: 72.4,
      taxRate: 8,
      total: 977.4,
      notes: 'All services completed according to industry standards and local regulations. Warranty covers all parts and labor for 90 days. Emergency contact number available 24/7 for urgent service needs.',
      paymentTerms: 'Payment due within 15 days of service completion. We accept cash, check, and all major credit cards. Service warranty void if payment is not received within 30 days.'
    }
  }
];

export function getAllTemplates(): TemplateDefinition[] {
  return templateDefinitions;
}

export function getTemplateById(id: string): TemplateDefinition | undefined {
  return templateDefinitions.find(template => template.id === id);
}

export function getTemplatesByCategory(category: string): TemplateDefinition[] {
  return templateDefinitions.filter(template => template.category === category);
}