'use client'

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { trackEvent } from '@/lib/analytics-client';

interface OnboardingState {
  hasCompletedOnboarding: boolean;
  hasCreatedFirstInvoice: boolean;
  onboardingStep: string | null;
  lastOnboardingDate: Date | null;
}

export function useOnboarding() {
  const { data: session } = useSession();
  const [onboardingState, setOnboardingState] = useState<OnboardingState>({
    hasCompletedOnboarding: false,
    hasCreatedFirstInvoice: false,
    onboardingStep: null,
    lastOnboardingDate: null
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (session?.user?.id) {
      checkOnboardingStatus();
    }
  }, [session]);

  const checkOnboardingStatus = async () => {
    try {
      // Check localStorage first for quick access
      const localState = localStorage.getItem(`onboarding-${session?.user?.id}`);
      if (localState) {
        const parsed = JSON.parse(localState);
        setOnboardingState({
          ...parsed,
          lastOnboardingDate: parsed.lastOnboardingDate ? new Date(parsed.lastOnboardingDate) : null
        });
      }

      // Then verify with server
      const response = await fetch('/api/user/onboarding-status');
      if (response.ok) {
        const data = await response.json();
        const newState = {
          hasCompletedOnboarding: data.hasCompletedOnboarding || false,
          hasCreatedFirstInvoice: data.hasCreatedFirstInvoice || false,
          onboardingStep: data.onboardingStep || null,
          lastOnboardingDate: data.lastOnboardingDate ? new Date(data.lastOnboardingDate) : null
        };
        
        setOnboardingState(newState);
        localStorage.setItem(`onboarding-${session?.user?.id}`, JSON.stringify(newState));
      }
    } catch (error) {
      console.error('Error checking onboarding status:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateOnboardingStatus = async (updates: Partial<OnboardingState>) => {
    try {
      const newState = { ...onboardingState, ...updates };
      setOnboardingState(newState);
      
      // Update localStorage immediately
      localStorage.setItem(`onboarding-${session?.user?.id}`, JSON.stringify(newState));
      
      // Then update server
      await fetch('/api/user/onboarding-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      });

      // Track the update
      trackEvent('onboarding', 'update', { label: 'status_updated', ...updates });
    } catch (error) {
      console.error('Error updating onboarding status:', error);
    }
  };

  const completeOnboarding = async () => {
    await updateOnboardingStatus({
      hasCompletedOnboarding: true,
      lastOnboardingDate: new Date()
    });
  };

  const skipOnboarding = async () => {
    await updateOnboardingStatus({
      hasCompletedOnboarding: true,
      onboardingStep: 'skipped',
      lastOnboardingDate: new Date()
    });
  };

  const resetOnboarding = async () => {
    localStorage.removeItem(`onboarding-${session?.user?.id}`);
    setOnboardingState({
      hasCompletedOnboarding: false,
      hasCreatedFirstInvoice: false,
      onboardingStep: null,
      lastOnboardingDate: null
    });
    
    await fetch('/api/user/onboarding-status', {
      method: 'DELETE'
    });
  };

  const shouldShowOnboarding = () => {
    if (loading) return false;
    if (!session?.user) return false;
    
    // Show onboarding if user hasn't completed it and hasn't created an invoice
    return !onboardingState.hasCompletedOnboarding && !onboardingState.hasCreatedFirstInvoice;
  };

  const shouldShowFirstInvoiceTips = () => {
    if (loading) return false;
    if (!session?.user) return false;
    
    // Show tips if user completed onboarding but hasn't created first invoice
    return onboardingState.hasCompletedOnboarding && !onboardingState.hasCreatedFirstInvoice;
  };

  return {
    onboardingState,
    loading,
    shouldShowOnboarding,
    shouldShowFirstInvoiceTips,
    completeOnboarding,
    skipOnboarding,
    updateOnboardingStatus,
    resetOnboarding,
    checkOnboardingStatus
  };
}