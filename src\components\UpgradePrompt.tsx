'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface UpgradePromptProps {
  isOpen: boolean;
  onClose: () => void;
  remainingInvoices?: number;
  reason?: string;
  source?: string; // Track where the modal was triggered from
  variant?: 'limit' | 'upsell' | 'feature';
}

export default function UpgradePrompt({ 
  isOpen, 
  onClose, 
  remainingInvoices, 
  reason, 
  source = 'unknown',
  variant = 'limit' 
}: UpgradePromptProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [upgrading, setUpgrading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const isPro = session?.user?.subscription?.plan === 'pro';
  const invoicesUsed = session?.user?.subscription?.invoicesUsed || 0;

  useEffect(() => {
    if (isOpen && session?.user?.id) {
      // Track modal shown event
      trackModalShown();
    }
  }, [isOpen, session?.user?.id]);

  const trackModalShown = async () => {
    try {
      await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'upgrade_modal_shown',
          properties: {
            source,
            variant,
            remainingInvoices,
            invoicesUsed,
            reason,
          }
        })
      });
    } catch (error) {
      console.error('Error tracking modal shown:', error);
    }
  };

  const trackModalClosed = async (action: 'close' | 'upgrade' | 'pricing') => {
    try {
      await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: action === 'upgrade' ? 'upgrade_clicked' : 'upgrade_cancelled',
          properties: {
            source,
            variant,
            action,
            timeOnModal: Date.now(), // You could track time spent
          }
        })
      });
    } catch (error) {
      console.error('Error tracking modal action:', error);
    }
  };

  if (!isOpen) return null;

  const handleUpgrade = async () => {
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    setUpgrading(true);
    await trackModalClosed('upgrade');

    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          successUrl: `${window.location.origin}/dashboard?upgraded=true`,
          cancelUrl: window.location.href,
          source: `upgrade_modal_${source}`,
        }),
      });

      const data = await response.json();

      if (data.url) {
        window.location.href = data.url;
      } else {
        console.error('Failed to create checkout session');
        alert('Failed to start upgrade process. Please try again.');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setUpgrading(false);
    }
  };

  const handleViewPricing = async () => {
    await trackModalClosed('pricing');
    router.push('/pricing');
  };

  const handleClose = async () => {
    await trackModalClosed('close');
    onClose();
  };

  const getModalContent = () => {
    switch (variant) {
      case 'limit':
        return {
          icon: '🚫',
          title: 'Invoice Limit Reached',
          description: remainingInvoices === 0 
            ? 'You\'ve used all 3 invoices for this month. Upgrade to Pro for unlimited invoices.'
            : `You have ${remainingInvoices} invoice${remainingInvoices !== 1 ? 's' : ''} remaining this month.`,
          urgency: 'high'
        };
      case 'upsell':
        return {
          icon: '⚡',
          title: 'Unlock Your Potential',
          description: 'Ready to take your invoicing to the next level? Get unlimited invoices and premium features.',
          urgency: 'medium'
        };
      case 'feature':
        return {
          icon: '✨',
          title: 'Premium Feature',
          description: 'This feature is available with Pro. Upgrade now to access premium templates and advanced features.',
          urgency: 'low'
        };
      default:
        return {
          icon: '⚡',
          title: 'Upgrade to Pro',
          description: 'Get unlimited invoices and premium features.',
          urgency: 'medium'
        };
    }
  };

  const content = getModalContent();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4" onClick={handleClose}>
      <div 
        className="relative bg-white rounded-xl shadow-2xl max-w-md w-full mx-auto transform transition-all duration-300 scale-100" 
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="relative p-6 text-center">
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
          >
            ✕
          </button>
          
          <div className={`mx-auto flex items-center justify-center h-16 w-16 rounded-full mb-4 ${
            content.urgency === 'high' ? 'bg-red-100' :
            content.urgency === 'medium' ? 'bg-yellow-100' :
            'bg-blue-100'
          }`}>
            <span className="text-2xl">{content.icon}</span>
          </div>
          
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            {content.title}
          </h3>
          
          <p className="text-gray-600 text-sm leading-relaxed">
            {content.description}
          </p>
        </div>

        {/* Current Usage */}
        {variant === 'limit' && (
          <div className="px-6 mb-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Current usage</span>
                <span className="font-medium">{invoicesUsed}/3 invoices</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    invoicesUsed === 3 ? 'bg-red-500' : 
                    invoicesUsed === 2 ? 'bg-yellow-500' : 
                    'bg-blue-500'
                  }`}
                  style={{ width: `${(invoicesUsed / 3) * 100}%` }}
                />
              </div>
            </div>
          </div>
        )}

        {/* Value Proposition */}
        <div className="px-6 mb-6">
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-semibold text-gray-900">Pro Plan</h4>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">$9.99</div>
                <div className="text-xs text-gray-500">per month</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <span className="w-4 h-4 text-green-500 mr-2">✓</span>
                <span className="text-gray-700 font-medium">Unlimited invoices</span>
              </div>
              <div className="flex items-center text-sm">
                <span className="w-4 h-4 text-green-500 mr-2">✓</span>
                <span className="text-gray-700">Premium templates</span>
              </div>
              <div className="flex items-center text-sm">
                <span className="w-4 h-4 text-green-500 mr-2">✓</span>
                <span className="text-gray-700">Priority support</span>
              </div>
              
              {showDetails && (
                <>
                  <div className="flex items-center text-sm">
                    <span className="w-4 h-4 text-green-500 mr-2">✓</span>
                    <span className="text-gray-700">Advanced analytics</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="w-4 h-4 text-green-500 mr-2">✓</span>
                    <span className="text-gray-700">Custom branding</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="w-4 h-4 text-green-500 mr-2">✓</span>
                    <span className="text-gray-700">Export capabilities</span>
                  </div>
                </>
              )}
              
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
              >
                {showDetails ? 'Show less' : 'Show all features'}
              </button>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="px-6 pb-6 space-y-3">
          {!isPro && (
            <button
              onClick={handleUpgrade}
              disabled={upgrading}
              className={`w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:opacity-50 ${
                content.urgency === 'high'
                  ? 'bg-red-600 hover:bg-red-700 text-white shadow-lg hover:shadow-red-200'
                  : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-200'
              }`}
            >
              {upgrading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </div>
              ) : (
                'Upgrade to Pro Now'
              )}
            </button>
          )}
          
          <div className="flex space-x-2">
            <button
              onClick={handleViewPricing}
              className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
            >
              View Pricing
            </button>
            
            <button
              onClick={handleClose}
              className="flex-1 bg-white hover:bg-gray-50 text-gray-500 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors"
            >
              Maybe Later
            </button>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="px-6 pb-6">
          <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
            <span className="flex items-center">
              <span className="mr-1">🔒</span>
              Secure billing
            </span>
            <span className="flex items-center">
              <span className="mr-1">↩️</span>
              Cancel anytime
            </span>
            <span className="flex items-center">
              <span className="mr-1">📧</span>
              Email support
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Usage tracking component for dashboard
export function UsageIndicator() {
  const { data: session } = useSession();
  
  if (!session?.user?.subscription) return null;
  
  const { plan, invoicesUsed } = session.user.subscription;
  
  if (plan === 'pro') {
    return (
      <div className="bg-green-50 border border-green-200 rounded-md p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-5 h-5 text-green-400">✓</div>
          </div>
          <div className="ml-3">
            <p className="text-sm text-green-800">
              <strong>Pro Plan:</strong> Unlimited invoices
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  const remaining = Math.max(0, 3 - invoicesUsed);
  const percentage = (invoicesUsed / 3) * 100;
  
  return (
    <div className={`border rounded-md p-4 ${
      remaining === 0 ? 'bg-red-50 border-red-200' : 
      remaining === 1 ? 'bg-yellow-50 border-yellow-200' : 
      'bg-blue-50 border-blue-200'
    }`}>
      <div className="flex items-center justify-between mb-2">
        <p className={`text-sm font-medium ${
          remaining === 0 ? 'text-red-800' : 
          remaining === 1 ? 'text-yellow-800' : 
          'text-blue-800'
        }`}>
          Invoice Usage
        </p>
        <span className={`text-xs ${
          remaining === 0 ? 'text-red-600' : 
          remaining === 1 ? 'text-yellow-600' : 
          'text-blue-600'
        }`}>
          {invoicesUsed}/3 used
        </span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full ${
            remaining === 0 ? 'bg-red-500' : 
            remaining === 1 ? 'bg-yellow-500' : 
            'bg-blue-500'
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        ></div>
      </div>
      
      <p className={`text-xs mt-2 ${
        remaining === 0 ? 'text-red-600' : 
        remaining === 1 ? 'text-yellow-600' : 
        'text-blue-600'
      }`}>
        {remaining === 0 ? 
          'Limit reached. Upgrade to Pro for unlimited invoices.' :
          `${remaining} invoice${remaining !== 1 ? 's' : ''} remaining this month`
        }
      </p>
    </div>
  );
}