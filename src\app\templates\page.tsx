import type { Metadata } from 'next'
import { Suspense } from 'react'
import TemplatesContent from './TemplatesContent'

export const metadata: Metadata = {
  title: 'Invoice Templates - Professional Designs for Every Business',
  description: 'Choose from our collection of professional invoice templates. Modern, classic, and creative designs for freelancers, agencies, and businesses.',
}

export default function TemplatesPage() {
  return (
    <Suspense fallback={<TemplatesLoadingFallback />}>
      <TemplatesContent />
    </Suspense>
  )
}

function TemplatesLoadingFallback() {
  return (
    <main className="min-h-screen bg-gray-50">
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-16 text-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Invoice Templates
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Loading templates...
            </p>
          </div>
        </div>
      </section>
      <div className="min-h-[600px] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      </div>
    </main>
  )
}
