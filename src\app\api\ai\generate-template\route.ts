import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const requestData = await request.json();
    
    // Dynamically import to avoid bundling MongoDB for client
    const { generateInvoiceTemplate } = await import('@/lib/ai-template-generator');
    
    const template = await generateInvoiceTemplate(requestData);
    
    return NextResponse.json(template);

  } catch (error) {
    console.error('Error generating AI template:', error);
    return NextResponse.json(
      { error: 'Failed to generate template', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}