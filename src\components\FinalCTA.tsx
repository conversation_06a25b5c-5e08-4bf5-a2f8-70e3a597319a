'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Clock,
  TrendingUp,
  Users,
  Calendar,
  DollarSign,
  Gift,
  Sparkles,
  AlertCircle,
  CheckCircle,
  ArrowRight,
  X,
  FileText,
  Download,
  Shield,
  Phone
} from 'lucide-react'
import { Button } from '@/components/ui/Button'

export default function FinalCTA() {
  const [showExitIntent, setShowExitIntent] = useState(false)
  const [timeLeft, setTimeLeft] = useState({ hours: 47, minutes: 59, seconds: 59 })
  const [recentSignup, setRecentSignup] = useState({ name: '<PERSON>', location: 'Portland' })

  // Countdown timer
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 }
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 }
        }
        return prev
      })
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // Recent signup rotation
  useEffect(() => {
    const signups = [
      { name: 'Sarah', location: 'Portland' },
      { name: 'Mike', location: 'Austin' },
      { name: 'Emma', location: 'Chicago' },
      { name: 'David', location: 'Miami' },
      { name: 'Lisa', location: 'Seattle' }
    ]
    
    const interval = setInterval(() => {
      setRecentSignup(signups[Math.floor(Math.random() * signups.length)])
    }, 5000)
    
    return () => clearInterval(interval)
  }, [])

  // Exit intent detection
  useEffect(() => {
    const handleMouseLeave = (e: MouseEvent) => {
      if (e.clientY <= 0 && !showExitIntent) {
        setShowExitIntent(true)
      }
    }
    
    document.addEventListener('mouseleave', handleMouseLeave)
    return () => document.removeEventListener('mouseleave', handleMouseLeave)
  }, [showExitIntent])

  const savings = [
    { amount: '$12,400', description: 'recovered from old invoices' },
    { amount: '28 days', description: 'faster payment average' },
    { amount: '4 hours', description: 'saved per week' }
  ]

  const guarantees = [
    { icon: Shield, text: '60-day money-back guarantee' },
    { icon: X, text: 'Cancel anytime, keep your invoices' },
    { icon: FileText, text: 'Free migration from your current system' }
  ]

  return (
    <>
      <section className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <motion.div
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className="absolute top-20 left-20 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              x: [0, -100, 0],
              y: [0, 100, 0],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl"
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Urgency Banner */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-600 text-white rounded-lg p-4 mb-8 max-w-3xl mx-auto"
          >
            <div className="flex items-center justify-center gap-4">
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <p className="font-medium">
                Limited time offer ends in:{' '}
                <span className="font-mono font-bold">
                  {String(timeLeft.hours).padStart(2, '0')}:
                  {String(timeLeft.minutes).padStart(2, '0')}:
                  {String(timeLeft.seconds).padStart(2, '0')}
                </span>
              </p>
            </div>
          </motion.div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Stop losing money on{' '}
              <span className="text-red-400">late payments</span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-200 mb-8">
              Join thousands of professionals who got their{' '}
              <span className="font-semibold text-green-400">last late payment</span> this month
            </p>

            {/* Social Proof Ticker */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full mb-8"
            >
              <Users className="w-5 h-5 text-green-400" />
              <AnimatePresence mode="wait">
                <motion.span
                  key={recentSignup.name}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="text-white"
                >
                  {recentSignup.name} from {recentSignup.location} just created an account
                </motion.span>
              </AnimatePresence>
            </motion.div>

            {/* Savings Calculator */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 mb-12">
              <h3 className="text-2xl font-bold text-white mb-6">
                Your potential savings with our platform
              </h3>
              <div className="grid md:grid-cols-3 gap-6">
                {savings.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="text-center"
                  >
                    <p className="text-3xl font-bold text-green-400 mb-2">{item.amount}</p>
                    <p className="text-gray-300">{item.description}</p>
                  </motion.div>
                ))}
              </div>
              <p className="text-yellow-400 mt-6 font-medium">
                The average user recoups our annual cost in their first invoice
              </p>
            </div>

            {/* Special Offer */}
            <motion.div
              animate={{
                scale: [1, 1.02, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
              className="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl p-8 mb-12"
            >
              <div className="flex items-center justify-center gap-3 mb-4">
                <Gift className="w-8 h-8 text-white" />
                <h3 className="text-2xl font-bold text-white">This week only</h3>
              </div>
              <p className="text-3xl font-bold text-white mb-2">
                Get 2 months FREE on annual plans
              </p>
              <p className="text-white/90">
                That's $40 in savings + our 60-day guarantee
              </p>
            </motion.div>

            {/* Risk Reversal */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              {guarantees.map((guarantee, index) => {
                const Icon = guarantee.icon
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-3 justify-center text-white"
                  >
                    <Icon className="w-5 h-5 text-green-400" />
                    <span>{guarantee.text}</span>
                  </motion.div>
                )
              })}
            </div>

            {/* CTAs */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white text-lg px-8 py-6 shadow-2xl"
                >
                  <Sparkles className="w-5 h-5 mr-2" />
                  Start My Free Trial
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  size="lg"
                  variant="outline"
                  className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 text-lg px-8 py-6"
                >
                  <Calendar className="w-5 h-5 mr-2" />
                  Schedule 5-Minute Demo
                </Button>
              </motion.div>
            </div>

            {/* Low commitment option */}
            <div className="text-center">
              <Button
                variant="ghost"
                className="text-white/80 hover:text-white"
              >
                <Download className="w-4 h-4 mr-2" />
                Download Free Invoice Template
              </Button>
            </div>

            {/* Trust indicators */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="mt-12 pt-12 border-t border-white/20"
            >
              <div className="flex flex-wrap gap-8 justify-center text-white/60 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4" />
                  No credit card required
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  SSL encrypted
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  24/7 support
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  12,847 active users
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Exit Intent Popup */}
      <AnimatePresence>
        {showExitIntent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowExitIntent(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-8 max-w-lg w-full relative"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setShowExitIntent(false)}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>

              <div className="text-center">
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 0.5 }}
                  className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4"
                >
                  <AlertCircle className="w-10 h-10 text-red-600" />
                </motion.div>

                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Wait! Don't leave money on the table
                </h3>
                <p className="text-gray-600 mb-6">
                  Get an exclusive 30% discount on your first month - only available right now
                </p>

                <div className="bg-green-50 rounded-lg p-4 mb-6">
                  <p className="text-lg font-semibold text-green-800 mb-1">
                    Special one-time offer
                  </p>
                  <p className="text-3xl font-bold text-green-600">
                    30% OFF first month
                  </p>
                  <p className="text-sm text-gray-600 mt-2">
                    This offer expires when you leave this page
                  </p>
                </div>

                <Button
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                  size="lg"
                  onClick={() => setShowExitIntent(false)}
                >
                  Claim My 30% Discount
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>

                <p className="text-sm text-gray-500 mt-4">
                  No thanks, I don't want to save money
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}