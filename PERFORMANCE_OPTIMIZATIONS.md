# Performance Optimizations Guide

This document outlines the performance optimizations implemented in the Template Invoices application for Core Web Vitals and overall performance.

## Next.js Configuration Optimizations

### 1. Image Optimization
```javascript
images: {
  domains: ['templateinvoices.com', 'localhost'],
  formats: ['image/webp', 'image/avif'],
  minimumCacheTTL: 31536000, // 1 year
  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
}
```
- **WebP and AVIF formats**: Modern image formats with better compression
- **Long cache TTL**: Images cached for 1 year
- **Responsive sizes**: Optimized for various device sizes

### 2. Security Headers
```javascript
headers: [
  'X-Content-Type-Options: nosniff',
  'X-Frame-Options: DENY',
  'X-XSS-Protection: 1; mode=block',
  'Referrer-Policy: strict-origin-when-cross-origin',
  'Permissions-Policy: camera=(), microphone=(), geolocation=()',
]
```
- Protection against XSS, clickjacking, and MIME type sniffing
- Privacy-focused permissions policy

### 3. Caching Strategy
- **Static assets**: Cached for 1 year with immutable flag
- **HTML pages**: Cached for 1 hour with revalidation
- **API responses**: Custom caching based on endpoint

### 4. Bundle Optimization
```javascript
splitChunks: {
  chunks: 'all',
  cacheGroups: {
    vendor: { /* Third-party libraries */ },
    common: { /* Shared code */ },
    styles: { /* CSS files */ },
  }
}
```
- **Code splitting**: Separate vendor and common chunks
- **Tree shaking**: Remove unused code
- **CSS optimization**: Separate CSS bundle

## Core Web Vitals Monitoring

### WebVitals Component
The `WebVitals` component tracks:
- **LCP** (Largest Contentful Paint): < 2.5s
- **FID** (First Input Delay): < 100ms
- **CLS** (Cumulative Layout Shift): < 0.1
- **FCP** (First Contentful Paint): < 1.8s
- **INP** (Interaction to Next Paint): < 200ms
- **TTFB** (Time to First Byte): < 800ms

### Analytics Integration
```javascript
window.gtag('event', 'web_vitals', {
  event_category: 'Web Vitals',
  event_label: metric.name,
  value: metric.value,
  metric_rating: metric.rating,
});
```

## Performance Best Practices

### 1. Font Optimization
```javascript
const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true
})
```
- **Font display swap**: Prevent invisible text during font load
- **Subset loading**: Load only required characters
- **Preloading**: Load fonts early in the process

### 2. Lazy Loading
- Images using Next.js Image component
- Route-based code splitting
- Dynamic imports for heavy components

### 3. Compression
- **Gzip/Brotli**: Enabled for all text-based assets
- **Image compression**: Automatic optimization via Next.js

### 4. Resource Hints
```html
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="dns-prefetch" href="https://www.googletagmanager.com" />
```

## Monitoring & Testing

### Tools for Testing
1. **Google PageSpeed Insights**: https://pagespeed.web.dev/
2. **Chrome DevTools Lighthouse**: Built-in performance audit
3. **WebPageTest**: https://www.webpagetest.org/
4. **Core Web Vitals Chrome Extension**: Real-user monitoring

### Performance Budget
- **LCP**: < 2.5s (Good), < 4s (Needs Improvement)
- **FID**: < 100ms (Good), < 300ms (Needs Improvement)
- **CLS**: < 0.1 (Good), < 0.25 (Needs Improvement)
- **Bundle Size**: < 200KB for initial JS
- **Page Weight**: < 1MB total

## Deployment Considerations

### Vercel Optimizations
- **Edge Network**: Global CDN distribution
- **Image Optimization**: Automatic via Vercel
- **Analytics**: Built-in Web Analytics

### Environment Variables
```bash
NEXT_PUBLIC_BASE_URL=https://templateinvoices.com
```

## Future Optimizations

1. **Service Worker**: For offline support and caching
2. **Resource Prioritization**: Critical CSS inlining
3. **Prefetching**: Smart link prefetching
4. **Database Query Optimization**: Indexed queries and caching
5. **Edge Functions**: For faster API responses

## Performance Checklist

- [ ] Images optimized and using modern formats
- [ ] Fonts loaded with `display: swap`
- [ ] JavaScript bundle < 200KB
- [ ] Core Web Vitals in "Good" range
- [ ] Security headers configured
- [ ] Caching headers optimized
- [ ] Code splitting implemented
- [ ] Unnecessary dependencies removed
- [ ] Production build tested
- [ ] Performance monitoring active