/**
 * MongoDB fallback utility for client-side components
 * This helps with development when MongoDB is not available
 */

import { mockDashboardData } from '../app/mock-data/dashboard-data';

/**
 * Fetch dashboard data with fallback to mock data when MongoDB is unavailable
 * @param period The time period for dashboard data (month, year, all)
 * @returns Dashboard data from API or mock data if the API fails
 */
export async function fetchDashboardDataWithFallback(period: string = 'month') {
  try {
    // First try to get real data from the API
    const response = await fetch(`/api/analytics/user-dashboard?period=${period}`, {
      cache: 'no-store',
    });
    
    if (!response.ok) {
      console.warn('Dashboard API returned an error, using mock data instead');
      return mockDashboardData;
    }
    
    return await response.json();
  } catch (error) {
    // If there's any error (including MongoDB connection issues), use mock data
    console.warn('Error fetching dashboard data, using mock data instead:', error);
    return mockDashboardData;
  }
}

/**
 * Determines if mock data should be used based on environment and settings
 * @returns Boolean indicating if mock data should be allowed
 */
export function allowMockData(): boolean {
  // Allow mock data in development or when explicitly enabled in .env
  return process.env.NODE_ENV === 'development' || 
         process.env.NEXT_PUBLIC_ALLOW_MOCK_DATA === 'true';
}
