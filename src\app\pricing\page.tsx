'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

export default function PricingPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [upgrading, setUpgrading] = useState(false);
  const [loading, setLoading] = useState(false);

  const faqStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: [
      {
        '@type': 'Question',
        name: 'How many invoices can I create for free?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'You can create up to 3 invoices per month with our free plan. Upgrade to Pro for unlimited invoices.',
        },
      },
      {
        '@type': 'Question',
        name: 'Can I customize the invoice templates?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes, all templates are fully customizable. You can add your logo, change colors, and modify all content.',
        },
      },
      {
        '@type': 'Question',
        name: 'Can I cancel anytime?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes, you can cancel your subscription at any time. You\'ll keep access until the end of your billing period.',
        },
      },
      {
        '@type': 'Question',
        name: 'What happens when I reach the free limit?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'You\'ll be prompted to upgrade to Pro to create more invoices. Your existing invoices remain accessible.',
        },
      },
      {
        '@type': 'Question',
        name: 'Is my data secure?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes, all data is encrypted and securely stored. We never share your information with third parties.',
        },
      },
    ],
  };

  const isPro = session?.user?.subscription?.plan === 'pro';
  const invoicesUsed = session?.user?.subscription?.invoicesUsed || 0;

  const handleUpgrade = async () => {
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    setUpgrading(true);
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          successUrl: `${window.location.origin}/dashboard?upgraded=true`,
          cancelUrl: `${window.location.origin}/pricing`,
        }),
      });

      const data = await response.json();

      if (data.url) {
        window.location.href = data.url;
      } else {
        alert('Failed to start checkout. Please try again.');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setUpgrading(false);
    }
  };

  const handleManageSubscription = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/stripe/portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          returnUrl: `${window.location.origin}/pricing`,
        }),
      });

      const data = await response.json();

      if (data.url) {
        window.location.href = data.url;
      } else {
        alert('Failed to access billing portal. Please try again.');
      }
    } catch (error) {
      console.error('Error accessing billing portal:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />
      <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Simple Pricing
          </h1>
          <p className="mt-4 text-xl text-gray-600">
            Start free, upgrade when you need more
          </p>
        </div>

        {/* Current Status */}
        {session?.user && (
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                  isPro ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                }`}>
                  {isPro ? 'Pro' : 'Free'}
                </span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-800">
                  You're currently on the <strong>{isPro ? 'Pro' : 'Free'}</strong> plan.
                  {!isPro && (
                    <span> You have {3 - invoicesUsed} invoices remaining this month.</span>
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Simple Plan Comparison */}
        <div className="mt-12 bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="px-6 py-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900">Upgrade to Pro</h2>
            <p className="mt-2 text-gray-600">Get unlimited invoices and premium features</p>
            
            <div className="mt-8">
              <span className="text-4xl font-extrabold text-gray-900">$9.99</span>
              <span className="text-xl font-medium text-gray-500">/month</span>
            </div>

            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Free Plan */}
              <div className="text-left">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Free Plan</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                    <span className="text-sm text-gray-600">3 invoices per month</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                    <span className="text-sm text-gray-600">Basic templates</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                    <span className="text-sm text-gray-600">PDF generation</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-gray-300 mr-3">✗</span>
                    <span className="text-sm text-gray-400">Unlimited invoices</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-gray-300 mr-3">✗</span>
                    <span className="text-sm text-gray-400">Premium templates</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-gray-300 mr-3">✗</span>
                    <span className="text-sm text-gray-400">Priority support</span>
                  </li>
                </ul>
              </div>

              {/* Pro Plan */}
              <div className="text-left">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Pro Plan</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                    <span className="text-sm text-gray-600 font-medium">Unlimited invoices</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                    <span className="text-sm text-gray-600">All templates</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                    <span className="text-sm text-gray-600">PDF generation</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                    <span className="text-sm text-gray-600">Custom templates</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                    <span className="text-sm text-gray-600">Priority support</span>
                  </li>
                  <li className="flex items-center">
                    <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                    <span className="text-sm text-gray-600">Advanced analytics</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Action Button */}
            <div className="mt-8">
              {!isPro ? (
                <button
                  onClick={handleUpgrade}
                  disabled={upgrading}
                  className="w-full md:w-auto bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {upgrading ? 'Processing...' : 'Upgrade to Pro'}
                </button>
              ) : (
                <div className="space-y-4">
                  <p className="text-green-600 font-medium">✅ You're already on Pro!</p>
                  <button
                    onClick={handleManageSubscription}
                    disabled={loading}
                    className="w-full md:w-auto bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-8 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'Loading...' : 'Manage Subscription'}
                  </button>
                </div>
              )}
            </div>

            {/* Features */}
            <div className="mt-8 text-center">
              <p className="text-sm text-gray-500">
                ✨ Cancel anytime • 💳 Secure billing • 📧 Email support
              </p>
            </div>
          </div>
        </div>

        {/* FAQ */}
        <div className="mt-12">
          <h3 className="text-lg font-semibold text-gray-900 text-center mb-8">Frequently Asked Questions</h3>
          <div className="space-y-6 max-w-2xl mx-auto">
            <div>
              <h4 className="font-medium text-gray-900">Can I cancel anytime?</h4>
              <p className="text-sm text-gray-600 mt-1">Yes, you can cancel your subscription at any time. You'll keep access until the end of your billing period.</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">What happens when I reach the free limit?</h4>
              <p className="text-sm text-gray-600 mt-1">You'll be prompted to upgrade to Pro to create more invoices. Your existing invoices remain accessible.</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Is my data secure?</h4>
              <p className="text-sm text-gray-600 mt-1">Yes, all data is encrypted and securely stored. We never share your information with third parties.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}