/**
 * Test Script for Invoice Limits System
 * 
 * Run this script to test the invoice limits functionality:
 * npx ts-node test-invoice-limits.ts
 */

import { ObjectId } from 'mongodb';
import {
  checkInvoiceLimit,
  incrementInvoiceCount,
  resetMonthlyCount,
  canCreateInvoice,
  getUsageStatistics,
  INVOICE_LIMITS
} from './src/lib/invoice-limits';

// Mock user ID for testing
const testUserId = new ObjectId('507f1f77bcf86cd799439011');

async function testInvoiceLimits() {
  console.log('🧪 Testing Invoice Limits System\n');
  
  try {
    // Test 1: Check initial limits
    console.log('📊 Test 1: Checking initial invoice limits...');
    const initialUsage = await checkInvoiceLimit(testUserId);
    console.log('Initial usage:', initialUsage);
    console.log(`- Plan: ${initialUsage.plan}`);
    console.log(`- Used: ${initialUsage.used}/${initialUsage.limit}`);
    console.log(`- Remaining: ${initialUsage.remaining}`);
    console.log(`- Should show warning: ${initialUsage.shouldShowWarning}`);
    console.log(`- Should show upgrade: ${initialUsage.shouldShowUpgrade}\n`);
    
    // Test 2: Can create invoice check
    console.log('✅ Test 2: Checking if user can create invoice...');
    const canCreateResult = await canCreateInvoice(testUserId);
    console.log('Can create invoice:', canCreateResult);
    console.log(`- Allowed: ${canCreateResult.canCreate}`);
    console.log(`- Reason: ${canCreateResult.reason || 'No restrictions'}`);
    console.log(`- Remaining: ${canCreateResult.remainingInvoices}\n`);
    
    // Test 3: Increment invoice count
    console.log('📝 Test 3: Creating invoices to test limits...');
    for (let i = 1; i <= 4; i++) {
      console.log(`\nAttempting to create invoice #${i}...`);
      
      const beforeCreate = await canCreateInvoice(testUserId);
      if (beforeCreate.canCreate) {
        const newCount = await incrementInvoiceCount(testUserId);
        console.log(`✅ Invoice #${i} created. Total used: ${newCount}`);
        
        if (beforeCreate.showUpgradePrompt) {
          console.log(`⚠️  Warning: ${beforeCreate.upgradeMessage}`);
        }
      } else {
        console.log(`❌ Cannot create invoice #${i}`);
        console.log(`   Reason: ${beforeCreate.reason}`);
        console.log(`   Upgrade required: ${beforeCreate.showUpgradePrompt}`);
        break;
      }
    }
    
    // Test 4: Get usage statistics
    console.log('\n📈 Test 4: Getting usage statistics...');
    const stats = await getUsageStatistics(testUserId);
    console.log('Usage Statistics:');
    console.log('- Current Month:', stats.currentMonth);
    console.log('- Lifetime:', stats.lifetime);
    console.log('- Subscription:', stats.subscription);
    
    // Test 5: Reset monthly usage
    console.log('\n🔄 Test 5: Testing monthly reset...');
    const resetSuccess = await resetMonthlyCount(testUserId);
    console.log(`Monthly reset: ${resetSuccess ? 'Success' : 'Failed'}`);
    
    if (resetSuccess) {
      const afterReset = await checkInvoiceLimit(testUserId);
      console.log(`Usage after reset: ${afterReset.used}/${afterReset.limit}`);
    }
    
    // Test 6: Constants verification
    console.log('\n🔢 Test 6: Invoice Limits Constants');
    console.log('- Free tier limit:', INVOICE_LIMITS.FREE_TIER_LIMIT);
    console.log('- Pro tier limit:', INVOICE_LIMITS.PRO_TIER_LIMIT === Infinity ? 'Unlimited' : INVOICE_LIMITS.PRO_TIER_LIMIT);
    console.log('- Grace period days:', INVOICE_LIMITS.GRACE_PERIOD_DAYS);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
  
  console.log('\n✅ Invoice limits testing complete!');
}

// Run tests if executed directly
if (require.main === module) {
  testInvoiceLimits()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

export { testInvoiceLimits };