#!/bin/bash

echo "GitHub Personal Access Token Setup"
echo "=================================="
echo ""
echo "To push to GitHub, you need to create a personal access token:"
echo ""
echo "1. Go to: https://github.com/settings/tokens/new"
echo "2. Give your token a descriptive name (e.g., 'template-invoice-dev')"
echo "3. Set expiration (recommend 90 days for security)"
echo "4. Select scopes:"
echo "   - repo (Full control of private repositories)"
echo "   - workflow (Update GitHub Action workflows)"
echo "5. Click 'Generate token'"
echo "6. Copy the token (it starts with 'ghp_')"
echo ""
echo "Once you have your token, run:"
echo ""
echo "git push https://YOUR_GITHUB_USERNAME:<EMAIL>/DuncanFly/template-invoices.git"
echo ""
echo "Or set it up permanently with:"
echo "git config --global credential.helper store"
echo "Then use your token as the password when prompted"