// Invoice Service for Template Invoice System
import { ObjectId } from 'mongodb';
import clientPromise from './mongodb';
import { 
  InvoiceDocument, 
  CreateInvoiceInput, 
  UpdateInvoiceInput, 
  COLLECTIONS,
  ClientDocument 
} from './models';
import { logActivity } from './user-service';
import { incrementInvoiceCount } from './invoice-limits';

// Get database instance
async function getDatabase() {
  const client = await clientPromise;
  return client.db();
}

// Generate unique invoice number
export async function generateInvoiceNumber(userId: string | ObjectId): Promise<string> {
  const db = await getDatabase();
  const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Get user's invoice count
  const count = await invoicesCollection.countDocuments({ userId: objectId });
  
  // Generate number: INV-YYYY-XXXX (year + sequential number)
  const year = new Date().getFullYear();
  const sequential = (count + 1).toString().padStart(4, '0');
  
  return `INV-${year}-${sequential}`;
}

// Create invoice
export async function createInvoice(input: CreateInvoiceInput): Promise<InvoiceDocument> {
  const db = await getDatabase();
  const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
  
  const now = new Date();
  
  // Generate invoice number if not provided
  if (!input.invoiceNumber) {
    input.invoiceNumber = await generateInvoiceNumber(input.userId);
  }
  
  const invoice: InvoiceDocument = {
    ...input,
    createdAt: now,
    updatedAt: now,
    currency: input.currency || 'USD'
  };
  
  const result = await invoicesCollection.insertOne(invoice);
  
  // Increment user's invoice count (for freemium tracking)
  await incrementInvoiceCount(input.userId);
  
  // Update client's invoice history
  await updateClientInvoiceHistory(input.userId, input.clientInfo, invoice.totals.total);
  
  // Log activity
  await logActivity({
    userId: input.userId,
    action: 'invoice_created',
    resourceType: 'invoice',
    resourceId: result.insertedId,
    details: {
      description: `Invoice ${invoice.invoiceNumber} created`,
      metadata: {
        invoiceNumber: invoice.invoiceNumber,
        total: invoice.totals.total,
        currency: invoice.currency
      }
    }
  });
  
  return { ...invoice, _id: result.insertedId };
}

// Update invoice
export async function updateInvoice(invoiceId: string | ObjectId, userId: string | ObjectId, updates: UpdateInvoiceInput): Promise<InvoiceDocument | null> {
  const db = await getDatabase();
  const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
  
  const invoiceObjectId = typeof invoiceId === 'string' ? new ObjectId(invoiceId) : invoiceId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const result = await invoicesCollection.findOneAndUpdate(
    { _id: invoiceObjectId, userId: userObjectId },
    { 
      $set: { 
        ...updates,
        updatedAt: new Date()
      }
    },
    { returnDocument: 'after' }
  );
  
  if (result?.value) {
    // Log activity
    await logActivity({
      userId: userObjectId,
      action: 'invoice_updated',
      resourceType: 'invoice',
      resourceId: invoiceObjectId,
      details: {
        description: `Invoice ${result.value.invoiceNumber || 'Unknown'} updated`,
        metadata: { updatedFields: Object.keys(updates) }
      }
    });
  }
  
  return result?.value || null;
}

// Get invoice by ID
export async function getInvoiceById(invoiceId: string | ObjectId, userId: string | ObjectId): Promise<InvoiceDocument | null> {
  const db = await getDatabase();
  const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
  
  const invoiceObjectId = typeof invoiceId === 'string' ? new ObjectId(invoiceId) : invoiceId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  return await invoicesCollection.findOne({ _id: invoiceObjectId, userId: userObjectId });
}

// Get user's invoices with pagination and filters
export async function getUserInvoices(
  userId: string | ObjectId, 
  options: {
    page?: number;
    limit?: number;
    status?: InvoiceDocument['status'];
    search?: string;
    sortBy?: 'createdAt' | 'dueDate' | 'total' | 'invoiceNumber';
    sortOrder?: 'asc' | 'desc';
  } = {}
): Promise<{ invoices: InvoiceDocument[]; total: number; hasMore: boolean }> {
  const db = await getDatabase();
  const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const {
    page = 1,
    limit = 20,
    status,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = options;
  
  const skip = (page - 1) * limit;
  
  // Build query
  const query: any = { userId: userObjectId };
  
  if (status) {
    query.status = status;
  }
  
  if (search) {
    query.$or = [
      { invoiceNumber: { $regex: search, $options: 'i' } },
      { 'clientInfo.name': { $regex: search, $options: 'i' } },
      { 'businessInfo.name': { $regex: search, $options: 'i' } }
    ];
  }
  
  // Build sort
  const sort: any = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
  
  // Execute queries
  const [invoices, total] = await Promise.all([
    invoicesCollection
      .find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .toArray(),
    invoicesCollection.countDocuments(query)
  ]);
  
  const hasMore = skip + invoices.length < total;
  
  return { invoices, total, hasMore };
}

// Mark invoice as sent
export async function markInvoiceAsSent(invoiceId: string | ObjectId, userId: string | ObjectId): Promise<InvoiceDocument | null> {
  const result = await updateInvoice(invoiceId, userId, {
    status: 'sent',
    sentAt: new Date()
  });
  
  if (result) {
    await logActivity({
      userId: typeof userId === 'string' ? new ObjectId(userId) : userId,
      action: 'invoice_sent',
      resourceType: 'invoice',
      resourceId: typeof invoiceId === 'string' ? new ObjectId(invoiceId) : invoiceId,
      details: {
        description: `Invoice ${result.invoiceNumber || 'Unknown'} marked as sent`
      }
    });
  }
  
  return result;
}

// Mark invoice as paid
export async function markInvoiceAsPaid(invoiceId: string | ObjectId, userId: string | ObjectId, paidAt?: Date): Promise<InvoiceDocument | null> {
  const result = await updateInvoice(invoiceId, userId, {
    status: 'paid',
    paidAt: paidAt || new Date()
  });
  
  if (result) {
    await logActivity({
      userId: typeof userId === 'string' ? new ObjectId(userId) : userId,
      action: 'invoice_paid',
      resourceType: 'invoice',
      resourceId: typeof invoiceId === 'string' ? new ObjectId(invoiceId) : invoiceId,
      details: {
        description: `Invoice ${result.invoiceNumber || 'Unknown'} marked as paid`,
        metadata: {
          amount: result.totals?.total || 0,
          currency: result.currency || 'USD'
        }
      }
    });
  }
  
  return result;
}

// Delete invoice
export async function deleteInvoice(invoiceId: string | ObjectId, userId: string | ObjectId): Promise<boolean> {
  const db = await getDatabase();
  const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
  
  const invoiceObjectId = typeof invoiceId === 'string' ? new ObjectId(invoiceId) : invoiceId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Get invoice details for logging
  const invoice = await invoicesCollection.findOne({ _id: invoiceObjectId, userId: userObjectId });
  
  if (!invoice) {
    return false;
  }
  
  const result = await invoicesCollection.deleteOne({ _id: invoiceObjectId, userId: userObjectId });
  
  if (result.deletedCount > 0) {
    await logActivity({
      userId: userObjectId,
      action: 'invoice_deleted',
      resourceType: 'invoice',
      details: {
        description: `Invoice ${invoice.invoiceNumber} deleted`,
        metadata: {
          invoiceNumber: invoice.invoiceNumber,
          total: invoice.totals.total
        }
      }
    });
    return true;
  }
  
  return false;
}

// Get invoice statistics for user
export async function getInvoiceStatistics(userId: string | ObjectId) {
  const db = await getDatabase();
  const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const stats = await invoicesCollection.aggregate([
    { $match: { userId: userObjectId } },
    {
      $group: {
        _id: null,
        totalInvoices: { $sum: 1 },
        totalAmount: { $sum: '$totals.total' },
        paidAmount: {
          $sum: {
            $cond: [{ $eq: ['$status', 'paid'] }, '$totals.total', 0]
          }
        },
        pendingAmount: {
          $sum: {
            $cond: [{ $in: ['$status', ['draft', 'sent']] }, '$totals.total', 0]
          }
        },
        overdueAmount: {
          $sum: {
            $cond: [{ $eq: ['$status', 'overdue'] }, '$totals.total', 0]
          }
        },
        statusCounts: {
          $push: '$status'
        }
      }
    },
    {
      $addFields: {
        statusBreakdown: {
          $arrayToObject: {
            $map: {
              input: [
                { k: 'draft', v: { $size: { $filter: { input: '$statusCounts', cond: { $eq: ['$$this', 'draft'] } } } } },
                { k: 'sent', v: { $size: { $filter: { input: '$statusCounts', cond: { $eq: ['$$this', 'sent'] } } } } },
                { k: 'paid', v: { $size: { $filter: { input: '$statusCounts', cond: { $eq: ['$$this', 'paid'] } } } } },
                { k: 'overdue', v: { $size: { $filter: { input: '$statusCounts', cond: { $eq: ['$$this', 'overdue'] } } } } },
                { k: 'cancelled', v: { $size: { $filter: { input: '$statusCounts', cond: { $eq: ['$$this', 'cancelled'] } } } } }
              ],
              as: { k: '$$this.k', v: '$$this.v' }
            }
          }
        }
      }
    }
  ]).toArray();
  
  return stats[0] || {
    totalInvoices: 0,
    totalAmount: 0,
    paidAmount: 0,
    pendingAmount: 0,
    overdueAmount: 0,
    statusBreakdown: {
      draft: 0,
      sent: 0,
      paid: 0,
      overdue: 0,
      cancelled: 0
    }
  };
}

// Update client invoice history
async function updateClientInvoiceHistory(userId: string | ObjectId, clientInfo: InvoiceDocument['clientInfo'], invoiceTotal: number): Promise<void> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Find or create client
  let client = await clientsCollection.findOne({
    userId: userObjectId,
    $or: [
      { email: clientInfo.email },
      { name: clientInfo.name }
    ]
  });
  
  if (client) {
    // Update existing client
    const newTotal = client.totalInvoiced + invoiceTotal;
    const newCount = client.invoiceCount + 1;
    const newAverage = newTotal / newCount;
    
    await clientsCollection.updateOne(
      { _id: client._id },
      {
        $set: {
          lastInvoiceDate: new Date(),
          totalInvoiced: newTotal,
          invoiceCount: newCount,
          averageInvoiceAmount: newAverage,
          updatedAt: new Date(),
          // Update contact info if changed
          name: clientInfo.name,
          email: clientInfo.email,
          address: clientInfo.address,
          city: clientInfo.city,
          contactPerson: clientInfo.contactPerson
        }
      }
    );
  } else {
    // Create new client
    const newClient: Omit<ClientDocument, '_id'> = {
      userId: userObjectId,
      name: clientInfo.name,
      email: clientInfo.email,
      address: clientInfo.address,
      city: clientInfo.city,
      contactPerson: clientInfo.contactPerson,
      lastInvoiceDate: new Date(),
      totalInvoiced: invoiceTotal,
      invoiceCount: 1,
      averageInvoiceAmount: invoiceTotal,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await clientsCollection.insertOne(newClient);
  }
}

// Get recent invoices
export async function getRecentInvoices(userId: string | ObjectId, limit: number = 5): Promise<InvoiceDocument[]> {
  const db = await getDatabase();
  const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  return await invoicesCollection
    .find({ userId: userObjectId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .toArray();
}

// Check for overdue invoices and update status
export async function updateOverdueInvoices(): Promise<void> {
  const db = await getDatabase();
  const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
  
  const now = new Date();
  
  await invoicesCollection.updateMany(
    {
      status: 'sent',
      dueDate: { $lt: now }
    },
    {
      $set: { status: 'overdue' }
    }
  );
}