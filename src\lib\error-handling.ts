// Comprehensive Error Handling for Template Invoice System
import { NextResponse } from 'next/server';

// Error types
export enum ErrorType {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  RATE_LIMIT = 'rate_limit',
  EXTERNAL_SERVICE = 'external_service',
  DATABASE = 'database',
  FILE_PROCESSING = 'file_processing',
  PAYMENT = 'payment',
  EMAIL = 'email',
  PDF_GENERATION = 'pdf_generation',
  AI_SERVICE = 'ai_service',
  NETWORK = 'network',
  INTERNAL = 'internal'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Custom error class
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly statusCode: number;
  public readonly userMessage: string;
  public readonly details?: any;
  public readonly retryable: boolean;
  public readonly timestamp: Date;

  constructor(
    message: string,
    type: ErrorType,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    statusCode: number = 500,
    userMessage?: string,
    details?: any,
    retryable: boolean = false
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.severity = severity;
    this.statusCode = statusCode;
    this.userMessage = userMessage || this.getDefaultUserMessage(type);
    this.details = details;
    this.retryable = retryable;
    this.timestamp = new Date();

    // Capture stack trace
    Error.captureStackTrace(this, AppError);
  }

  private getDefaultUserMessage(type: ErrorType): string {
    switch (type) {
      case ErrorType.VALIDATION:
        return 'Please check your input and try again.';
      case ErrorType.AUTHENTICATION:
        return 'Please sign in to continue.';
      case ErrorType.AUTHORIZATION:
        return 'You don\'t have permission to perform this action.';
      case ErrorType.NOT_FOUND:
        return 'The requested item was not found.';
      case ErrorType.RATE_LIMIT:
        return 'Too many requests. Please try again later.';
      case ErrorType.EXTERNAL_SERVICE:
        return 'External service is temporarily unavailable. Please try again.';
      case ErrorType.DATABASE:
        return 'Database error occurred. Please try again.';
      case ErrorType.FILE_PROCESSING:
        return 'File processing failed. Please try again.';
      case ErrorType.PAYMENT:
        return 'Payment processing failed. Please try again.';
      case ErrorType.EMAIL:
        return 'Email sending failed. Please try again.';
      case ErrorType.PDF_GENERATION:
        return 'PDF generation failed. Please try again.';
      case ErrorType.AI_SERVICE:
        return 'AI service is temporarily unavailable. Please try again.';
      case ErrorType.NETWORK:
        return 'Network error occurred. Please check your connection.';
      default:
        return 'Something went wrong. Please try again.';
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      type: this.type,
      severity: this.severity,
      statusCode: this.statusCode,
      userMessage: this.userMessage,
      details: this.details,
      retryable: this.retryable,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack
    };
  }
}

// Error factory functions
export const createValidationError = (
  message: string, 
  details?: any, 
  userMessage?: string
) => new AppError(
  message,
  ErrorType.VALIDATION,
  ErrorSeverity.LOW,
  400,
  userMessage,
  details
);

export const createAuthenticationError = (
  message: string = 'Authentication required',
  userMessage?: string
) => new AppError(
  message,
  ErrorType.AUTHENTICATION,
  ErrorSeverity.MEDIUM,
  401,
  userMessage
);

export const createAuthorizationError = (
  message: string = 'Insufficient permissions',
  userMessage?: string
) => new AppError(
  message,
  ErrorType.AUTHORIZATION,
  ErrorSeverity.MEDIUM,
  403,
  userMessage
);

export const createNotFoundError = (
  resource: string = 'Resource',
  userMessage?: string
) => new AppError(
  `${resource} not found`,
  ErrorType.NOT_FOUND,
  ErrorSeverity.LOW,
  404,
  userMessage
);

export const createRateLimitError = (
  message: string = 'Rate limit exceeded',
  retryAfter?: number
) => new AppError(
  message,
  ErrorType.RATE_LIMIT,
  ErrorSeverity.MEDIUM,
  429,
  'Too many requests. Please try again later.',
  { retryAfter },
  true
);

export const createExternalServiceError = (
  service: string,
  message: string,
  retryable: boolean = true
) => new AppError(
  `${service} error: ${message}`,
  ErrorType.EXTERNAL_SERVICE,
  ErrorSeverity.HIGH,
  503,
  `${service} is temporarily unavailable. Please try again.`,
  { service },
  retryable
);

export const createDatabaseError = (
  operation: string,
  message: string
) => new AppError(
  `Database ${operation} failed: ${message}`,
  ErrorType.DATABASE,
  ErrorSeverity.HIGH,
  500,
  'Database error occurred. Please try again.',
  { operation },
  true
);

export const createPDFGenerationError = (
  message: string,
  details?: any
) => new AppError(
  `PDF generation failed: ${message}`,
  ErrorType.PDF_GENERATION,
  ErrorSeverity.MEDIUM,
  500,
  'PDF generation failed. Please try again.',
  details,
  true
);

export const createEmailError = (
  message: string,
  details?: any
) => new AppError(
  `Email sending failed: ${message}`,
  ErrorType.EMAIL,
  ErrorSeverity.MEDIUM,
  500,
  'Email sending failed. Please try again.',
  details,
  true
);

export const createPaymentError = (
  message: string,
  details?: any
) => new AppError(
  `Payment processing failed: ${message}`,
  ErrorType.PAYMENT,
  ErrorSeverity.HIGH,
  500,
  'Payment processing failed. Please try again.',
  details,
  false
);

export const createAIServiceError = (
  message: string,
  details?: any
) => new AppError(
  `AI service error: ${message}`,
  ErrorType.AI_SERVICE,
  ErrorSeverity.MEDIUM,
  503,
  'AI service is temporarily unavailable. Please try again.',
  details,
  true
);

// Error handler for API routes
export function handleApiError(error: unknown): NextResponse {
  let appError: AppError;

  if (error instanceof AppError) {
    appError = error;
  } else if (error instanceof Error) {
    // Convert known errors
    if (error.message.includes('MongoDB') || error.message.includes('database')) {
      appError = createDatabaseError('operation', error.message);
    } else if (error.message.includes('PDF') || error.message.includes('puppeteer')) {
      appError = createPDFGenerationError(error.message);
    } else if (error.message.includes('email') || error.message.includes('SMTP')) {
      appError = createEmailError(error.message);
    } else if (error.message.includes('Stripe') || error.message.includes('payment')) {
      appError = createPaymentError(error.message);
    } else if (error.message.includes('AI') || error.message.includes('OpenAI')) {
      appError = createAIServiceError(error.message);
    } else {
      appError = new AppError(
        error.message,
        ErrorType.INTERNAL,
        ErrorSeverity.HIGH,
        500,
        'Something went wrong. Please try again.'
      );
    }
  } else {
    appError = new AppError(
      'Unknown error occurred',
      ErrorType.INTERNAL,
      ErrorSeverity.CRITICAL,
      500,
      'Something went wrong. Please try again.'
    );
  }

  // Log error (you could integrate with logging service here)
  logError(appError);

  // Return appropriate response
  return NextResponse.json(
    {
      error: {
        type: appError.type,
        message: appError.userMessage,
        retryable: appError.retryable,
        ...(process.env.NODE_ENV === 'development' && {
          details: appError.details,
          stack: appError.stack
        })
      }
    },
    { status: appError.statusCode }
  );
}

// Error logging function
function logError(error: AppError): void {
  const logData = {
    timestamp: error.timestamp.toISOString(),
    type: error.type,
    severity: error.severity,
    message: error.message,
    userMessage: error.userMessage,
    statusCode: error.statusCode,
    stack: error.stack,
    details: error.details
  };

  // Log based on severity
  switch (error.severity) {
    case ErrorSeverity.CRITICAL:
      console.error('[CRITICAL ERROR]', logData);
      // You could send to external monitoring service here
      break;
    case ErrorSeverity.HIGH:
      console.error('[HIGH ERROR]', logData);
      break;
    case ErrorSeverity.MEDIUM:
      console.warn('[MEDIUM ERROR]', logData);
      break;
    case ErrorSeverity.LOW:
      console.log('[LOW ERROR]', logData);
      break;
  }
}

// Retry mechanism for retryable operations
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  backoffFactor: number = 2,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      // Don't retry if it's not retryable
      if (error instanceof AppError && !error.retryable) {
        throw error;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(backoffFactor, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      console.log(`Retrying operation, attempt ${attempt + 1}/${maxRetries} in ${delay}ms`);
    }
  }

  throw lastError!;
}

// Circuit breaker pattern for external services
class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private threshold: number = 5,
    private resetTimeout: number = 60000
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw createExternalServiceError(
          'Circuit Breaker',
          'Service is temporarily unavailable',
          true
        );
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}

// Global circuit breaker instances
export const pdfCircuitBreaker = new CircuitBreaker(3, 30000);
export const emailCircuitBreaker = new CircuitBreaker(5, 60000);
export const aiCircuitBreaker = new CircuitBreaker(3, 60000);
export const paymentCircuitBreaker = new CircuitBreaker(2, 120000);

// Validation helpers
export function validateRequired(value: any, fieldName: string): void {
  if (value === undefined || value === null || value === '') {
    throw createValidationError(
      `${fieldName} is required`,
      { field: fieldName },
      `${fieldName} is required`
    );
  }
}

export function validateEmail(email: string): void {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw createValidationError(
      'Invalid email format',
      { email },
      'Please enter a valid email address'
    );
  }
}

export function validateObjectId(id: string, fieldName: string = 'ID'): void {
  if (!id || !/^[0-9a-fA-F]{24}$/.test(id)) {
    throw createValidationError(
      `Invalid ${fieldName} format`,
      { id, field: fieldName },
      `Invalid ${fieldName} format`
    );
  }
}

export function validatePositiveNumber(value: number, fieldName: string): void {
  if (typeof value !== 'number' || value <= 0) {
    throw createValidationError(
      `${fieldName} must be a positive number`,
      { value, field: fieldName },
      `${fieldName} must be a positive number`
    );
  }
}

// Error boundary for React components (if needed)
export function createErrorBoundary() {
  return class ErrorBoundary extends Error {
    constructor(message: string, public originalError?: Error) {
      super(message);
      this.name = 'ErrorBoundary';
    }
  };
}

// Performance monitoring helpers
export function measurePerformance<T>(
  operation: () => Promise<T>,
  operationName: string
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      console.log(`[PERFORMANCE] ${operationName}: ${duration.toFixed(2)}ms`);
      
      // Alert on slow operations
      if (duration > 5000) {
        console.warn(`[SLOW OPERATION] ${operationName} took ${duration.toFixed(2)}ms`);
      }
      
      resolve(result);
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      console.error(`[PERFORMANCE ERROR] ${operationName} failed after ${duration.toFixed(2)}ms:`, error);
      reject(error);
    }
  });
}