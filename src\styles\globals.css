@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .animate-blob {
    animation: blob 7s infinite;
  }
  
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  
  .animation-delay-4000 {
    animation-delay: 4s;
  }
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Swiper Custom Styles */
.template-swiper .swiper-pagination-bullet {
  background: #e5e7eb;
  opacity: 1;
}

.template-swiper .swiper-pagination-bullet-active {
  background: #000;
}

.template-swiper .swiper-button-next,
.template-swiper .swiper-button-prev {
  color: #000;
}

.template-swiper .swiper-button-next:after,
.template-swiper .swiper-button-prev:after {
  font-size: 24px;
}

/* Template Carousel Styles */
.template-carousel .swiper-pagination {
  bottom: 0 !important;
}

.template-carousel .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  opacity: 0.3;
  transition: all 0.3s ease;
}

.template-carousel .swiper-pagination-bullet-active {
  opacity: 1;
  width: 24px;
  border-radius: 4px;
  box-shadow: 0 0 0 3px rgba(252, 74, 26, 0.1);
}

.template-carousel .swiper-slide {
  height: auto;
}

.template-carousel .swiper-3d .swiper-slide-shadow-left,
.template-carousel .swiper-3d .swiper-slide-shadow-right {
  background-image: none;
}

/* Testimonial Slider */
.testimonial-swiper .swiper-pagination {
  bottom: -40px;
}

.testimonial-swiper .swiper-pagination-bullet {
  background: #e5e7eb;
  opacity: 1;
  width: 12px;
  height: 12px;
}

.testimonial-swiper .swiper-pagination-bullet-active {
  background: #000;
  width: 32px;
  border-radius: 6px;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}