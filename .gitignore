# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
node_modules/

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea
.vscode
*.swp
*.swo

# OS
Thumbs.db

# Project specific
/tmp
/temp
*.log
.cache
notes.db
.claude/
.npm
**/npm-cache/
CUserssbdinAppDataLocalAnthropicClaudeapp-0.8.1npm-cache/
C:\\Users\\<USER>\\AppData\\Local\\AnthropicClaude\\app-0.8.1\\npm-cache/
C:/Users/<USER>/AppData/Local/AnthropicClaude/app-0.8.1/npm-cache/*
