import type { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, CheckCircle, Users, Download, Clock, FileText, Zap, Shield, Globe, Star, TrendingUp } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Free Invoice Generator | Create Professional Invoices Online',
  description: 'Create professional invoices in minutes with our free AI-powered invoice generator. Perfect for freelancers, small businesses, and contractors. Download PDF invoices instantly.',
  keywords: 'free invoice generator, invoice template, create invoice online, professional invoices, freelancer invoice, small business invoicing',
  openGraph: {
    title: 'Free Invoice Generator | Create Professional Invoices Online',
    description: 'Create professional invoices in minutes with AI-powered templates. Free for freelancers and small businesses.',
    url: 'https://templateinvoices.com',
    images: [{ url: '/og-homepage.jpg', width: 1200, height: 630 }],
  },
};

export default function HomePage() {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: 'Template Invoices',
    description: 'Professional invoice generator with beautiful templates',
    url: 'https://templateinvoices.com',
    applicationCategory: 'BusinessApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '1250',
    },
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="w-full bg-yellow-100 text-yellow-900 text-center p-2 text-sm font-medium">
        🚀 This project is for sale.{" "}
        <a
          href="https://sideprojectors.com/project/63151/template-invoices-ai-invoice-generator"
          target="_blank"
          rel="noopener noreferrer"
          className="underline hover:text-yellow-700"
        >
          View the listing →
        </a>
      </div>
      <a
        href="https://www.sideprojectors.com/project/63151/template-invoices-ai-invoice-generator"
      >
        <img
          style={{position:'fixed',zIndex:1000,top:'-5px', right: '20px', border: 0}}
          src="https://www.sideprojectors.com/img/badges/badge_2_red.png"
          alt="Template Invoices AI Invoice Generator is for sale at @SideProjectors"
        />
      </a>
      {/* Hero Section with Video Background */}
      <section className='relative min-h-screen flex items-center justify-center overflow-hidden'>
        {/* Video Background */}
        <video
          autoPlay
          loop
          muted
          playsInline
          className='absolute inset-0 w-full h-full object-cover z-0'
        >
          <source src="/video-bg.mp4.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        
        {/* Overlay for better text readability */}
        <div className='absolute inset-0 bg-black/50 z-10' />
        
        {/* Hero Content */}
        <div className='relative z-20 text-center text-white px-4 sm:px-6 lg:px-8'>
          <div className='max-w-4xl mx-auto'>
            <h1 className='text-4xl sm:text-6xl font-bold mb-6'>
              Free Invoice Generator for
              <span className='text-blue-400'> Professional Businesses</span>
            </h1>
            <p className='text-xl sm:text-2xl mb-8 leading-relaxed max-w-3xl mx-auto'>
              Create professional invoices in minutes with AI-powered templates. 
              Perfect for freelancers, small businesses, contractors, and consultants. 
              Download PDF invoices instantly or send directly to clients.
            </p>
            
            <div className='flex flex-col sm:flex-row gap-4 justify-center mb-12'>
              <Link 
                href='/create'
                className='bg-white text-gray-900 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center'
              >
                Create Free Invoice <ArrowRight className='ml-2 h-5 w-5' />
              </Link>
              <Link 
                href='/templates'
                className='border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-900 transition-colors'
              >
                View Templates
              </Link>
            </div>
            
            {/* Trust Indicators */}
            <div className='flex flex-wrap justify-center items-center gap-8 text-sm text-white/80'>
              <div className='flex items-center gap-2'>
                <Users className='h-4 w-4' />
                <span>Trusted by 10,000+ professionals</span>
              </div>
              <div className='flex items-center gap-2'>
                <Download className='h-4 w-4' />
                <span>Instant PDF download</span>
              </div>
              <div className='flex items-center gap-2'>
                <Clock className='h-4 w-4' />
                <span>Create in under 2 minutes</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section with Long-tail Keywords */}
      <section className='py-20 bg-white'>
        <div className='container mx-auto px-4'>
          <h2 className='text-4xl font-bold text-center mb-4'>
            Everything You Need for Professional Invoicing
          </h2>
          <p className='text-xl text-gray-600 text-center mb-16 max-w-3xl mx-auto'>
            Our free invoice generator includes all the features you need to create, 
            send, and track professional invoices for your business.
          </p>
          
          <div className='grid md:grid-cols-3 gap-8'>
            <div className='text-center p-6'>
              <div className='bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Zap className='h-8 w-8 text-blue-600' />
              </div>
              <h3 className='text-xl font-semibold mb-3'>AI-Powered Templates</h3>
              <p className='text-gray-600'>
                Our AI creates custom invoice templates based on your business type. 
                Perfect for freelancers, contractors, consultants, and service providers.
              </p>
            </div>
            
            <div className='text-center p-6'>
              <div className='bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Download className='h-8 w-8 text-green-600' />
              </div>
              <h3 className='text-xl font-semibold mb-3'>Instant PDF Download</h3>
              <p className='text-gray-600'>
                Download professional PDF invoices instantly. Perfect for email, 
                printing, or saving to your records. Works on all devices.
              </p>
            </div>
            
            <div className='text-center p-6'>
              <div className='bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Clock className='h-8 w-8 text-purple-600' />
              </div>
              <h3 className='text-xl font-semibold mb-3'>Quick & Easy</h3>
              <p className='text-gray-600'>
                Create professional invoices in under 2 minutes. No account required 
                for basic use. Start invoicing your clients immediately.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className='py-20 bg-gray-50'>
        <div className='container mx-auto px-4'>
          <div className='max-w-6xl mx-auto'>
            <h2 className='text-4xl font-bold text-center mb-16'>
              Why Choose Our Free Invoice Generator?
            </h2>
            
            <div className='grid md:grid-cols-2 gap-12'>
              <div className='space-y-6'>
                <div className='flex gap-4'>
                  <CheckCircle className='h-6 w-6 text-green-500 flex-shrink-0' />
                  <div>
                    <h3 className='font-semibold text-lg mb-2'>100% Free to Use</h3>
                    <p className='text-gray-600'>No hidden fees, no credit card required. Create unlimited professional invoices for free.</p>
                  </div>
                </div>
                
                <div className='flex gap-4'>
                  <Shield className='h-6 w-6 text-blue-500 flex-shrink-0' />
                  <div>
                    <h3 className='font-semibold text-lg mb-2'>Secure & Private</h3>
                    <p className='text-gray-600'>Your data is encrypted and secure. We never share your business information.</p>
                  </div>
                </div>
                
                <div className='flex gap-4'>
                  <Globe className='h-6 w-6 text-purple-500 flex-shrink-0' />
                  <div>
                    <h3 className='font-semibold text-lg mb-2'>Works Everywhere</h3>
                    <p className='text-gray-600'>Access from any device - desktop, tablet, or mobile. No software installation needed.</p>
                  </div>
                </div>
              </div>
              
              <div className='space-y-6'>
                <div className='flex gap-4'>
                  <FileText className='h-6 w-6 text-indigo-500 flex-shrink-0' />
                  <div>
                    <h3 className='font-semibold text-lg mb-2'>Professional Templates</h3>
                    <p className='text-gray-600'>Industry-specific templates that make your business look professional and established.</p>
                  </div>
                </div>
                
                <div className='flex gap-4'>
                  <Star className='h-6 w-6 text-yellow-500 flex-shrink-0' />
                  <div>
                    <h3 className='font-semibold text-lg mb-2'>Customer Favorite</h3>
                    <p className='text-gray-600'>Rated 4.8/5 by thousands of freelancers and small business owners.</p>
                  </div>
                </div>
                
                <div className='flex gap-4'>
                  <TrendingUp className='h-6 w-6 text-green-500 flex-shrink-0' />
                  <div>
                    <h3 className='font-semibold text-lg mb-2'>Grow Your Business</h3>
                    <p className='text-gray-600'>Professional invoices help you get paid faster and build client trust.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Industries Section */}
      <section className='py-20 bg-white'>
        <div className='container mx-auto px-4'>
          <h2 className='text-4xl font-bold text-center mb-4'>
            Invoice Templates for Every Industry
          </h2>
          <p className='text-xl text-gray-600 text-center mb-16 max-w-3xl mx-auto'>
            Choose from specialized templates designed for your specific business needs
          </p>
          
          <div className='grid grid-cols-2 md:grid-cols-4 gap-6 max-w-5xl mx-auto'>
            {[
              { name: 'Freelance Writers', icon: '✍️', keywords: 'freelance writer invoice template' },
              { name: 'Web Developers', icon: '💻', keywords: 'web developer invoice template' },
              { name: 'Consultants', icon: '💼', keywords: 'consultant invoice template' },
              { name: 'Photographers', icon: '📸', keywords: 'photography invoice template' },
              { name: 'Designers', icon: '🎨', keywords: 'graphic designer invoice template' },
              { name: 'Contractors', icon: '🔨', keywords: 'contractor invoice template' },
              { name: 'Healthcare', icon: '🏥', keywords: 'medical invoice template' },
              { name: 'Real Estate', icon: '🏡', keywords: 'real estate invoice template' },
            ].map((industry, index) => (
              <div 
                key={index}
                className='bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer'
              >
                <div className='text-4xl mb-3'>{industry.icon}</div>
                <p className='font-medium text-gray-800'>{industry.name}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section for Long-tail Keywords */}
      <section className='py-20 bg-gray-50'>
        <div className='container mx-auto px-4 max-w-4xl'>
          <h2 className='text-4xl font-bold text-center mb-16'>
            Frequently Asked Questions
          </h2>
          
          <div className='space-y-8'>
            <div>
              <h3 className='text-xl font-semibold mb-3'>
                How do I create a professional invoice for free?
              </h3>
              <p className='text-gray-600'>
                Simply click "Create Free Invoice" above, enter your business and client details, 
                add your services or products, and download your professional PDF invoice instantly. 
                No registration required for basic invoices.
              </p>
            </div>
            
            <div>
              <h3 className='text-xl font-semibold mb-3'>
                What invoice templates are available for freelancers?
              </h3>
              <p className='text-gray-600'>
                We offer specialized templates for freelance writers, web developers, graphic designers, 
                photographers, consultants, and more. Each template is optimized for your specific 
                industry and includes relevant fields and professional formatting.
              </p>
            </div>
            
            <div>
              <h3 className='text-xl font-semibold mb-3'>
                Can I send invoices directly to clients?
              </h3>
              <p className='text-gray-600'>
                Yes! You can email invoices directly to clients from our platform, or download 
                the PDF and send it yourself. All invoices include professional formatting and 
                your business branding.
              </p>
            </div>
            
            <div>
              <h3 className='text-xl font-semibold mb-3'>
                Is this invoice generator really free?
              </h3>
              <p className='text-gray-600'>
                Yes, our basic invoice generator is completely free. You can create unlimited 
                invoices with our standard templates. Premium features like advanced customization 
                and client management are available with our Pro plan.
              </p>
            </div>
            
            <div>
              <h3 className='text-xl font-semibold mb-3'>
                What makes a professional invoice?
              </h3>
              <p className='text-gray-600'>
                A professional invoice includes your business information, client details, unique 
                invoice number, clear itemization of services, payment terms, and total amount due. 
                Our templates automatically format all these elements for a polished, professional look.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className='py-20 bg-blue-600'>
        <div className='container mx-auto px-4 text-center'>
          <h2 className='text-4xl font-bold text-white mb-4'>
            Start Creating Professional Invoices Today
          </h2>
          <p className='text-xl text-blue-100 mb-8 max-w-2xl mx-auto'>
            Join thousands of freelancers and small businesses who trust our 
            free invoice generator for their billing needs.
          </p>
          <div className='flex flex-col sm:flex-row gap-4 justify-center'>
            <Link 
              href='/create'
              className='bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center'
            >
              Create Your First Invoice <ArrowRight className='ml-2 h-5 w-5' />
            </Link>
            <Link 
              href='/templates'
              className='border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white/10 transition-colors'
            >
              Browse Templates
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
