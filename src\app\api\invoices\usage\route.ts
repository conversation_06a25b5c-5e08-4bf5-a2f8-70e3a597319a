import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUsageStatistics } from '@/lib/invoice-limits';

/**
 * API Route: Get invoice usage statistics
 * GET /api/invoices/usage
 * 
 * Returns detailed usage statistics for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get comprehensive usage statistics
    const stats = await getUsageStatistics(session.user.id);
    
    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error fetching usage statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch usage statistics' },
      { status: 500 }
    );
  }
}