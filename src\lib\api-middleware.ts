import { NextRequest, NextResponse } from 'next/server';
import { trackAPIPerformance, trackEvent } from './analytics-enhanced';
import { getServerSession } from 'next-auth';
import { authOptions } from './auth';

// Wrapper for API routes to track performance
export function withPerformanceTracking(
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    const startTime = Date.now();
    let response: NextResponse;
    let error: Error | null = null;

    try {
      response = await handler(req);
    } catch (err) {
      error = err as Error;
      response = NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    // Track performance
    const duration = Date.now() - startTime;
    const pathname = new URL(req.url).pathname;
    const method = req.method;
    const status = error ? 'error' : 'success';
    const statusCode = response.status;

    // Get user ID if authenticated
    let userId: string | undefined;
    try {
      const session = await getServerSession(authOptions);
      userId = session?.user?.id;
    } catch {
      // Ignore auth errors for performance tracking
    }

    await trackAPIPerformance(
      pathname,
      method,
      duration,
      status,
      statusCode,
      error?.message,
      userId
    );

    // Add performance headers
    response.headers.set('X-Response-Time', `${duration}ms`);
    response.headers.set('X-Request-ID', generateRequestId());

    return response;
  };
}

// Wrapper for critical operations with detailed tracking
export function withCriticalTracking(
  category: string,
  operation: string
) {
  return function decorator<T extends (...args: any[]) => Promise<any>>(
    target: T
  ): T {
    return (async (...args: any[]) => {
      const startTime = Date.now();
      let result;
      let error: Error | null = null;

      try {
        result = await target(...args);
      } catch (err) {
        error = err as Error;
        throw err;
      } finally {
        const duration = Date.now() - startTime;
        
        await trackEvent(category as any, error ? 'fail' : 'complete', {
          label: operation,
          metadata: {
            operation,
            duration,
            success: !error,
            error: error?.message
          },
          performance: {
            duration,
            status: error ? 'error' : 'success',
            errorMessage: error?.message
          }
        });
      }

      return result;
    }) as T;
  };
}

// Error tracking middleware
export async function trackError(
  error: Error,
  context: {
    userId?: string;
    operation?: string;
    metadata?: Record<string, any>;
  }
): Promise<void> {
  await trackEvent('error', 'error', {
    userId: context.userId,
    label: error.name,
    metadata: {
      message: error.message,
      stack: error.stack,
      operation: context.operation,
      ...context.metadata
    }
  });
}

// Generate unique request ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}

// Rate limiting with analytics
export async function checkRateLimitWithAnalytics(
  identifier: string,
  limit: number = 100,
  window: number = 60000 // 1 minute
): Promise<{ allowed: boolean; remaining: number }> {
  // This would integrate with your rate limiting logic
  // For now, just track the attempt
  await trackEvent('system', 'view', {
    label: 'rate_limit_check',
    metadata: {
      identifier,
      limit,
      window
    }
  });

  // Placeholder implementation
  return { allowed: true, remaining: limit };
}

// Session tracking
export async function trackUserSession(
  userId: string,
  event: 'start' | 'end' | 'active',
  metadata?: Record<string, any>
): Promise<void> {
  await trackEvent('user', event === 'start' ? 'login' : event === 'end' ? 'logout' : 'view', {
    userId,
    label: `session_${event}`,
    metadata: {
      sessionEvent: event,
      ...metadata
    }
  });
}