'use client';

// Client-side Stripe configuration
// This file can be imported in client components

// Determine if we're in test mode based on environment variable
const isTestMode = process.env.NEXT_PUBLIC_STRIPE_MODE === 'test';

// Select the appropriate publishable key
export const stripePublishableKey = isTestMode
  ? process.env.NEXT_PUBLIC_STRIPE_TEST_PUBLISHABLE_KEY || ''
  : process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '';

// Get the current mode
export const getStripeMode = () => isTestMode ? 'test' : 'live';

// Check if we're using test keys
export const isUsingTestKeys = () => {
  return stripePublishableKey.startsWith('pk_test_');
};

// Export configuration object
export const stripeClientConfig = {
  publishableKey: stripePublishableKey,
  mode: getStripeMode(),
  isTestMode,
};