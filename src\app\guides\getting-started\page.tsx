import type { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowRight, CheckCircle, FileText, Download, Eye, Mail, UserPlus, Palette } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Getting Started with Template Invoice - Complete Beginner\'s Guide',
  description: 'Learn how to create your first professional invoice in under 5 minutes. Step-by-step guide for beginners using Template Invoice generator.',
  keywords: ['how to create invoice', 'invoice tutorial', 'beginner invoice guide', 'first invoice', 'invoice generator tutorial', 'professional invoice creation'],
  openGraph: {
    title: 'Getting Started with Template Invoice - Complete Beginner\'s Guide',
    description: 'Learn how to create your first professional invoice in under 5 minutes. Step-by-step guide for beginners.',
    type: 'article',
  },
}

export default function GettingStartedGuide() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Getting Started with Template Invoice
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Complete beginner's guide to creating your first professional invoice in under 5 minutes
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="max-w-4xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
        <nav className="flex items-center space-x-2 text-sm text-gray-600">
          <Link href="/" className="hover:text-gray-900">Home</Link>
          <span>/</span>
          <Link href="/guides" className="hover:text-gray-900">Guides</Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">Getting Started</span>
        </nav>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Table of Contents */}
        <div className="bg-blue-50 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Table of Contents</h2>
          <ol className="space-y-2 text-blue-700">
            <li><a href="#introduction" className="hover:text-blue-900">1. Introduction</a></li>
            <li><a href="#sign-up" className="hover:text-blue-900">2. Creating Your Account</a></li>
            <li><a href="#dashboard" className="hover:text-blue-900">3. Understanding the Dashboard</a></li>
            <li><a href="#select-template" className="hover:text-blue-900">4. Selecting Your Template</a></li>
            <li><a href="#fill-invoice" className="hover:text-blue-900">5. Filling Out Your Invoice</a></li>
            <li><a href="#preview" className="hover:text-blue-900">6. Preview and Review</a></li>
            <li><a href="#download-send" className="hover:text-blue-900">7. Download and Send</a></li>
            <li><a href="#next-steps" className="hover:text-blue-900">8. Next Steps</a></li>
          </ol>
        </div>

        {/* Introduction */}
        <section id="introduction" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Introduction</h2>
          <p className="text-gray-700 leading-relaxed mb-4">
            Welcome to Template Invoice! Whether you're a freelancer, small business owner, or consultant, 
            creating professional invoices is essential for getting paid on time. This comprehensive guide 
            will walk you through every step of creating your first invoice, from signing up to sending it 
            to your client.
          </p>
          <p className="text-gray-700 leading-relaxed mb-4">
            By the end of this tutorial, you'll be able to:
          </p>
          <ul className="space-y-2 mb-6">
            <li className="flex items-start">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700">Create a professional account on Template Invoice</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700">Navigate the dashboard and understand key features</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700">Select and customize an invoice template</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700">Generate and send professional invoices to clients</span>
            </li>
          </ul>
        </section>

        {/* Sign Up Process */}
        <section id="sign-up" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Creating Your Account</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Getting started with Template Invoice is quick and easy. Follow these simple steps to create 
            your account:
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start">
                <div className="bg-blue-100 rounded-full p-2 mr-4">
                  <UserPlus className="w-6 h-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold mb-2">Step 1: Visit the Sign-Up Page</h3>
                  <p className="text-gray-700 mb-4">
                    Navigate to <Link href="/auth/signin" className="text-blue-600 hover:underline">templateinvoice.com/auth/signin</Link> and 
                    click on "Create an account" or sign in with Google for instant access.
                  </p>
                  <div className="bg-gray-100 rounded p-4 text-sm">
                    <strong>Pro Tip:</strong> Using Google sign-in saves time and automatically fills in your 
                    profile information.
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start">
                <div className="bg-blue-100 rounded-full p-2 mr-4">
                  <Mail className="w-6 h-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold mb-2">Step 2: Verify Your Email</h3>
                  <p className="text-gray-700 mb-4">
                    If you signed up with email, check your inbox for a verification link. Click it to 
                    activate your account. This ensures your invoices reach the right recipients.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start">
                <div className="bg-blue-100 rounded-full p-2 mr-4">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold mb-2">Step 3: Complete Your Profile</h3>
                  <p className="text-gray-700 mb-4">
                    Fill in your business information including:
                  </p>
                  <ul className="list-disc list-inside space-y-1 text-gray-700">
                    <li>Business name and logo</li>
                    <li>Contact information</li>
                    <li>Tax ID or business number</li>
                    <li>Default payment terms</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Dashboard Overview */}
        <section id="dashboard" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Understanding the Dashboard</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Once logged in, you'll see your personalized dashboard. Here's what each section does:
          </p>

          <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden mb-6">
            <div className="bg-gray-50 p-4 border-b border-gray-200">
              <h3 className="font-semibold">Dashboard Overview</h3>
            </div>
            <div className="p-6 space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Quick Actions</h4>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Create New Invoice button</li>
                    <li>• View Recent Invoices</li>
                    <li>• Access Templates</li>
                    <li>• Manage Clients</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Statistics Panel</h4>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Total invoices created</li>
                    <li>• Monthly revenue tracking</li>
                    <li>• Outstanding payments</li>
                    <li>• Client overview</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Template Selection */}
        <section id="select-template" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Selecting Your Template</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Template Invoice offers a variety of professional templates tailored to different industries 
            and styles. Here's how to choose the perfect one:
          </p>

          <div className="space-y-4">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Palette className="w-6 h-6 mr-2 text-purple-600" />
                Browse Template Categories
              </h3>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-700">
                  <li>• <strong>Professional:</strong> Clean, minimalist designs</li>
                  <li>• <strong>Creative:</strong> Colorful, modern layouts</li>
                  <li>• <strong>Corporate:</strong> Traditional business formats</li>
                </ul>
                <ul className="space-y-2 text-gray-700">
                  <li>• <strong>Freelance:</strong> Perfect for individuals</li>
                  <li>• <strong>Service:</strong> Ideal for service businesses</li>
                  <li>• <strong>Product:</strong> Great for itemized billing</li>
                </ul>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-3">Template Selection Tips</h3>
              <ol className="space-y-3">
                <li className="flex items-start">
                  <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                  <div>
                    <strong>Consider Your Industry:</strong> Choose templates that match your business type
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                  <div>
                    <strong>Preview First:</strong> Click on any template to see a full preview
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                  <div>
                    <strong>Check Customization:</strong> Ensure the template supports your needed fields
                  </div>
                </li>
              </ol>
            </div>
          </div>
        </section>

        {/* Filling Out Invoice */}
        <section id="fill-invoice" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Filling Out Your Invoice</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Now comes the important part - adding your invoice details. Follow this comprehensive guide 
            to ensure nothing is missed:
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Essential Invoice Information</h3>
              
              <div className="space-y-4">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-1">Your Business Details</h4>
                  <p className="text-gray-700 text-sm mb-2">This information appears at the top of your invoice:</p>
                  <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                    <li>Business name and logo</li>
                    <li>Complete address</li>
                    <li>Phone number and email</li>
                    <li>Tax ID or registration number</li>
                  </ul>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-1">Client Information</h4>
                  <p className="text-gray-700 text-sm mb-2">Accurate client details ensure proper delivery:</p>
                  <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                    <li>Client company name</li>
                    <li>Contact person's name</li>
                    <li>Billing address</li>
                    <li>Client email and phone</li>
                  </ul>
                </div>

                <div className="border-l-4 border-purple-500 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-1">Invoice Details</h4>
                  <p className="text-gray-700 text-sm mb-2">Critical information for tracking and payment:</p>
                  <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                    <li>Invoice number (auto-generated or custom)</li>
                    <li>Invoice date</li>
                    <li>Due date (based on payment terms)</li>
                    <li>Purchase order number (if applicable)</li>
                  </ul>
                </div>

                <div className="border-l-4 border-orange-500 pl-4">
                  <h4 className="font-semibold text-gray-900 mb-1">Line Items</h4>
                  <p className="text-gray-700 text-sm mb-2">Clearly itemize your products or services:</p>
                  <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                    <li>Description of service/product</li>
                    <li>Quantity or hours</li>
                    <li>Rate or unit price</li>
                    <li>Line total (automatically calculated)</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <FileText className="w-6 h-6 mr-2 text-yellow-600" />
                Best Practices for Invoice Details
              </h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Be specific in descriptions - avoid vague terms like "Services"</li>
                <li>• Include project names or reference numbers</li>
                <li>• Add payment instructions and accepted methods</li>
                <li>• Include late payment terms if applicable</li>
                <li>• Add notes for special instructions or thank you messages</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Preview Section */}
        <section id="preview" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Preview and Review</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Before finalizing your invoice, it's crucial to review everything carefully. Our preview 
            feature shows exactly how your client will see the invoice.
          </p>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center">
              <Eye className="w-6 h-6 mr-2 text-blue-600" />
              Preview Checklist
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Visual Review</h4>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                    <span className="text-gray-700 text-sm">Logo displays correctly</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                    <span className="text-gray-700 text-sm">Colors match your brand</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                    <span className="text-gray-700 text-sm">Layout is professional</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                    <span className="text-gray-700 text-sm">Text is readable</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Content Accuracy</h4>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                    <span className="text-gray-700 text-sm">All amounts are correct</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                    <span className="text-gray-700 text-sm">Dates are accurate</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                    <span className="text-gray-700 text-sm">Client info is complete</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                    <span className="text-gray-700 text-sm">Payment terms are clear</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Download and Send */}
        <section id="download-send" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Download and Send</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            You're almost done! Now it's time to deliver your professional invoice to your client. 
            Template Invoice offers multiple delivery options:
          </p>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <Download className="w-8 h-8 text-blue-600 mr-3" />
                <h3 className="text-xl font-semibold">Download as PDF</h3>
              </div>
              <p className="text-gray-700 mb-4">
                Download a high-quality PDF version of your invoice for your records or to attach to emails.
              </p>
              <ul className="space-y-2 text-sm text-gray-700">
                <li>• Professional PDF format</li>
                <li>• Print-ready quality</li>
                <li>• Secure and uneditable</li>
                <li>• Perfect for email attachments</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <Mail className="w-8 h-8 text-green-600 mr-3" />
                <h3 className="text-xl font-semibold">Email Directly</h3>
              </div>
              <p className="text-gray-700 mb-4">
                Send invoices directly from Template Invoice with our built-in email system.
              </p>
              <ul className="space-y-2 text-sm text-gray-700">
                <li>• Professional email template</li>
                <li>• Automatic PDF attachment</li>
                <li>• Delivery tracking</li>
                <li>• Payment reminders</li>
              </ul>
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-6 mt-6">
            <h3 className="text-xl font-semibold mb-3">Email Best Practices</h3>
            <div className="space-y-3 text-gray-700">
              <p>When sending your invoice via email, remember to:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>Use a clear subject line: "Invoice #[Number] from [Your Business]"</li>
                <li>Include a brief, friendly message thanking them for their business</li>
                <li>Mention the due date and payment methods</li>
                <li>Attach the PDF invoice</li>
                <li>Include your contact information for questions</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Next Steps */}
        <section id="next-steps" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Next Steps</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Congratulations! You've created and sent your first professional invoice. Here's what to do next 
            to maximize your invoicing efficiency:
          </p>

          <div className="space-y-4">
            <Link href="/guides/customizing-templates" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold mb-2">Customize Your Templates</h3>
                  <p className="text-gray-700">Learn to personalize templates with your brand colors, fonts, and layout preferences.</p>
                </div>
                <ArrowRight className="w-6 h-6 text-gray-400" />
              </div>
            </Link>

            <Link href="/guides/managing-clients" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold mb-2">Set Up Client Management</h3>
                  <p className="text-gray-700">Save time by storing client information for quick invoice creation.</p>
                </div>
                <ArrowRight className="w-6 h-6 text-gray-400" />
              </div>
            </Link>

            <Link href="/guides/automation" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold mb-2">Explore Automation Features</h3>
                  <p className="text-gray-700">Set up recurring invoices and automatic payment reminders.</p>
                </div>
                <ArrowRight className="w-6 h-6 text-gray-400" />
              </div>
            </Link>
          </div>
        </section>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Create Your First Invoice?</h2>
          <p className="mb-6">Put your new knowledge into practice and create a professional invoice in minutes.</p>
          <Link href="/create" className="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Create Invoice Now
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
        </div>

        {/* Help Section */}
        <div className="mt-12 text-center">
          <p className="text-gray-600">
            Need more help? Visit our <Link href="/help" className="text-blue-600 hover:underline">Help Center</Link> or{' '}
            <Link href="/contact" className="text-blue-600 hover:underline">contact support</Link>.
          </p>
        </div>
      </div>
    </main>
  )
}