import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import '@/styles/globals.css'
import Link from 'next/link'
import { Mail } from 'lucide-react'
import SessionProvider from '@/components/SessionProvider'
import { OnboardingProvider } from '@/components/onboarding/OnboardingProvider'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { siteConfig, generateMetaTags, structuredData } from '@/lib/seo-config'
import Script from 'next/script'
import StructuredData from '@/components/StructuredData'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'
import StripeTestModeIndicator from '@/components/StripeTestModeIndicator'

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true
})

// Import WebVitals component directly - it's already a client component
import WebVitals from '@/components/ProductionWebVitals'

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  themeColor: '#ffffff'
}

export const metadata: Metadata = {
  metadataBase: new URL('https://templateinvoices.com'),
  title: {
    default: 'Template Invoices - Professional Invoice Generator | Create Invoices Online',
    template: '%s | Template Invoices'
  },
  description: 'Create professional invoices in minutes with our free invoice generator. Choose from beautiful templates, generate PDFs instantly, and send directly to clients. No signup required.',
  keywords: [
    'invoice generator',
    'free invoice template',
    'professional invoices',
    'online invoice maker',
    'invoice creator',
    'business invoicing',
    'invoice templates',
    'PDF invoice generator',
    'small business invoicing',
    'freelance invoice template'
  ],
  authors: [{ name: 'Template Invoices' }],
  creator: 'Template Invoices',
  publisher: 'Template Invoices',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'Template Invoices - Professional Invoice Generator',
    description: 'Create beautiful invoices in minutes. Free templates, instant PDFs.',
    url: 'https://templateinvoices.com',
    siteName: 'Template Invoices',
    type: 'website',
    locale: 'en_US',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Template Invoices - Professional Invoice Generator',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Template Invoices - Professional Invoice Generator',
    description: 'Create beautiful invoices in minutes. Free templates, instant PDFs.',
    creator: '@templateinvoices',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'YOUR_GOOGLE_VERIFICATION_CODE', // Add from Google Search Console
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' }
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180' }
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/safari-pinned-tab.svg',
        color: '#3b82f6'
      }
    ]
  },
  manifest: '/manifest.json',
  alternates: {
    canonical: 'https://templateinvoices.com',
    languages: {
      'en-US': 'https://templateinvoices.com',
      'es-ES': 'https://templateinvoices.com/es',
      'fr-FR': 'https://templateinvoices.com/fr',
    },
    types: {
      'application/rss+xml': 'https://templateinvoices.com/rss.xml'
    }
  },
  category: 'business'
}

// Google Analytics ID - replace with your actual ID
const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_ID || 'G-XXXXXXXXXX'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} scroll-smooth`}>
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Favicon set */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        
        {/* Theme and viewport */}
        <meta name="theme-color" content="#3b82f6" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        
        {/* PWA meta tags */}
        <meta name="application-name" content="Template Invoices" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Template Invoices" />
        <meta name="mobile-web-app-capable" content="yes" />
        
        {/* Additional SEO meta tags */}
        <meta name="author" content="Template Invoices" />
        <meta name="generator" content="Next.js" />
        <meta name="referrer" content="origin-when-cross-origin" />
        <meta name="color-scheme" content="light dark" />
        
        {/* Geo tags */}
        <meta name="geo.region" content="US" />
        <meta name="geo.placename" content="United States" />
        
        {/* Open Graph additional tags */}
        <meta property="og:image:type" content="image/jpeg" />
        <meta property="og:image:alt" content="Template Invoices - Professional Invoice Generator" />
        
        {/* Twitter additional tags */}
        <meta name="twitter:image:alt" content="Template Invoices - Professional Invoice Generator" />
        
        {/* Structured Data */}
        <StructuredData type="homepage" />
      </head>
      <body 
        className={`${inter.className} antialiased text-gray-900 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen flex flex-col`}
        suppressHydrationWarning={true}
      >
        {/* Google Analytics */}
        <Script
          src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_path: window.location.pathname,
            });
          `}
        </Script>
        
        <SessionProvider>
          <OnboardingProvider>
            {/* Web Vitals monitoring - only in production */}
            {process.env.NODE_ENV === 'production' && <WebVitals />}
            
            {/* Stripe Test Mode Indicator */}
            <StripeTestModeIndicator />
            
            {/* Skip to main content for accessibility */}
            <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded">
              Skip to main content
            </a>
            
            {/* Sticky Header */}
            <header className="sticky top-0 z-50 w-full border-b border-gray-200/50 bg-white/70 backdrop-blur-xl supports-[backdrop-filter]:bg-white/70" role="banner">
              <nav className="container mx-auto px-4 sm:px-6 lg:px-8" aria-label="Main navigation">
                <div className="flex h-16 items-center justify-between">
                  {/* Logo */}
                  <div className="flex items-center">
                    <Link href="/" className="flex items-center gap-2" aria-label="Invoice Generator Home">
                      <img src="/images/logo/logo.svg" alt="Invoice Generator Logo" className="h-10 w-auto" width="150" height="40" />
                    </Link>
                  </div>
                  
                  {/* Navigation Links */}
                  <nav className="hidden md:flex items-center space-x-8" role="navigation">
                    <Link href="/guides" className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors">
                      Guides
                    </Link>
                    <Link href="/templates" className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors">
                      Templates
                    </Link>
                    <Link href="/pricing" className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors">
                      Pricing
                    </Link>
                    <Link href="/dashboard" className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors">
                      Dashboard
                    </Link>
                  </nav>
                  
                  {/* CTA Buttons */}
                  <div className="flex items-center space-x-4">
                    <Link href="/auth/signin" className="hidden sm:inline-flex text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors">
                      Sign In
                    </Link>
                    <Link href="/create" className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-200">
                      Create Invoice
                    </Link>
                  </div>
                </div>
              </nav>
            </header>

            {/* Main Content Wrapper */}
            <main id="main-content" className="flex-1 w-full" role="main">
              <div className="relative isolate">
                {/* Background decoration */}
                <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80" aria-hidden="true">
                  <div className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-blue-200 to-purple-200 opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]" />
                </div>
                
                {/* Content Container */}
                <div className="mx-auto max-w-7xl">
                  {children}
                </div>
                
                {/* Bottom decoration */}
                <div className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]" aria-hidden="true">
                  <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-purple-200 to-pink-200 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]" />
                </div>
              </div>
            </main>

            {/* Footer */}
            <footer className="mt-auto bg-gray-50/50 backdrop-blur-sm border-t border-gray-200/50" role="contentinfo">
              <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-16">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-8 lg:gap-12">
                  {/* Company Info */}
                  <div className="col-span-2 md:col-span-1">
                    <Link href="/" className="inline-block mb-4" aria-label="Invoice Generator Home">
                      <img src="/images/logo/logo.svg" alt="Invoice Generator Logo" className="h-8 w-auto" width="120" height="32" />
                    </Link>
                    <p className="text-sm text-gray-600 mb-6">
                      Create professional invoices instantly. Perfect for freelancers, consultants, and small businesses.
                    </p>
                  </div>
                  
                  {/* Product Links */}
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4">
                      Product
                    </h4>
                    <ul className="space-y-3" role="list">
                      <li>
                        <Link href="/guides" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          Guides
                        </Link>
                      </li>
                      <li>
                        <Link href="/templates" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          Templates
                        </Link>
                      </li>
                      <li>
                        <Link href="/pricing" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          Pricing
                        </Link>
                      </li>
                    </ul>
                  </div>
                  
                  {/* Resources */}
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4">
                      Resources
                    </h4>
                    <ul className="space-y-3" role="list">
                      <li>
                        <Link href="/dashboard" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          Dashboard
                        </Link>
                      </li>
                      <li>
                        <Link href="/guides" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          Guides
                        </Link>
                      </li>
                      <li>
                        <Link href="/help" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          Help Center
                        </Link>
                      </li>
                      <li>
                        <Link href="/contact" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          Contact
                        </Link>
                      </li>
                    </ul>
                  </div>
                  
                  {/* Company */}
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4">
                      Company
                    </h4>
                    <ul className="space-y-3" role="list">
                      <li>
                        <Link href="/about" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          About
                        </Link>
                      </li>
                      <li>
                        <Link href="/terms" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          Terms of Service
                        </Link>
                      </li>
                      <li>
                        <Link href="/privacy" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          Privacy Policy
                        </Link>
                      </li>
                    </ul>
                  </div>
                </div>
                
                {/* Bottom Bar */}
                <div className="mt-12 pt-8 border-t border-gray-200">
                  <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <p className="text-sm text-gray-600">
                      &copy; {new Date().getFullYear()} Invoice Generator. All rights reserved.
                    </p>
                    <div className="flex space-x-6">
                      <Link href="/terms" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                        Terms
                      </Link>
                      <Link href="/privacy" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                        Privacy
                      </Link>
                      <Link href="/cookies" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                        Cookies
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </footer>
          </OnboardingProvider>
        </SessionProvider>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  )
}