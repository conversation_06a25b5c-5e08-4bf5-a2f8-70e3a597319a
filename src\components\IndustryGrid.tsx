'use client'

import { Briefcase, User, Wrench, Camera, Home, FileText } from 'lucide-react'
import { motion } from 'framer-motion'

const industries = [
  {
    name: "Freelancers",
    icon: User,
    desc: "Easy invoices for gig economy and creatives.",
  },
  {
    name: "Consultants",
    icon: Briefcase,
    desc: "Professional, tax-compliant billing.",
  },
  {
    name: "Contractors",
    icon: Wrench,
    desc: "Track hours, materials, and milestones.",
  },
  {
    name: "Photography",
    icon: Camera,
    desc: "Showcase your work and get paid fast.",
  },
  {
    name: "Legal",
    icon: FileText,
    desc: "Bill by retainer, hourly, or case.",
  },
  {
    name: "Real Estate",
    icon: Home,
    desc: "Seamless invoicing for brokers and agents.",
  },
]

export default function IndustryGrid() {
  return (
    <div id="industries">
      <motion.h2 
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
        className="text-3xl sm:text-4xl font-bold mb-8 text-center"
      >
        Tailored For Your Industry
      </motion.h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {industries.map((ind, i) => {
          const Icon = ind.icon
          return (
            <motion.div
              key={ind.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: i * 0.1 }}
              whileHover={{ scale: 1.035 }}
              className="group relative p-8 rounded-2xl shadow-xl bg-white/70 backdrop-blur-lg flex flex-col items-center justify-between transition-all duration-300 border border-gray-200 hover:border-black cursor-pointer"
            >
              <div className="mb-4">
                <Icon className="w-7 h-7 text-black/80" />
              </div>
              <h3 className="font-semibold text-xl mb-2">{ind.name}</h3>
              <p className="text-gray-600 text-center">{ind.desc}</p>
              <span className="absolute right-4 top-4 text-gray-300 group-hover:text-black transition-colors duration-200">&#8250;</span>
            </motion.div>
          )
        })}
      </div>
    </div>
  )
}