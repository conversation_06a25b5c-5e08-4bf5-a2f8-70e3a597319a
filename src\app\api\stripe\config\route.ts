import { NextRequest, NextResponse } from 'next/server';
import { isStripeConfigured, stripeConfig, getStripeMode, isUsingTestKeys } from '@/lib/stripe-config';

export async function GET(request: NextRequest) {
  try {
    const configured = isStripeConfigured();
    const mode = getStripeMode();
    const isTest = isUsingTestKeys();
    
    if (!configured) {
      return NextResponse.json({
        configured: false,
        message: 'Stripe not configured',
        mode,
        isTestMode: isTest,
        publicKey: !!stripeConfig.publishableKey
      });
    }

    // Basic configuration check
    return NextResponse.json({
      configured: true,
      message: `Stripe is configured in ${mode} mode`,
      mode,
      isTestMode: isTest,
      publicKey: !!stripeConfig.publishableKey,
      webhookConfigured: !!stripeConfig.webhookSecret,
      warning: isTest && process.env.NODE_ENV === 'production' 
        ? 'WARNING: Using test keys in production environment' 
        : undefined
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Stripe config check failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}