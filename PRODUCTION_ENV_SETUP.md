# Production Environment Variables Setup

## Critical: Set These in Vercel Dashboard

Go to your Vercel project → Settings → Environment Variables and add:

### NextAuth Configuration
```
NEXTAUTH_URL=https://templateinvoices.com
NEXTAUTH_SECRET=GyR5wc479Bc4VlydwpWiPxYxyruJH5KMwOBWoDtOLrU=
```

### Google OAuth (Production)
```
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
```

### Database
```
MONGODB_URI=your-production-mongodb-connection-string
```

### Application
```
NEXT_PUBLIC_BASE_URL=https://templateinvoices.com
```

## Google OAuth Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Go to APIs & Services → Credentials
4. Edit your OAuth 2.0 Client ID
5. Add these to **Authorized JavaScript origins**:
   - `https://templateinvoices.com`
   - `https://www.templateinvoices.com` (for fallback)

6. Add these to **Authorized redirect URIs**:
   - `https://templateinvoices.com/api/auth/callback/google`
   - `https://www.templateinvoices.com/api/auth/callback/google` (for fallback)

## ⚠️ CRITICAL FIXES NEEDED:

### 1. Environment Variables Missing
The most likely cause of auth failure is missing or incorrect environment variables in Vercel.

### 2. Google OAuth URLs
Your Google OAuth console must include the production URLs above.

### 3. NEXTAUTH_URL Must Be Exact
- ✅ Correct: `https://templateinvoices.com`
- ❌ Wrong: `https://www.templateinvoices.com`
- ❌ Wrong: `http://templateinvoices.com`
- ❌ Wrong: `https://templateinvoices.com/`

### 4. Test Checklist
After setting environment variables:
1. Redeploy the app
2. Try signing in
3. Check Vercel function logs for our debug messages
4. Look for: "🔍 [AUTH] NextAuth endpoint hit"

## Debug Information
The app now logs these messages in Vercel functions:
- `🔍 [AUTH] NextAuth endpoint hit`
- `🔍 [AUTH] NEXTAUTH_URL: [value]`
- `🔍 [AUTH] MongoDB URI exists: [true/false]`
- `🔍 [AUTH] Google Client ID exists: [true/false]`
- `🔍 [SIGNIN] Attempting sign in for: [email]`
- `✅ [SIGNIN] User created/updated successfully`
- `🔍 [REDIRECT] Redirect called with: [urls]`