// Client Service for Template Invoice System
import { ObjectId } from 'mongodb';
import clientPromise from './mongodb';
import { 
  ClientDocument, 
  CreateClientInput, 
  UpdateClientInput, 
  COLLECTIONS 
} from './models';
import { logActivity } from './user-service';

// Get database instance
async function getDatabase() {
  const client = await clientPromise;
  return client.db();
}

// Create client
export async function createClient(input: CreateClientInput): Promise<ClientDocument> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const now = new Date();
  
  const client: ClientDocument = {
    ...input,
    totalInvoiced: 0,
    invoiceCount: 0,
    averageInvoiceAmount: 0,
    status: input.status || 'active',
    createdAt: now,
    updatedAt: now
  };
  
  const result = await clientsCollection.insertOne(client);
  
  // Log activity
  await logActivity({
    userId: input.userId,
    action: 'client_created',
    resourceType: 'client',
    resourceId: result.insertedId,
    details: {
      description: `Client '${client.name}' created`,
      metadata: {
        clientName: client.name,
        clientEmail: client.email
      }
    }
  });
  
  return { ...client, _id: result.insertedId };
}

// Update client
export async function updateClient(clientId: string | ObjectId, userId: string | ObjectId, updates: UpdateClientInput): Promise<ClientDocument | null> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const clientObjectId = typeof clientId === 'string' ? new ObjectId(clientId) : clientId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const result = await clientsCollection.findOneAndUpdate(
    { _id: clientObjectId, userId: userObjectId },
    { 
      $set: { 
        ...updates,
        updatedAt: new Date()
      }
    },
    { returnDocument: 'after' }
  );
  
  if (result) {
    // Log activity
    await logActivity({
      userId: userObjectId,
      action: 'client_updated',
      resourceType: 'client',
      resourceId: clientObjectId,
      details: {
        description: `Client '${result.value?.name || 'Unknown'}' updated`,
        metadata: { updatedFields: Object.keys(updates) }
      }
    });
  }
  
  return result?.value || null;
}

// Get client by ID
export async function getClientById(clientId: string | ObjectId, userId: string | ObjectId): Promise<ClientDocument | null> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const clientObjectId = typeof clientId === 'string' ? new ObjectId(clientId) : clientId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  return await clientsCollection.findOne({ _id: clientObjectId, userId: userObjectId });
}

// Get user's clients with pagination and filters
export async function getUserClients(
  userId: string | ObjectId, 
  options: {
    page?: number;
    limit?: number;
    status?: ClientDocument['status'];
    search?: string;
    sortBy?: 'name' | 'createdAt' | 'lastInvoiceDate' | 'totalInvoiced';
    sortOrder?: 'asc' | 'desc';
  } = {}
): Promise<{ clients: ClientDocument[]; total: number; hasMore: boolean }> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const {
    page = 1,
    limit = 20,
    status,
    search,
    sortBy = 'name',
    sortOrder = 'asc'
  } = options;
  
  const skip = (page - 1) * limit;
  
  // Build query
  const query: any = { userId: userObjectId };
  
  if (status) {
    query.status = status;
  }
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { contactPerson: { $regex: search, $options: 'i' } },
      { businessType: { $regex: search, $options: 'i' } }
    ];
  }
  
  // Build sort
  const sort: any = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
  
  // Execute queries
  const [clients, total] = await Promise.all([
    clientsCollection
      .find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .toArray(),
    clientsCollection.countDocuments(query)
  ]);
  
  const hasMore = skip + clients.length < total;
  
  return { clients, total, hasMore };
}

// Search clients (for autocomplete)
export async function searchClients(userId: string | ObjectId, searchTerm: string, limit: number = 10): Promise<ClientDocument[]> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  return await clientsCollection
    .find({
      userId: userObjectId,
      status: 'active',
      $or: [
        { name: { $regex: searchTerm, $options: 'i' } },
        { email: { $regex: searchTerm, $options: 'i' } }
      ]
    })
    .sort({ name: 1 })
    .limit(limit)
    .toArray();
}

// Get client by email (for reusing client data)
export async function getClientByEmail(userId: string | ObjectId, email: string): Promise<ClientDocument | null> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  return await clientsCollection.findOne({ 
    userId: userObjectId, 
    email: email.toLowerCase().trim()
  });
}

// Delete client
export async function deleteClient(clientId: string | ObjectId, userId: string | ObjectId): Promise<boolean> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const clientObjectId = typeof clientId === 'string' ? new ObjectId(clientId) : clientId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Get client details for logging
  const client = await clientsCollection.findOne({ _id: clientObjectId, userId: userObjectId });
  
  if (!client) {
    return false;
  }
  
  const result = await clientsCollection.deleteOne({ _id: clientObjectId, userId: userObjectId });
  
  if (result.deletedCount > 0) {
    await logActivity({
      userId: userObjectId,
      action: 'client_deleted',
      resourceType: 'client',
      details: {
        description: `Client '${client.name}' deleted`,
        metadata: {
          clientName: client.name,
          clientEmail: client.email
        }
      }
    });
    return true;
  }
  
  return false;
}

// Get client statistics
export async function getClientStatistics(userId: string | ObjectId) {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const stats = await clientsCollection.aggregate([
    { $match: { userId: userObjectId } },
    {
      $group: {
        _id: null,
        totalClients: { $sum: 1 },
        activeClients: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        totalRevenue: { $sum: '$totalInvoiced' },
        averageClientValue: { $avg: '$totalInvoiced' },
        topClient: { $max: '$totalInvoiced' },
        statusCounts: {
          $push: '$status'
        }
      }
    },
    {
      $addFields: {
        statusBreakdown: {
          $arrayToObject: {
            $map: {
              input: [
                { k: 'active', v: { $size: { $filter: { input: '$statusCounts', cond: { $eq: ['$$this', 'active'] } } } } },
                { k: 'inactive', v: { $size: { $filter: { input: '$statusCounts', cond: { $eq: ['$$this', 'inactive'] } } } } },
                { k: 'blocked', v: { $size: { $filter: { input: '$statusCounts', cond: { $eq: ['$$this', 'blocked'] } } } } }
              ],
              as: { k: '$$this.k', v: '$$this.v' }
            }
          }
        }
      }
    }
  ]).toArray();
  
  return stats[0] || {
    totalClients: 0,
    activeClients: 0,
    totalRevenue: 0,
    averageClientValue: 0,
    topClient: 0,
    statusBreakdown: {
      active: 0,
      inactive: 0,
      blocked: 0
    }
  };
}

// Get top clients by revenue
export async function getTopClients(userId: string | ObjectId, limit: number = 10): Promise<ClientDocument[]> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  return await clientsCollection
    .find({ userId: userObjectId, status: 'active' })
    .sort({ totalInvoiced: -1 })
    .limit(limit)
    .toArray();
}

// Get recent clients
export async function getRecentClients(userId: string | ObjectId, limit: number = 5): Promise<ClientDocument[]> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  return await clientsCollection
    .find({ userId: userObjectId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .toArray();
}

// Mark client as inactive
export async function markClientAsInactive(clientId: string | ObjectId, userId: string | ObjectId): Promise<ClientDocument | null> {
  return await updateClient(clientId, userId, { status: 'inactive' });
}

// Mark client as active
export async function markClientAsActive(clientId: string | ObjectId, userId: string | ObjectId): Promise<ClientDocument | null> {
  return await updateClient(clientId, userId, { status: 'active' });
}

// Add tags to client
export async function addClientTags(clientId: string | ObjectId, userId: string | ObjectId, tags: string[]): Promise<ClientDocument | null> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const clientObjectId = typeof clientId === 'string' ? new ObjectId(clientId) : clientId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const result = await clientsCollection.findOneAndUpdate(
    { _id: clientObjectId, userId: userObjectId },
    { 
      $addToSet: { tags: { $each: tags } },
      $set: { updatedAt: new Date() }
    },
    { returnDocument: 'after' }
  );
  
  if (result) {
    await logActivity({
      userId: userObjectId,
      action: 'client_tags_added',
      resourceType: 'client',
      resourceId: clientObjectId,
      details: {
        description: `Tags added to client '${result.value?.name || 'Unknown'}'`,
        metadata: { tags }
      }
    });
  }
  
  return result?.value || null;
}

// Remove tags from client
export async function removeClientTags(clientId: string | ObjectId, userId: string | ObjectId, tags: string[]): Promise<ClientDocument | null> {
  const db = await getDatabase();
  const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
  
  const clientObjectId = typeof clientId === 'string' ? new ObjectId(clientId) : clientId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const result = await clientsCollection.findOneAndUpdate(
    { _id: clientObjectId, userId: userObjectId },
    { 
      $pullAll: { tags: tags },
      $set: { updatedAt: new Date() }
    },
    { returnDocument: 'after' }
  );
  
  if (result) {
    await logActivity({
      userId: userObjectId,
      action: 'client_tags_removed',
      resourceType: 'client',
      resourceId: clientObjectId,
      details: {
        description: `Tags removed from client '${result.value?.name || 'Unknown'}'`,
        metadata: { tags }
      }
    });
  }
  
  return result?.value || null;
}

// Export clients to CSV format
export async function exportClientsToCSV(userId: string | ObjectId): Promise<string> {
  const { clients } = await getUserClients(userId, { limit: 1000 });
  
  const headers = [
    'Name',
    'Email',
    'Phone',
    'Address',
    'City',
    'Contact Person',
    'Business Type',
    'Status',
    'Total Invoiced',
    'Invoice Count',
    'Average Invoice',
    'Last Invoice Date',
    'Created Date'
  ];
  
  const csvRows = [
    headers.join(','),
    ...clients.map(client => [
      `"${client.name || ''}"`,
      `"${client.email || ''}"`,
      `"${client.phone || ''}"`,
      `"${client.address || ''}"`,
      `"${client.city || ''}"`,
      `"${client.contactPerson || ''}"`,
      `"${client.businessType || ''}"`,
      `"${client.status}"`,
      client.totalInvoiced.toString(),
      client.invoiceCount.toString(),
      client.averageInvoiceAmount.toFixed(2),
      client.lastInvoiceDate ? client.lastInvoiceDate.toISOString().split('T')[0] : '',
      client.createdAt.toISOString().split('T')[0]
    ].join(','))
  ];
  
  return csvRows.join('\n');
}