'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  FileText,
  <PERSON>rkles,
  ArrowRight,
  Briefcase,
  Camera,
  Code,
  Hammer,
  Heart,
  GraduationCap,
  ShoppingBag,
  Music,
  Home,
  PaintBucket,
  Check,
  Loader2,
  Lightbulb,
  Filter,
  Star,
  Zap,
  Eye,
  Bookmark,
  TrendingUp,
  Shield,
  Clock,
  Users,
  Award,
  X
} from 'lucide-react'
import { Button } from '../../components/ui/Button'
import { Input } from '../../components/ui/Input'
import { Card } from '../../components/ui/Card'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { invoiceTemplates, searchTemplates, getTemplatesByIndustry } from '../../lib/template-data'
import { generateCustomTemplate, simulateAIProcessing, CustomizedTemplate } from '../../lib/ai-template-generator-real'
import { getAllTemplates, getTemplateById, TemplateDefinition } from '../../lib/templates/template-definitions'
import { generatePreview } from '../../lib/template-renderer'

// Icon mapping for templates
const templateIcons = {
  'professional': Briefcase,
  'modern': PaintBucket,
  'freelancer': Code,
  'photography': Camera,
  'consulting': GraduationCap,
  'service-business': Hammer
}

const templateColors = {
  'professional': 'bg-blue-500',
  'modern': 'bg-purple-500',
  'freelancer': 'bg-green-500',
  'photography': 'bg-pink-500',
  'consulting': 'bg-indigo-500',
  'service-business': 'bg-yellow-600'
}

// Get real templates from template definitions
const realTemplates = getAllTemplates();

// Convert template data to display format
const templates = realTemplates.map(template => ({
  ...template,
  icon: templateIcons[template.id as keyof typeof templateIcons] || FileText,
  color: templateColors[template.id as keyof typeof templateColors] || 'bg-gray-500',
  features: [
    template.styling.layout === 'two-column' ? 'Two-column layout' : 'Modern layout',
    'Professional styling',
    'Customizable fields',
    'Print-ready format'
  ],
  isPopular: ['professional', 'freelancer', 'photography'].includes(template.id),
  isNew: template.id === 'modern',
  // Add preview functionality
  previewHtml: null as string | null
}))

const placeholderExamples = [
  'Wedding photography business',
  'Freelance web developer', 
  'Dog walking service',
  'Consulting firm',
  'Graphic design studio'
]

// Template Preview Component
const TemplatePreview = ({ template, onUseTemplate }: { template: any; onUseTemplate: (id: string) => void }) => {
  const [previewHtml, setPreviewHtml] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showFullPreview, setShowFullPreview] = useState(false)
  
  useEffect(() => {
    const loadPreview = async () => {
      try {
        setIsLoading(true)
        // Use the preview API endpoint
        const response = await fetch(`/api/templates/preview?template=${template.id}&type=thumbnail`)
        if (response.ok) {
          const html = await response.text()
          setPreviewHtml(html)
        } else {
          console.error('Failed to load template preview')
        }
      } catch (error) {
        console.error('Error loading template preview:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    loadPreview()
  }, [template.id])
  
  if (isLoading) {
    return (
      <div className="mb-4 p-6 bg-gray-50 rounded-lg relative overflow-hidden border-2 border-gray-100 h-48 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-500">Loading preview...</p>
        </div>
      </div>
    )
  }
  
  return (
    <>
      <div className="mb-4 p-3 bg-gray-50 rounded-lg relative overflow-hidden border-2 border-gray-100 hover:border-gray-200 transition-colors cursor-pointer"
           onClick={() => setShowFullPreview(true)}>
        <div className={`absolute top-0 left-0 w-full h-2 ${template.color}`} />
        
        {previewHtml ? (
          <div className="h-40 overflow-hidden">
            <iframe 
              srcDoc={previewHtml}
              width="100%"
              height="400"
              style={{ 
                border: 'none', 
                transform: 'scale(0.8)', 
                transformOrigin: 'top left',
                width: '125%',
                pointerEvents: 'none'
              }}
            />
          </div>
        ) : (
          <div className="h-40 flex items-center justify-center">
            <p className="text-sm text-gray-500">Preview unavailable</p>
          </div>
        )}
        
        <div className="absolute bottom-2 right-2 opacity-20">
          <template.icon className="w-6 h-6 text-gray-400" />
        </div>
        
        <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded-lg">
          <div className="text-white text-center">
            <Eye className="w-6 h-6 mx-auto mb-1" />
            <p className="text-sm">Click to preview</p>
          </div>
        </div>
      </div>
      
      {/* Full Preview Modal */}
      {showFullPreview && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4"
             onClick={() => setShowFullPreview(false)}>
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto"
               onClick={(e) => e.stopPropagation()}>
            <div className="sticky top-0 bg-white border-b p-4 flex justify-between items-center">
              <h3 className="text-lg font-semibold">{template.name} Preview</h3>
              <button 
                onClick={() => setShowFullPreview(false)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4">
              {previewHtml && (
                <iframe 
                  srcDoc={previewHtml.replace('transform: scale(0.3)', 'transform: scale(1)')}
                  width="100%"
                  height="800"
                  style={{ border: 'none' }}
                />
              )}
            </div>
            <div className="sticky bottom-0 bg-white border-t p-4 flex justify-end gap-3">
              <Button 
                onClick={() => setShowFullPreview(false)}
                variant="outline"
              >
                Close
              </Button>
              <Button 
                onClick={() => {
                  setShowFullPreview(false)
                  // Trigger the use template action
                  onUseTemplate(template.id)
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <ArrowRight className="w-4 h-4 mr-2" />
                Use This Template
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

const industryFilters = [
  'All Industries',
  'business',
  'creative', 
  'freelance',
  'professional',
  'service'
]

export default function TemplatesContent() {
  const [businessDescription, setBusinessDescription] = useState('')
  const [currentPlaceholder, setCurrentPlaceholder] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationStage, setGenerationStage] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [generatedTemplate, setGeneratedTemplate] = useState<CustomizedTemplate | null>(null)
  const [selectedIndustry, setSelectedIndustry] = useState('All Industries')
  const [savedTemplates, setSavedTemplates] = useState<string[]>([])
  const router = useRouter()
  const { data: session } = useSession()

  // Rotate placeholder examples
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPlaceholder((prev) => (prev + 1) % placeholderExamples.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  // Filter templates based on selected industry
  const filteredTemplates = templates.filter(template => {
    if (selectedIndustry === 'All Industries') return true
    return template.category.toLowerCase() === selectedIndustry.toLowerCase() ||
           template.name.toLowerCase().includes(selectedIndustry.toLowerCase())
  })

  const handleGenerateTemplate = async () => {
    if (!businessDescription.trim()) return

    setIsGenerating(true)
    setGenerationStage('Analyzing your business type...')
    
    try {
      // Simulate AI processing with realistic stages
      await simulateAIProcessing(businessDescription)
      setGenerationStage('Identifying best template match...')
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setGenerationStage('Customizing template for your needs...')
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // Generate custom template using AI logic
      const customTemplate = await generateCustomTemplate(businessDescription)
      
      setGenerationStage('Finalizing your custom template...')
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Set the generated template
      setGeneratedTemplate(customTemplate)
      setSelectedTemplate(customTemplate.id)
      setSelectedIndustry(customTemplate.industry)
      
    } catch (error) {
      console.error('Error generating template:', error)
      setGenerationStage('Generation failed. Please try again.')
      setTimeout(() => {
        setGenerationStage('')
      }, 3000)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleUseTemplate = (templateId: string) => {
    // Store template selection in localStorage for persistence
    localStorage.setItem('selectedTemplate', templateId)
    
    // If it's a generated template, store the customizations
    if (generatedTemplate && templateId === generatedTemplate.id) {
      localStorage.setItem('templateCustomizations', JSON.stringify(generatedTemplate.customizations))
      localStorage.setItem('generatedTemplateData', JSON.stringify(generatedTemplate))
    }
    
    router.push(`/create?template=${templateId}`)
  }

  const handleSaveTemplate = (templateId: string) => {
    setSavedTemplates(prev => 
      prev.includes(templateId) 
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    )
  }

  const resetGeneration = () => {
    setSelectedTemplate(null)
    setGeneratedTemplate(null)
    setBusinessDescription('')
    setSelectedIndustry('All Industries')
    setGenerationStage('')
  }

  return (
    <main className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-16 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Invoice Templates
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Choose from our professional collection of templates for your business
            </p>
          </motion.div>
        </div>
      </section>

      {/* AI Template Generator Section */}
      <section className="py-12 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="max-w-4xl mx-auto"
          >
            <Card className="p-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full mb-4">
                  <Sparkles className="w-10 h-10 text-purple-600" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">
                  Template Finder
                </h2>
                <p className="text-gray-600 text-lg">
                  Describe your business and we'll suggest the best template for you
                </p>
              </div>

              <div className="space-y-6">
                <div className="relative">
                  <Input
                    value={businessDescription}
                    onChange={(e) => setBusinessDescription(e.target.value)}
                    placeholder={`e.g., ${placeholderExamples[currentPlaceholder]}`}
                    className="text-lg p-6 border-2 border-gray-200 focus:border-purple-500 transition-colors"
                    onKeyPress={(e) => e.key === 'Enter' && handleGenerateTemplate()}
                    disabled={isGenerating}
                  />
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                    <Zap className="w-5 h-5" />
                  </div>
                </div>
                
                {/* Generated Template Preview */}
                {generatedTemplate && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg border-2 border-green-200"
                  >
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                          <Check className="w-6 h-6 text-green-600" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          ✨ Custom Template Generated!
                        </h3>
                        <p className="text-sm text-gray-600 mb-3">
                          <strong>{generatedTemplate.name}</strong> - {generatedTemplate.description}
                        </p>
                        <div className="grid grid-cols-2 gap-4 text-xs text-gray-600 mb-4">
                          <div>
                            <strong>Optimized for:</strong> {generatedTemplate.industry}
                          </div>
                          <div>
                            <strong>Layout:</strong> {generatedTemplate.styling.layout}
                          </div>
                        </div>
                        <div className="flex gap-3">
                          <Button
                            onClick={() => handleUseTemplate(generatedTemplate.id)}
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            <ArrowRight className="w-4 h-4 mr-2" />
                            Use This Custom Template
                          </Button>
                          <Button
                            onClick={resetGeneration}
                            variant="outline"
                            size="sm"
                          >
                            Try Different Business
                          </Button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {!generatedTemplate && (
                  <div className="flex gap-4">
                    <Button
                      onClick={handleGenerateTemplate}
                      disabled={!businessDescription.trim() || isGenerating}
                      className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-4 text-lg font-semibold shadow-lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                          {generationStage}
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-5 h-5 mr-2" />
                          Generate My Template
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Template Gallery */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {/* Section Header with Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-center mb-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {generatedTemplate ? 'Your Custom Template & More Options' : 'Professional Templates'}
            </h2>
            <p className="text-lg text-gray-600 mb-6">
              {generatedTemplate 
                ? 'You can also browse our full collection of templates below' 
                : 'Choose from our collection of industry-specific templates'
              }
            </p>
            
            {/* Industry Filter */}
            <div className="flex flex-wrap justify-center gap-2 mb-6">
              {industryFilters.map((industry) => (
                <Button
                  key={industry}
                  onClick={() => setSelectedIndustry(industry)}
                  variant={selectedIndustry === industry ? "primary" : "outline"}
                  size="sm"
                  className={selectedIndustry === industry ? 
                    "bg-purple-600 hover:bg-purple-700" : ""
                  }
                >
                  <Filter className="w-3 h-3 mr-1" />
                  {industry}
                </Button>
              ))}
            </div>
          </motion.div>

          {/* All Templates */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template, index) => (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
              >
                <Card
                  className={`h-full hover:shadow-xl transition-all duration-300 cursor-pointer ${
                    selectedTemplate === template.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <div className="p-6">
                    {/* Template Badges */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex gap-1">
                        {template.isPopular && (
                          <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full font-medium">
                            <Star className="w-3 h-3 inline mr-1" />
                            Popular
                          </span>
                        )}
                        {template.isNew && (
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                            New
                          </span>
                        )}
                      </div>
                      <Button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleSaveTemplate(template.id)
                        }}
                        variant="ghost"
                        size="sm"
                        className="p-1"
                      >
                        <Bookmark className={`w-4 h-4 ${savedTemplates.includes(template.id) ? 'fill-current text-blue-600' : 'text-gray-400'}`} />
                      </Button>
                    </div>
                    
                    {/* Template Preview */}
                    <TemplatePreview template={template} onUseTemplate={handleUseTemplate} />

                    {/* Template Info */}
                    <div className="space-y-3">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {template.name}
                        </h3>
                        <p className="text-sm text-gray-500">{template.category}</p>
                      </div>
                      <p className="text-sm text-gray-600">
                        {template.description}
                      </p>

                      {/* Features */}
                      <div className="space-y-1">
                        {template.features.slice(0, 3).map((feature, idx) => (
                          <div key={idx} className="flex items-center gap-2">
                            <Check className="w-3 h-3 text-green-500" />
                            <span className="text-xs text-gray-600">{feature}</span>
                          </div>
                        ))}
                      </div>

                      {/* Actions */}
                      <div className="pt-3">
                        <Button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleUseTemplate(template.id)
                          }}
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          Use This Template
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                        {/* Preview button removed */}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* No Results */}
          {filteredTemplates.length === 0 && (
            <div className="text-center py-12">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No templates found
              </h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your filter or try different search terms
              </p>
              <Button
                onClick={() => setSelectedIndustry('All Industries')}
                variant="outline"
              >
                Show All Templates
              </Button>
            </div>
          )}
        </div>
      </section>
    </main>
  )
}
