import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Check if AI is configured
    const hasOpenAIKey = !!process.env.OPENAI_API_KEY;
    
    if (!hasOpenAIKey) {
      return NextResponse.json(
        { configured: false, message: 'OpenAI API key not configured' },
        { status: 503 }
      );
    }

    // Simple test to verify AI service is accessible
    // In production, you might want to make a minimal API call
    return NextResponse.json({
      configured: true,
      message: 'AI service is configured',
      provider: 'OpenAI'
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'AI test failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}