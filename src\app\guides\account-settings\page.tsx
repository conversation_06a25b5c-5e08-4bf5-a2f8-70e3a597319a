import type { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { <PERSON>R<PERSON>, Settings, User, Shield, Bell, CreditCard, Mail, Globe } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Account Settings and Preferences - Configuration Guide',
  description: 'Configure your Template Invoice account for optimal workflow. Settings, preferences, and account management.',
  keywords: ['invoice account settings', 'invoice preferences', 'account configuration', 'user settings', 'invoice customization'],
  openGraph: {
    title: 'Account Settings and Preferences - Configuration Guide',
    description: 'Configure your Template Invoice account for optimal workflow. Settings, preferences, and account management.',
    type: 'article',
  },
}

export default function AccountSettingsGuide() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-slate-600 to-gray-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Account Settings & Preferences
            </h1>
            <p className="text-xl text-slate-100 max-w-3xl mx-auto">
              Configure your Template Invoice account for optimal workflow and security
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-gray-900">Home</Link>
          <span>/</span>
          <Link href="/guides" className="hover:text-gray-900">Guides</Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">Account Settings</span>
        </nav>

        {/* Profile Settings */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4 flex items-center">
            <User className="w-8 h-8 mr-3 text-slate-600" />
            Profile Settings
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Your profile information appears on invoices and communications. Keep it updated and professional.
          </p>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-xl font-semibold mb-4">Business Information</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
                  <p className="text-sm text-gray-600">This appears as the sender on all invoices</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                  <p className="text-sm text-gray-600">Primary email for notifications and client communications</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                  <p className="text-sm text-gray-600">Displayed on invoices for client contact</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Business Address</label>
                  <p className="text-sm text-gray-600">Complete address for professional invoices</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tax ID / EIN</label>
                  <p className="text-sm text-gray-600">Required for business tax documentation</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Website URL</label>
                  <p className="text-sm text-gray-600">Optional link to your business website</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="font-semibold text-blue-800 mb-2">Profile Completeness</h3>
            <p className="text-blue-700">
              Complete profiles get paid faster! Clients trust businesses with professional, 
              complete contact information. Aim for 100% profile completion.
            </p>
          </div>
        </section>

        {/* Invoice Preferences */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4 flex items-center">
            <Settings className="w-8 h-8 mr-3 text-slate-600" />
            Invoice Preferences
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Customize default settings to speed up your invoicing workflow and maintain consistency.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Default Invoice Settings</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Payment Terms</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Net 30 (payment due in 30 days)</li>
                    <li>• Net 15 (payment due in 15 days)</li>
                    <li>• Due on receipt</li>
                    <li>• Custom terms (Net 45, Net 60, etc.)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Invoice Numbering</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Auto-increment numbers (001, 002, 003)</li>
                    <li>• Custom prefix (INV-001, 2024-001)</li>
                    <li>• Date-based numbering (240101-001)</li>
                    <li>• Manual numbering</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Currency and Localization</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Currency Settings</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Default currency (USD, EUR, GBP, etc.)</li>
                    <li>• Currency symbol placement</li>
                    <li>• Decimal places for amounts</li>
                    <li>• Multi-currency support</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Regional Settings</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Date format (MM/DD/YYYY, DD/MM/YYYY)</li>
                    <li>• Number formatting (1,000.00 vs 1.000,00)</li>
                    <li>• Time zone preferences</li>
                    <li>• Language for invoice text</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Notification Settings */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4 flex items-center">
            <Bell className="w-8 h-8 mr-3 text-slate-600" />
            Notification Preferences
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Stay informed about important events while avoiding notification overload. Customize when 
            and how you receive updates.
          </p>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-semibold mb-4">Email Notifications</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <h4 className="font-medium">Payment Received</h4>
                  <p className="text-sm text-gray-600">When a client pays an invoice</p>
                </div>
                <label className="switch">
                  <input type="checkbox" defaultChecked />
                  <span className="text-green-600">Enabled</span>
                </label>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <h4 className="font-medium">Payment Failed</h4>
                  <p className="text-sm text-gray-600">When a payment attempt fails</p>
                </div>
                <label className="switch">
                  <input type="checkbox" defaultChecked />
                  <span className="text-green-600">Enabled</span>
                </label>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <h4 className="font-medium">Invoice Overdue</h4>
                  <p className="text-sm text-gray-600">When an invoice passes its due date</p>
                </div>
                <label className="switch">
                  <input type="checkbox" defaultChecked />
                  <span className="text-green-600">Enabled</span>
                </label>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <h4 className="font-medium">Weekly Summary</h4>
                  <p className="text-sm text-gray-600">Weekly digest of invoice activity</p>
                </div>
                <label className="switch">
                  <input type="checkbox" />
                  <span className="text-gray-400">Disabled</span>
                </label>
              </div>
            </div>
          </div>
        </section>

        {/* Security Settings */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4 flex items-center">
            <Shield className="w-8 h-8 mr-3 text-slate-600" />
            Security & Privacy
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Protect your account and sensitive business information with these security features.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Account Security</h3>
              <div className="space-y-4">
                <div className="border border-gray-200 rounded p-4">
                  <h4 className="font-semibold mb-2">Two-Factor Authentication (2FA)</h4>
                  <p className="text-gray-700 text-sm mb-3">
                    Add an extra layer of security to your account with 2FA via SMS or authenticator app.
                  </p>
                  <button className="bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700">
                    Enable 2FA
                  </button>
                </div>
                
                <div className="border border-gray-200 rounded p-4">
                  <h4 className="font-semibold mb-2">Password Management</h4>
                  <p className="text-gray-700 text-sm mb-3">
                    Change your password regularly and use a strong, unique password.
                  </p>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">
                    Change Password
                  </button>
                </div>
                
                <div className="border border-gray-200 rounded p-4">
                  <h4 className="font-semibold mb-2">Login History</h4>
                  <p className="text-gray-700 text-sm mb-3">
                    Monitor recent login activity and active sessions.
                  </p>
                  <Link href="/settings/security" className="text-blue-600 hover:underline text-sm">
                    View Login History
                  </Link>
                </div>
              </div>
            </div>

            <div className="bg-red-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3 text-red-800">Data Backup & Export</h3>
              <p className="text-red-700 mb-4">
                Regularly backup your invoice data and client information. We recommend monthly exports.
              </p>
              <div className="space-x-4">
                <button className="bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700">
                  Export All Data
                </button>
                <button className="bg-white text-red-600 border border-red-600 px-4 py-2 rounded text-sm hover:bg-red-50">
                  Schedule Auto-Backup
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Integration Settings */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4 flex items-center">
            <Globe className="w-8 h-8 mr-3 text-slate-600" />
            Integrations & API
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Connect Template Invoice with your favorite business tools for a seamless workflow.
          </p>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Popular Integrations</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border border-gray-200 rounded">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 rounded mr-3"></div>
                    <span className="font-medium">QuickBooks</span>
                  </div>
                  <button className="text-blue-600 hover:underline text-sm">Connect</button>
                </div>
                <div className="flex items-center justify-between p-3 border border-gray-200 rounded">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-green-100 rounded mr-3"></div>
                    <span className="font-medium">Stripe</span>
                  </div>
                  <span className="text-green-600 text-sm">Connected</span>
                </div>
                <div className="flex items-center justify-between p-3 border border-gray-200 rounded">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-purple-100 rounded mr-3"></div>
                    <span className="font-medium">Zapier</span>
                  </div>
                  <button className="text-blue-600 hover:underline text-sm">Connect</button>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">API Access</h3>
              <p className="text-gray-700 text-sm mb-4">
                For developers: Access the Template Invoice API to build custom integrations.
              </p>
              <div className="space-y-3">
                <div className="border border-gray-200 rounded p-3">
                  <h4 className="font-medium text-sm">API Key</h4>
                  <p className="text-xs text-gray-600 font-mono">sk_live_••••••••••••••••</p>
                </div>
                <button className="bg-gray-600 text-white px-4 py-2 rounded text-sm hover:bg-gray-700 w-full">
                  Generate New API Key
                </button>
                <Link href="/api/docs" className="block text-center text-blue-600 hover:underline text-sm">
                  View API Documentation
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Billing Settings */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4 flex items-center">
            <CreditCard className="w-8 h-8 mr-3 text-slate-600" />
            Billing & Subscription
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Manage your Template Invoice subscription and billing information.
          </p>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-semibold mb-4">Current Plan</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <div className="border border-green-200 bg-green-50 rounded-lg p-4">
                  <h4 className="font-semibold text-green-800 mb-2">Pro Plan</h4>
                  <p className="text-green-700 text-sm mb-3">
                    Unlimited invoices, advanced features, priority support
                  </p>
                  <p className="text-green-800 font-semibold">$29/month</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Next Billing Date</h4>
                  <p className="text-gray-700">March 15, 2024</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Payment Method</h4>
                  <p className="text-gray-700">•••• •••• •••• 4242 (Visa)</p>
                </div>
                <div className="space-x-2">
                  <Link href="/upgrade" className="inline-block bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">
                    Upgrade Plan
                  </Link>
                  <button className="bg-gray-600 text-white px-4 py-2 rounded text-sm hover:bg-gray-700">
                    Update Payment
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-slate-600 to-gray-600 rounded-lg p-8 text-white text-center">
          <h2 className="text-2xl font-bold mb-4">Optimize Your Account Settings</h2>
          <p className="mb-6">Configure your preferences for the best invoicing experience.</p>
          <Link href="/settings" className="inline-flex items-center bg-white text-slate-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Go to Settings
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
        </div>

        {/* Related Guides */}
        <div className="mt-12">
          <h3 className="text-xl font-semibold mb-4">Related Guides</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <Link href="/guides/payment-processing" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <h4 className="font-semibold mb-2">Payment Processing Setup</h4>
              <p className="text-sm text-gray-600">Configure Stripe and payment methods</p>
            </Link>
            <Link href="/guides/customizing-templates" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <h4 className="font-semibold mb-2">Template Customization</h4>
              <p className="text-sm text-gray-600">Brand your invoices with custom designs</p>
            </Link>
          </div>
        </div>
      </div>
    </main>
  )
}