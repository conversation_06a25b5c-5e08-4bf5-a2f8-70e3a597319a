'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Lock, CreditCard, RefreshCw, Globe2, DollarSign, Zap, Shield, BarChart3, FileText, Users } from 'lucide-react'

const features = [
  {
    name: "Google Sign-In",
    icon: Lock,
    desc: "Easy, secure login with your Google account. No password hassles.",
    color: "from-blue-500 to-blue-600"
  },
  {
    name: "Stripe Payments",
    icon: CreditCard,
    desc: "Accept card payments instantly. Track paid and unpaid invoices.",
    color: "from-purple-500 to-purple-600"
  },
  {
    name: "Recurring Billing",
    icon: RefreshCw,
    desc: "Auto-send invoices for repeat clients. Never miss a payment cycle.",
    color: "from-green-500 to-green-600"
  },
  {
    name: "Multi-Currency",
    icon: Globe2,
    desc: "Bill clients globally in 50+ currencies, with automatic exchange rates.",
    color: "from-indigo-500 to-indigo-600"
  },
  {
    name: "Tax Compliance",
    icon: DollarSign,
    desc: "Auto-calculate sales tax and VAT based on your location.",
    color: "from-yellow-500 to-yellow-600"
  },
  {
    name: "Smart Automation",
    icon: Zap,
    desc: "Automate recurring invoices, payment reminders, and follow-ups.",
    color: "from-orange-500 to-orange-600"
  },
  {
    name: "Bank Security",
    icon: Shield,
    desc: "256-bit encryption and compliance with global standards.",
    color: "from-red-500 to-red-600"
  },
  {
    name: "Analytics",
    icon: BarChart3,
    desc: "Comprehensive dashboards and predictive revenue forecasting.",
    color: "from-teal-500 to-teal-600"
  },
]

export default function FeatureTabs() {
  const [selected, setSelected] = useState(0)
  const Feature = features[selected]

  return (
    <section className="py-16">
      <motion.h2 
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        className="text-3xl sm:text-4xl font-bold text-center mb-8"
      >
        Powerful Features, Seamlessly Integrated
      </motion.h2>
      
      {/* Scrollable Tab Container */}
      <div className="relative">
        {/* Gradient overlays for scroll indication */}
        <div className="absolute left-0 top-0 bottom-0 w-12 bg-gradient-to-r from-white via-white to-transparent z-10 pointer-events-none md:hidden" />
        <div className="absolute right-0 top-0 bottom-0 w-12 bg-gradient-to-l from-white via-white to-transparent z-10 pointer-events-none md:hidden" />
        
        <div className="flex space-x-2 overflow-x-auto pb-2 justify-start md:justify-center scrollbar-hide px-4 md:px-0">
          {features.map((f, i) => {
            const Icon = f.icon
            return (
              <button
                key={f.name}
                className={`flex items-center gap-2 px-4 sm:px-6 py-3 rounded-full border whitespace-nowrap
                ${i === selected
                  ? "bg-black text-white border-black shadow-lg scale-105"
                  : "bg-white/70 text-black border-gray-200 hover:bg-black/5 hover:border-gray-300"
                } font-semibold transition-all duration-200 min-w-[140px] sm:min-w-[180px]`}
                onClick={() => setSelected(i)}
                aria-selected={i === selected}
                aria-controls={`feature-panel-${i}`}
                role="tab"
              >
                <Icon className="w-5 h-5 flex-shrink-0" />
                <span className="text-sm sm:text-base">{f.name}</span>
              </button>
            )
          })}
        </div>
      </div>
      
      {/* Feature Content */}
      <div className="mt-8 min-h-[200px] relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={features[selected].name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.45, ease: "easeOut" }}
            id={`feature-panel-${selected}`}
            className="flex flex-col items-center justify-center gap-4 p-8 bg-white/70 backdrop-blur-lg rounded-2xl shadow-xl border border-gray-100 mx-auto max-w-2xl"
            role="tabpanel"
            aria-live="polite"
          >
            <motion.div 
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2 }}
              className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${Feature.color} p-3 mb-2 shadow-lg`}
            >
              <Feature.icon className="w-full h-full text-white" />
            </motion.div>
            <h3 className="text-xl font-semibold mb-2">{Feature.name}</h3>
            <p className="text-gray-700 text-center text-base max-w-md">{Feature.desc}</p>
            
            {/* Optional: Add a CTA button */}
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="mt-4 px-6 py-2 bg-black text-white rounded-full text-sm font-medium hover:bg-gray-900 transition-colors"
            >
              Learn More →
            </motion.button>
          </motion.div>
        </AnimatePresence>
      </div>
      
      {/* Mobile scroll indicator */}
      <div className="flex justify-center mt-4 md:hidden">
        <div className="flex space-x-1">
          {features.map((_, i) => (
            <div
              key={i}
              className={`h-1 rounded-full transition-all duration-300 ${
                i === selected ? 'w-8 bg-black' : 'w-2 bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>
    </section>
  )
}