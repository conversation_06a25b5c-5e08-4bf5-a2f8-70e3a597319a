import { authOptions } from "@/lib/auth"
import NextAuth from "next-auth"

console.log("🔍 [AUTH] NextAuth endpoint hit")
console.log("🔍 [AUTH] NEXTAUTH_URL:", process.env.NEXTAUTH_URL)
console.log("🔍 [AUTH] MongoDB URI exists:", !!process.env.MONGODB_URI)
console.log("🔍 [AUTH] Google Client ID exists:", !!process.env.GOOGLE_CLIENT_ID)

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST }