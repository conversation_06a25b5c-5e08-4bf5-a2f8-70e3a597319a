'use client'

import { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { AlertCircle } from 'lucide-react'

function ErrorContent() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')
  
  // Get detailed error information for debugging
  const errorMessages: Record<string, string> = {
    'Configuration': 'There is a problem with the server configuration.',
    'AccessDenied': 'Access was denied. You may have declined the authentication request.',
    'Verification': 'The token verification failed.',
    'OAuthSignin': 'Error occurred while starting the OAuth signin flow.',
    'OAuthCallback': 'Error in handling the OAuth callback. Check redirect URIs.',
    'OAuthCreateAccount': 'Could not create OAuth provider user in the database.',
    'EmailCreateAccount': 'Could not create email provider user in the database.',
    'Callback': 'Error occurred in the OAuth callback handler route.',
    'OAuthAccountNotLinked': 'Email already exists with a different provider.',
    'EmailSignin': 'The email could not be sent.',
    'CredentialsSignin': 'Sign in failed. Check the details you provided are correct.',
    'SessionRequired': 'Please sign in to access this page.',
    'Default': 'An unexpected error occurred.'
  }
  
  const errorMessage = error ? (errorMessages[error] || errorMessages['Default']) : errorMessages['Default']

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-lg w-full bg-white shadow-lg rounded-lg p-8">
        <div className="text-center mb-6">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <h1 className="mt-4 text-2xl font-bold text-gray-900">Authentication Error</h1>
        </div>
        
        {/* Error Details */}
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-sm font-medium text-red-800 mb-2">
            Error Code: <span className="font-mono">{error || 'Unknown'}</span>
          </p>
          <p className="text-sm text-red-700">{errorMessage}</p>
        </div>
        
        {/* Debug Information (for development) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4 mb-6">
            <h3 className="text-sm font-medium text-gray-800 mb-2">Debug Information:</h3>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Check Google OAuth Console redirect URIs</li>
              <li>• Verify NEXTAUTH_URL matches your current URL</li>
              <li>• Ensure GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are set</li>
              <li>• Check MongoDB connection string</li>
            </ul>
          </div>
        )}
        
        {/* Common Solutions */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-800 mb-2">Common Solutions:</h3>
          <ul className="text-sm text-gray-600 space-y-2">
            {error === 'OAuthCallback' && (
              <>
                <li>• Verify redirect URI in Google Console includes: <code className="text-xs bg-gray-100 px-1 py-0.5 rounded">http://localhost:3000/api/auth/callback/google</code></li>
                <li>• Check that NEXTAUTH_URL in .env.local is set to: <code className="text-xs bg-gray-100 px-1 py-0.5 rounded">http://localhost:3000</code></li>
              </>
            )}
            {error === 'OAuthCreateAccount' && (
              <li>• Check MongoDB connection and user permissions</li>
            )}
            <li>• Clear browser cookies and try again</li>
            <li>• Try using an incognito/private browser window</li>
          </ul>
        </div>
        
        {/* Action Buttons */}
        <div className="flex items-center justify-center space-x-4">
          <Link 
            href="/auth/signin"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Try Sign In Again
          </Link>
          
          <Link 
            href="/"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Return to Homepage
          </Link>
        </div>
      </div>
    </div>
  )
}

export default function AuthErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p className="mt-2 text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <ErrorContent />
    </Suspense>
  )
}
