'use client'

import { motion } from 'framer-motion'
import { 
  PenTool, 
  Code, 
  Camera, 
  Wrench, 
  GraduationCap, 
  ShoppingBag,
  Home,
  Users,
  TrendingUp,
  FileText,
  DollarSign,
  Clock
} from 'lucide-react'

// Primary target industries without existing invoicing systems
export const TARGET_INDUSTRIES = {
  freelancers: {
    id: 'freelancers',
    title: 'Freelancers',
    icon: PenTool,
    description: 'Writers, Developers, Designers, Virtual Assistants',
    painPoints: [
      'Using Word documents or Excel spreadsheets',
      'Manually tracking hours and projects',
      'No professional invoice branding',
      'Difficulty getting paid on time'
    ],
    typicalRevenue: '$30K - $100K annually',
    averageInvoice: '$500 - $5,000',
    paymentCycle: '30-60 days',
    commonServices: [
      'Content Writing ($0.10-$1.00 per word)',
      'Web Development ($50-$150 per hour)',
      'Graphic Design ($40-$100 per hour)',
      'Virtual Assistant ($15-$50 per hour)'
    ],
    currentSolutions: [
      'Excel spreadsheets (45%)',
      'Word document templates (30%)',
      'Basic apps like Wave/FreshBooks (20%)',
      'No system at all (5%)'
    ]
  },
  
  creative: {
    id: 'creative',
    title: 'Creative Services',
    icon: Camera,
    description: 'Photography, Videography, Graphic Design, Web Design',
    painPoints: [
      'Complex project packages hard to invoice',
      'Usage rights and licensing confusion',
      'Travel and equipment cost tracking',
      'Rush job pricing inconsistency'
    ],
    typicalRevenue: '$40K - $150K annually',
    averageInvoice: '$1,000 - $8,000',
    paymentCycle: '15-45 days',
    commonServices: [
      'Wedding Photography ($2,000-$6,000 per event)',
      'Logo Design ($500-$3,000 per project)',
      'Video Production ($100-$300 per hour)',
      'Website Design ($2,000-$15,000 per project)'
    ],
    currentSolutions: [
      'Industry-specific software (35%)',
      'Excel/Word templates (40%)',
      'Paper contracts with manual invoicing (15%)',
      'Basic invoicing apps (10%)'
    ]
  },
  
  trades: {
    id: 'trades',
    title: 'Small Trades',
    icon: Wrench,
    description: 'Handyman, Cleaning, Landscaping, Personal Trainers',
    painPoints: [
      'Materials markup calculation',
      'Travel time and mileage tracking',
      'Service call vs project pricing',
      'Seasonal business fluctuations'
    ],
    typicalRevenue: '$35K - $80K annually',
    averageInvoice: '$200 - $2,500',
    paymentCycle: '0-30 days (often immediate)',
    commonServices: [
      'Handyman Services ($40-$80 per hour)',
      'House Cleaning ($25-$50 per hour)',
      'Landscaping ($35-$75 per hour)',
      'Personal Training ($30-$100 per session)'
    ],
    currentSolutions: [
      'Paper invoices and receipts (50%)',
      'Simple Square/PayPal invoicing (25%)',
      'Excel spreadsheets (20%)',
      'No formal invoicing (5%)'
    ]
  },
  
  local_services: {
    id: 'local_services',
    title: 'Local Services',
    icon: GraduationCap,
    description: 'Tutoring, Pet Care, Event Planning, Catering',
    painPoints: [
      'Package vs hourly rate confusion',
      'Client scheduling and billing alignment',
      'Add-on services pricing',
      'Seasonal demand variations'
    ],
    typicalRevenue: '$25K - $75K annually',
    averageInvoice: '$100 - $1,500',
    paymentCycle: '0-15 days',
    commonServices: [
      'Tutoring ($25-$75 per hour)',
      'Pet Sitting ($25-$50 per day)',
      'Event Planning ($500-$5,000 per event)',
      'Catering ($15-$50 per person)'
    ],
    currentSolutions: [
      'Cash or check payments (40%)',
      'Venmo/PayPal casual invoicing (35%)',
      'Simple apps like Square (20%)',
      'No formal system (5%)'
    ]
  },
  
  ecommerce: {
    id: 'ecommerce',
    title: 'E-commerce/Retail',
    icon: ShoppingBag,
    description: 'Online stores, Local shops, Dropshipping',
    painPoints: [
      'B2B vs B2C pricing structures',
      'Inventory tracking integration',
      'Bulk order discounting',
      'Multi-channel sales reconciliation'
    ],
    typicalRevenue: '$50K - $200K annually',
    averageInvoice: '$50 - $5,000',
    paymentCycle: '0-30 days',
    commonServices: [
      'Product Sales (20-50% markup)',
      'Dropshipping (10-30% margin)',
      'Custom Orders (+20-40% premium)',
      'Wholesale Accounts (Net 30 terms)'
    ],
    currentSolutions: [
      'Platform-integrated invoicing (40%)',
      'Separate invoicing software (30%)',
      'Excel tracking (25%)',
      'Manual processes (5%)'
    ]
  },
  
  home_based: {
    id: 'home_based',
    title: 'Home-based Businesses',
    icon: Home,
    description: 'Crafts, Consulting, Coaching, Bookkeeping',
    painPoints: [
      'Mixing personal and business finances',
      'Inconsistent pricing strategies',
      'Limited professional appearance',
      'Difficulty scaling operations'
    ],
    typicalRevenue: '$20K - $60K annually',
    averageInvoice: '$100 - $2,000',
    paymentCycle: '15-30 days',
    commonServices: [
      'Handmade Crafts ($20-$200 per item)',
      'Business Consulting ($50-$200 per hour)',
      'Life Coaching ($75-$300 per session)',
      'Bookkeeping ($30-$75 per hour)'
    ],
    currentSolutions: [
      'PayPal/Venmo casual payments (45%)',
      'Excel or Google Sheets (30%)',
      'Paper receipts (15%)',
      'No formal tracking (10%)'
    ]
  }
}

export default function TargetIndustries() {
  return (
    <div className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Built for Businesses Like Yours
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We focus on small businesses and freelancers who are currently using 
            spreadsheets, Word documents, or no invoicing system at all.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {Object.values(TARGET_INDUSTRIES).map((industry, index) => {
            const Icon = industry.icon
            return (
              <motion.div
                key={industry.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Icon className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{industry.title}</h3>
                    <p className="text-sm text-gray-600">{industry.description}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  {/* Key Stats */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500 mb-1">Typical Revenue</p>
                      <p className="font-medium text-gray-900">{industry.typicalRevenue}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 mb-1">Avg Invoice</p>
                      <p className="font-medium text-gray-900">{industry.averageInvoice}</p>
                    </div>
                  </div>

                  {/* Pain Points */}
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Common Challenges:</p>
                    <ul className="space-y-1">
                      {industry.painPoints.slice(0, 2).map((point, i) => (
                        <li key={i} className="text-xs text-gray-600 flex items-start gap-1">
                          <span className="text-red-400 mt-1">•</span>
                          {point}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Current Solutions */}
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Currently Using:</p>
                    <div className="space-y-1">
                      {industry.currentSolutions.slice(0, 2).map((solution, i) => (
                        <p key={i} className="text-xs text-gray-600">{solution}</p>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Market Opportunity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center"
        >
          <h3 className="text-2xl font-bold mb-4">The Opportunity</h3>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="flex flex-col items-center">
              <Users className="w-8 h-8 mb-2" />
              <p className="text-3xl font-bold mb-1">28M+</p>
              <p className="text-blue-100">Small businesses in the US without professional invoicing</p>
            </div>
            <div className="flex flex-col items-center">
              <TrendingUp className="w-8 h-8 mb-2" />
              <p className="text-3xl font-bold mb-1">73%</p>
              <p className="text-blue-100">Use Excel, Word, or paper invoices currently</p>
            </div>
            <div className="flex flex-col items-center">
              <Clock className="w-8 h-8 mb-2" />
              <p className="text-3xl font-bold mb-1">3-5x</p>
              <p className="text-blue-100">Faster payment with professional invoicing</p>
            </div>
          </div>
        </motion.div>

        {/* Success Stories Preview */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">
            What These Businesses Say After Switching
          </h3>
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <DollarSign className="w-4 h-4 text-green-600" />
                </div>
                <p className="font-semibold text-gray-900">45% Faster Payments</p>
              </div>
              <p className="text-sm text-gray-600">
                "Went from waiting 45 days to getting paid in 25 days on average"
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="w-4 h-4 text-blue-600" />
                </div>
                <p className="font-semibold text-gray-900">Professional Image</p>
              </div>
              <p className="text-sm text-gray-600">
                "Clients comment on how professional my invoices look now"
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <Clock className="w-4 h-4 text-purple-600" />
                </div>
                <p className="font-semibold text-gray-900">Time Savings</p>
              </div>
              <p className="text-sm text-gray-600">
                "Save 2+ hours per week on invoicing and follow-ups"
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}