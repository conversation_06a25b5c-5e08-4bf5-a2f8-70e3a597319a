import nodemailer from 'nodemailer';
import { generateInvoicePDF } from './pdf-generator';
import { InvoiceData } from './templates/template-definitions';

// Email configuration interface
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// Email options interface
interface EmailOptions {
  from?: string;
  replyTo?: string;
  subject?: string;
  text?: string;
  html?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer;
    contentType: string;
  }>;
}

// Default email configuration from environment variables
const getEmailConfig = (): EmailConfig => {
  const config: EmailConfig = {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || '',
      pass: process.env.SMTP_PASS || ''
    }
  };
  
  if (!config.auth.user || !config.auth.pass) {
    throw new Error('Email configuration is incomplete. Please set SMTP_USER and SMTP_PASS environment variables.');
  }
  
  return config;
};

// Create reusable transporter
let transporter: nodemailer.Transporter | null = null;

/**
 * Get or create email transporter
 */
function getTransporter(): nodemailer.Transporter {
  if (transporter) {
    return transporter;
  }
  
  try {
    const config = getEmailConfig();
    transporter = nodemailer.createTransport(config);
    return transporter;
  } catch (error) {
    console.error('Failed to create email transporter:', error);
    throw new Error('Email service is not configured properly');
  }
}

/**
 * Send invoice email with PDF attachment
 */
export async function sendInvoiceEmail(
  templateId: string,
  invoiceData: Partial<InvoiceData>,
  recipientEmail: string,
  senderEmail: string,
  options: Partial<EmailOptions> = {}
): Promise<boolean> {
  try {
    // Generate PDF attachment
    const pdfBuffer = await generateInvoicePDF(templateId, invoiceData);
    
    // Prepare email content
    const companyName = invoiceData.companyName || 'Your Company';
    const clientName = invoiceData.clientName || 'Valued Client';
    const invoiceNumber = invoiceData.invoiceNumber || 'Invoice';
    const total = invoiceData.total || 0;
    const dueDate = invoiceData.dueDate || 'N/A';
    
    const emailSubject = options.subject || `Invoice ${invoiceNumber} from ${companyName}`;
    
    const emailText = options.text || `
Dear ${clientName},

Please find attached invoice ${invoiceNumber} for the amount of $${total}.

Payment is due by ${dueDate}.

Thank you for your business!

Best regards,
${companyName}
    `.trim();
    
    const emailHtml = options.html || `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; }
    .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
    .content { padding: 30px; }
    .invoice-details { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }
    .amount { font-size: 24px; font-weight: bold; color: #28a745; }
    .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
    .btn { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Invoice from ${companyName}</h1>
    </div>
    
    <div class="content">
      <p>Dear ${clientName},</p>
      
      <p>I hope this email finds you well. Please find attached invoice <strong>${invoiceNumber}</strong> for the services provided.</p>
      
      <div class="invoice-details">
        <h3>Invoice Details:</h3>
        <p><strong>Invoice Number:</strong> ${invoiceNumber}</p>
        <p><strong>Amount Due:</strong> <span class="amount">$${total}</span></p>
        <p><strong>Due Date:</strong> ${dueDate}</p>
      </div>
      
      <p>The PDF invoice is attached to this email for your records. Please process payment according to the terms specified in the invoice.</p>
      
      <p>If you have any questions about this invoice or need any clarification, please don't hesitate to reach out to me.</p>
      
      <p>Thank you for your business!</p>
      
      <p>Best regards,<br>
      <strong>${companyName}</strong></p>
    </div>
    
    <div class="footer">
      <p>This is an automated email. Please do not reply to this message.</p>
      <p>If you need assistance, please contact us directly.</p>
    </div>
  </div>
</body>
</html>
    `;
    
    // Get transporter
    const emailTransporter = getTransporter();
    
    // Send email with PDF attachment
    const info = await emailTransporter.sendMail({
      from: options.from || senderEmail,
      to: recipientEmail,
      replyTo: options.replyTo || senderEmail,
      subject: emailSubject,
      text: emailText,
      html: emailHtml,
      attachments: [
        {
          filename: `invoice-${invoiceNumber}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        },
        ...(options.attachments || [])
      ]
    });
    
    console.log('Email sent successfully:', info.messageId);
    return true;
    
  } catch (error) {
    console.error('Email sending failed:', error);
    return false;
  }
}

/**
 * Send custom email with optional attachments
 */
export async function sendCustomEmail(
  to: string,
  subject: string,
  content: { text?: string; html?: string },
  attachments: Array<{ filename: string; content: Buffer; contentType: string }> = [],
  from?: string
): Promise<boolean> {
  try {
    const transporter = getTransporter();
    
    const info = await transporter.sendMail({
      from: from || process.env.SMTP_USER,
      to,
      subject,
      text: content.text,
      html: content.html,
      attachments
    });
    
    console.log('Custom email sent successfully:', info.messageId);
    return true;
    
  } catch (error) {
    console.error('Custom email sending failed:', error);
    return false;
  }
}

/**
 * Send invoice reminder email
 */
export async function sendInvoiceReminder(
  templateId: string,
  invoiceData: Partial<InvoiceData>,
  recipientEmail: string,
  senderEmail: string,
  reminderType: 'gentle' | 'urgent' | 'final' = 'gentle'
): Promise<boolean> {
  const companyName = invoiceData.companyName || 'Your Company';
  const clientName = invoiceData.clientName || 'Valued Client';
  const invoiceNumber = invoiceData.invoiceNumber || 'Invoice';
  const total = invoiceData.total || 0;
  const dueDate = invoiceData.dueDate || 'N/A';
  
  const reminderMessages = {
    gentle: {
      subject: `Friendly Reminder: Invoice ${invoiceNumber} Payment Due`,
      text: `Dear ${clientName},\n\nThis is a friendly reminder that payment for invoice ${invoiceNumber} in the amount of $${total} was due on ${dueDate}.\n\nIf you have already sent payment, please disregard this message. If not, please arrange payment at your earliest convenience.\n\nThank you for your business!\n\nBest regards,\n${companyName}`
    },
    urgent: {
      subject: `Urgent: Overdue Payment for Invoice ${invoiceNumber}`,
      text: `Dear ${clientName},\n\nThis is an urgent reminder that payment for invoice ${invoiceNumber} in the amount of $${total} is now overdue (due date: ${dueDate}).\n\nPlease arrange immediate payment to avoid any late fees or service interruptions.\n\nIf you have any questions or concerns, please contact us immediately.\n\nThank you,\n${companyName}`
    },
    final: {
      subject: `Final Notice: Invoice ${invoiceNumber} - Immediate Action Required`,
      text: `Dear ${clientName},\n\nThis is a final notice regarding the overdue payment for invoice ${invoiceNumber} in the amount of $${total} (due date: ${dueDate}).\n\nImmediate payment is required to avoid further collection actions. Please contact us within 5 business days to resolve this matter.\n\nWe value our business relationship and hope to resolve this quickly.\n\nRegards,\n${companyName}`
    }
  };
  
  const message = reminderMessages[reminderType];
  
  return await sendInvoiceEmail(
    templateId,
    invoiceData,
    recipientEmail,
    senderEmail,
    {
      subject: message.subject,
      text: message.text
    }
  );
}

/**
 * Send payment confirmation email
 */
export async function sendPaymentConfirmation(
  invoiceData: Partial<InvoiceData>,
  recipientEmail: string,
  senderEmail: string,
  paymentAmount: number,
  paymentDate: string,
  paymentMethod: string = 'N/A'
): Promise<boolean> {
  const companyName = invoiceData.companyName || 'Your Company';
  const clientName = invoiceData.clientName || 'Valued Client';
  const invoiceNumber = invoiceData.invoiceNumber || 'Invoice';
  
  const subject = `Payment Confirmation: Invoice ${invoiceNumber}`;
  const text = `
Dear ${clientName},

We have successfully received your payment for invoice ${invoiceNumber}.

Payment Details:
- Invoice Number: ${invoiceNumber}
- Payment Amount: $${paymentAmount}
- Payment Date: ${paymentDate}
- Payment Method: ${paymentMethod}

Thank you for your prompt payment! Your account is now up to date.

If you have any questions about this payment or need a receipt, please don't hesitate to contact us.

Best regards,
${companyName}
  `.trim();
  
  return await sendCustomEmail(
    recipientEmail,
    subject,
    { text },
    [],
    senderEmail
  );
}

/**
 * Test email configuration
 */
export async function testEmailConfiguration(): Promise<{ success: boolean; message: string }> {
  try {
    const transporter = getTransporter();
    await transporter.verify();
    return {
      success: true,
      message: 'Email configuration is valid and ready to send emails'
    };
  } catch (error) {
    return {
      success: false,
      message: `Email configuration error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Send bulk invoices to multiple recipients
 */
export async function sendBulkInvoices(
  invoices: Array<{
    templateId: string;
    invoiceData: Partial<InvoiceData>;
    recipientEmail: string;
  }>,
  senderEmail: string,
  options: { batchSize?: number; delayBetweenBatches?: number } = {}
): Promise<{ sent: number; failed: number; errors: string[] }> {
  const batchSize = options.batchSize || 10;
  const delay = options.delayBetweenBatches || 1000; // 1 second delay
  
  let sent = 0;
  let failed = 0;
  const errors: string[] = [];
  
  // Process invoices in batches
  for (let i = 0; i < invoices.length; i += batchSize) {
    const batch = invoices.slice(i, i + batchSize);
    
    const promises = batch.map(async ({ templateId, invoiceData, recipientEmail }) => {
      try {
        const success = await sendInvoiceEmail(templateId, invoiceData, recipientEmail, senderEmail);
        if (success) {
          sent++;
        } else {
          failed++;
          errors.push(`Failed to send invoice ${invoiceData.invoiceNumber} to ${recipientEmail}`);
        }
      } catch (error) {
        failed++;
        errors.push(`Error sending invoice ${invoiceData.invoiceNumber} to ${recipientEmail}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    });
    
    await Promise.all(promises);
    
    // Delay between batches to avoid overwhelming the email server
    if (i + batchSize < invoices.length) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  return { sent, failed, errors };
}

// Export email configuration checker
export { testEmailConfiguration as checkEmailConfig };