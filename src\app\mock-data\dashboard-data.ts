/**
 * Mock dashboard data for development and client-side rendering
 * This file provides realistic sample data that can be used when MongoDB is not available
 */

export const mockDashboardData = {
  overview: {
    totalRevenue: {
      thisMonth: 8750,
      lastMonth: 7200,
      allTime: 132500
    },
    totalInvoices: {
      thisMonth: 5,
      lastMonth: 4,
      allTime: 42
    },
    averageInvoiceAmount: {
      thisMonth: 1750,
      allTime: 3154.76
    },
    paymentTimeline: {
      averageDaysToPay: 15,
      fastestPayment: 1,
      slowestPayment: 45
    }
  },
  recentInvoices: [
    {
      _id: "mock-invoice-1",
      invoiceNumber: "INV-2025-001",
      clientName: "Acme Corporation",
      total: 2500,
      currency: "USD",
      status: "paid",
      createdAt: new Date(Date.now() - 15 * 86400000).toISOString(),
      dueDate: new Date(Date.now() - 5 * 86400000).toISOString(),
      isOverdue: false
    },
    {
      _id: "mock-invoice-2",
      invoiceNumber: "INV-2025-002",
      clientName: "Globex Design",
      total: 1800,
      currency: "USD",
      status: "sent",
      createdAt: new Date(Date.now() - 10 * 86400000).toISOString(),
      dueDate: new Date(Date.now() + 20 * 86400000).toISOString(),
      isOverdue: false
    },
    {
      _id: "mock-invoice-3",
      invoiceNumber: "INV-2025-003",
      clientName: "TechStart Inc.",
      total: 3200,
      currency: "USD",
      status: "draft",
      createdAt: new Date(Date.now() - 5 * 86400000).toISOString(),
      dueDate: new Date(Date.now() + 25 * 86400000).toISOString(),
      isOverdue: false
    },
    {
      _id: "mock-invoice-4",
      invoiceNumber: "INV-2025-004",
      clientName: "Smith Consulting",
      total: 1250,
      currency: "USD",
      status: "overdue",
      createdAt: new Date(Date.now() - 45 * 86400000).toISOString(),
      dueDate: new Date(Date.now() - 15 * 86400000).toISOString(),
      isOverdue: true
    },
    {
      _id: "mock-invoice-5",
      invoiceNumber: "INV-2025-005",
      clientName: "Acme Corporation",
      total: 4200,
      currency: "USD",
      status: "paid",
      createdAt: new Date(Date.now() - 60 * 86400000).toISOString(),
      dueDate: new Date(Date.now() - 30 * 86400000).toISOString(),
      isOverdue: false
    }
  ],
  clientInsights: {
    topClients: [
      { name: "Acme Corporation", total: 16500, invoiceCount: 6 },
      { name: "TechStart Inc.", total: 12800, invoiceCount: 4 },
      { name: "Globex Design", total: 9400, invoiceCount: 5 },
      { name: "Smith Consulting", total: 7200, invoiceCount: 3 },
      { name: "Johnson & Co", total: 4500, invoiceCount: 2 }
    ],
    newClientsThisMonth: 2,
    returningClientsThisMonth: 3,
    overdueInvoices: [
      {
        invoiceNumber: "INV-2025-004",
        clientName: "Smith Consulting",
        total: 1250,
        dueDate: new Date(Date.now() - 15 * 86400000).toISOString(),
        daysPastDue: 15
      }
    ]
  },
  businessIntelligence: {
    monthlyRevenue: [
      { month: "Jan", revenue: 9500, invoiceCount: 5 },
      { month: "Feb", revenue: 11200, invoiceCount: 6 },
      { month: "Mar", revenue: 8700, invoiceCount: 4 },
      { month: "Apr", revenue: 12500, invoiceCount: 7 },
      { month: "May", revenue: 10300, invoiceCount: 5 },
      { month: "Jun", revenue: 9800, invoiceCount: 4 },
      { month: "Jul", revenue: 8500, invoiceCount: 3 },
      { month: "Aug", revenue: 7200, invoiceCount: 3 },
      { month: "Sep", revenue: 9600, invoiceCount: 4 },
      { month: "Oct", revenue: 11500, invoiceCount: 5 },
      { month: "Nov", revenue: 12700, invoiceCount: 6 },
      { month: "Dec", revenue: 14500, invoiceCount: 7 }
    ],
    topServices: [
      { service: "Web Development", revenue: 45000 },
      { service: "Graphic Design", revenue: 28000 },
      { service: "Consulting", revenue: 22000 },
      { service: "Content Creation", revenue: 18500 },
      { service: "SEO Services", revenue: 15000 }
    ],
    seasonalPatterns: [
      { quarter: "Q1", revenue: 29400, invoices: 15, averageInvoice: 1960 },
      { quarter: "Q2", revenue: 32600, invoices: 16, averageInvoice: 2037.5 },
      { quarter: "Q3", revenue: 25300, invoices: 10, averageInvoice: 2530 },
      { quarter: "Q4", revenue: 38700, invoices: 18, averageInvoice: 2150 }
    ]
  },
  statusBreakdown: {
    draft: 8,
    sent: 12,
    paid: 20,
    overdue: 2
  },
  aiInsights: [
    {
      type: "revenue_opportunity",
      priority: "low",
      title: "Revenue Growth",
      message: "Your revenue has increased by 21% compared to last month. Keep up the good work!",
      actionable: false
    },
    {
      type: "client_retention",
      priority: "medium",
      title: "Client Retention Opportunity",
      message: "Some clients haven't been invoiced in over 3 months. Consider reaching out to reconnect.",
      actionable: true,
      action: "Send follow-up emails to inactive clients"
    },
    {
      type: "payment_prediction",
      priority: "low",
      title: "Payment Reminder",
      message: "You have 3 invoices awaiting payment. Consider sending reminders.",
      actionable: true,
      action: "Send payment reminders for pending invoices"
    }
  ],
  summary: {
    totalClients: 15,
    activeClients: 8,
    overdueAmount: 1250,
    pendingInvoices: 12
  }
};
