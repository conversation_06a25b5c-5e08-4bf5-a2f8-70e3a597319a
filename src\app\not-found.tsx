'use client'

import React from 'react'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'

export default function NotFound() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900">
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-6xl font-bold text-white mb-6">404</h1>
        <h2 className="text-3xl font-semibold text-white mb-8">Page Not Found</h2>
        <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
          Sorry, the page you are looking for doesn't exist or has been moved.
        </p>
        <Link 
          href="/" 
          className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white font-medium rounded-lg px-6 py-3 transition-all"
        >
          <ArrowLeft className="w-5 h-5" />
          Return to Home
        </Link>
      </div>
    </main>
  )
}
