'use client'

import { useSession, signIn } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useState, useEffect, Suspense } from 'react'
import { 
  Loader2, RefreshCw, AlertCircle, DollarSign, FileText, 
  BarChart3, Timer, Search, Brain, Eye, Edit, Copy, 
  MoreHorizontal, ChevronRight, Users, TrendingUp, 
  ArrowUpRight, ArrowDownRight, Lightbulb
} from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'
// Removed mock data imports - using real data only

// Define simple types for real data
interface Invoice {
  _id: string
  invoiceNumber: string
  clientName: string
  amount: number
  currency?: string
  status: 'draft' | 'sent' | 'paid' | 'overdue'
  createdAt: string
  dueDate: string
}

interface Stats {
  totalRevenue: number
  invoicesCreated: number
  averageInvoice: number
  averagePaymentTime: number
}

interface Subscription {
  plan: 'free' | 'pro'
  invoicesUsed: number
  resetDate: Date
  stripeCustomerId?: string
  stripeSubscriptionId?: string
}

function ClientDashboardContent() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [loading, setLoading] = useState(true)
  const [showPostUpgrade, setShowPostUpgrade] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [stats, setStats] = useState<Stats>({
    totalRevenue: 0,
    invoicesCreated: 0,
    averageInvoice: 0,
    averagePaymentTime: 0
  })
  
  // Development mode testing
  const isDevelopment = process.env.NODE_ENV === 'development'
  const isTestUser = session?.user?.email === '<EMAIL>' // Your email
  const testMode = searchParams.get('test')

  // Check for upgrade success message
  useEffect(() => {
    if (searchParams.get('upgraded') === 'true') {
      setShowPostUpgrade(true)
      router.replace('/dashboard', { scroll: false })
    }
  }, [searchParams, router])

  // Development override for testing different scenarios
  useEffect(() => {
    if (session && (isDevelopment || testMode)) {
      // URL parameter testing
      if (testMode === 'free') {
        setSubscription({
          plan: 'free',
          invoicesUsed: 3, // At limit
          resetDate: new Date()
        })
        console.log('🧪 Test mode: FREE user at limit')
      } else if (testMode === 'pro') {
        setSubscription({
          plan: 'pro',
          invoicesUsed: 50,
          resetDate: new Date()
        })
        console.log('🧪 Test mode: PRO user')
      } else if (testMode === 'newbie') {
        setSubscription({
          plan: 'free',
          invoicesUsed: 0, // New user
          resetDate: new Date()
        })
        console.log('🧪 Test mode: NEW free user')
      } else if (isDevelopment && isTestUser) {
        // Development override for your email
        setSubscription({
          plan: 'pro', // Test as pro user
          invoicesUsed: 10, // Test with many invoices
          resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        })
        console.log('🧪 Development override: Testing as PRO user')
        
        // Add mock invoices for testing
        const mockInvoices: Invoice[] = [
          {
            _id: '1',
            invoiceNumber: 'INV-2025-0001',
            clientName: 'Test Client 1',
            amount: 1500,
            currency: 'USD',
            status: 'paid',
            createdAt: new Date().toISOString(),
            dueDate: new Date().toISOString()
          },
          {
            _id: '2',
            invoiceNumber: 'INV-2025-0002',
            clientName: 'Test Client 2',
            amount: 2500,
            currency: 'USD',
            status: 'sent',
            createdAt: new Date().toISOString(),
            dueDate: new Date().toISOString()
          },
        ]
        
        setInvoices(mockInvoices)
        setStats({
          totalRevenue: 1500, // Only paid invoices
          invoicesCreated: 2,
          averageInvoice: 2000,
          averagePaymentTime: 7
        })
        setLoading(false)
        return
      }
    }
  }, [session, isDevelopment, isTestUser, testMode])

  // Fetch real user data
  useEffect(() => {
    // Skip if we're in development mode with overrides
    if (isDevelopment && isTestUser && !testMode) {
      return // Data already set by development override
    }
    
    if (status === 'authenticated' && session) {
      fetchUserData()
    } else if (status === 'unauthenticated') {
      // Show empty state for guests
      setLoading(false)
    }
  }, [status, session, isDevelopment, isTestUser, testMode])

  const fetchUserData = async () => {
    try {
      setLoading(true)
      
      // Fetch real user data
      const userResponse = await fetch('/api/user/profile')
      if (userResponse.ok) {
        const userData = await userResponse.json()
        setSubscription(userData.subscription || {
          plan: 'free',
          invoicesUsed: 0,
          resetDate: new Date()
        })
      }

      // Fetch real invoices
      const invoicesResponse = await fetch('/api/invoices')
      if (invoicesResponse.ok) {
        const invoicesData = await invoicesResponse.json()
        setInvoices(invoicesData)
        
        // Calculate real stats from actual invoices
        const paidInvoices = invoicesData.filter((inv: Invoice) => inv.status === 'paid')
        const totalRevenue = paidInvoices.reduce((sum: number, inv: Invoice) => sum + (inv.amount || 0), 0)
        
        setStats({
          totalRevenue,
          invoicesCreated: invoicesData.length,
          averageInvoice: invoicesData.length > 0 ? totalRevenue / paidInvoices.length : 0,
          averagePaymentTime: 0 // Calculate from actual payment dates if available
        })
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
      // Set empty state on error
      setStats({
        totalRevenue: 0,
        invoicesCreated: 0,
        averageInvoice: 0,
        averagePaymentTime: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const handleUpgrade = async () => {
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      const { url } = await response.json()
      if (url) {
        window.location.href = url
      }
    } catch (error) {
      console.error('Error creating checkout session:', error)
    }
  }

  // Format currency
  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'text-green-600 bg-green-50'
      case 'sent': return 'text-blue-600 bg-blue-50'
      case 'draft': return 'text-gray-600 bg-gray-50'
      case 'overdue': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  // Show loading state
  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    )
  }
  
  // Use override subscription data in development/test mode
  const activeSubscription = subscription || session?.user?.subscription
  const isPro = activeSubscription?.plan === 'pro'
  const invoicesUsed = activeSubscription?.invoicesUsed || 0
  const remainingInvoices = isPro ? Infinity : Math.max(0, 3 - invoicesUsed)

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Development Testing Banner */}
      {(isDevelopment || testMode) && session?.user && (
        <div className="mb-6 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-bold">🧪 Development Mode Active</h3>
              <p className="text-sm text-yellow-100">
                {testMode ? `Test Mode: ${testMode.toUpperCase()}` : 'Development Override Active'} | 
                Plan: {activeSubscription?.plan || 'unknown'} | 
                Invoices Used: {invoicesUsed}
              </p>
            </div>
            <div className="flex gap-2 text-xs">
              <a href="/dashboard?test=free" className="px-2 py-1 bg-yellow-600 rounded hover:bg-yellow-700">
                Test Free
              </a>
              <a href="/dashboard?test=pro" className="px-2 py-1 bg-yellow-600 rounded hover:bg-yellow-700">
                Test Pro
              </a>
              <a href="/dashboard?test=newbie" className="px-2 py-1 bg-yellow-600 rounded hover:bg-yellow-700">
                Test New
              </a>
              <a href="/dashboard" className="px-2 py-1 bg-yellow-600 rounded hover:bg-yellow-700">
                Clear
              </a>
            </div>
          </div>
        </div>
      )}
      
      {/* Guest User Banner */}
      {!session?.user && (
        <div className="mb-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold mb-2">Sign Up to Access Your Real Dashboard</h2>
              <p className="text-blue-100">
                This is a preview with sample data. Create your free account to start tracking your actual invoices and revenue.
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => signIn('google')}
                className="px-4 py-2 bg-white text-blue-600 rounded-lg font-medium hover:bg-gray-100 transition"
              >
                Sign Up Free
              </button>
              <button
                onClick={() => router.push('/pricing')}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-400 transition"
              >
                View Plans
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">
              {session?.user 
                ? `Welcome back, ${session.user.name}! Here's your business overview.`
                : "Welcome! This is a demo dashboard showing sample data."}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={selectedPeriod}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="month">This Month</option>
              <option value="year">This Year</option>
              <option value="all">All Time</option>
            </select>
            <button 
              onClick={fetchUserData} 
              className="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
            {session?.user ? (
              isPro ? (
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Pro
                </span>
              ) : (
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Free
                </span>
              )
            ) : (
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Demo
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Stats Grid - Show real data or zeros */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <h3 className="text-sm font-medium text-gray-600">
                Total Revenue
              </h3>
              <DollarSign className="h-4 w-4 text-green-500" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(stats.totalRevenue)}
              </div>
              <div className="text-xs text-gray-500">
                {stats.invoicesCreated > 0 ? 'From paid invoices' : 'No invoices yet'}
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <h3 className="text-sm font-medium text-gray-600">
                Invoices Created
              </h3>
              <FileText className="h-4 w-4 text-blue-500" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {stats.invoicesCreated}
              </div>
              <div className="text-xs text-gray-500">
                {activeSubscription?.plan === 'free' 
                  ? `${activeSubscription.invoicesUsed}/3 this month` 
                  : 'Unlimited'
                }
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <h3 className="text-sm font-medium text-gray-600">
                Average Invoice
              </h3>
              <BarChart3 className="h-4 w-4 text-purple-500" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(stats.averageInvoice)}
              </div>
              <div className="text-xs text-gray-500">
                {stats.invoicesCreated > 0 ? 'Per invoice' : 'Create your first invoice'}
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <h3 className="text-sm font-medium text-gray-600">
                Avg. Payment Time
              </h3>
              <Timer className="h-4 w-4 text-orange-500" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {stats.averagePaymentTime > 0 ? `${stats.averagePaymentTime} days` : 'N/A'}
              </div>
              <div className="text-xs text-gray-500">
                {stats.invoicesCreated > 0 ? 'Average time to payment' : 'No payments yet'}
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Subscription Usage Indicator */}
      {session?.user && (
        <div className="mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Invoice Usage {isPro ? '(Pro Plan)' : '(Free Plan)'}
                </h3>
                <div className="flex items-center gap-4">
                  <div className="text-sm text-gray-600">
                    Used this month: <span className="font-medium">{invoicesUsed}</span>
                    {!isPro && <span> / 3</span>}
                  </div>
                  {!isPro && (
                    <div className="text-sm text-gray-600">
                      Remaining: <span className={`font-medium ${remainingInvoices === 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {remainingInvoices === Infinity ? 'Unlimited' : remainingInvoices}
                      </span>
                    </div>
                  )}
                </div>
                {!isPro && (
                  <div className="mt-3 w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all ${
                        invoicesUsed >= 3 ? 'bg-red-500' : invoicesUsed >= 2 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min((invoicesUsed / 3) * 100, 100)}%` }}
                    />
                  </div>
                )}
              </div>
              <div className="flex gap-3">
                {isPro ? (
                  <span className="px-4 py-2 bg-green-100 text-green-800 rounded-lg font-medium">
                    ✨ Unlimited Invoices
                  </span>
                ) : (
                  <>
                    {remainingInvoices > 0 ? (
                      <Link
                        href="/create"
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                      >
                        Create Invoice
                      </Link>
                    ) : (
                      <div className="text-center">
                        <p className="text-sm text-red-600 mb-2">Invoice limit reached</p>
                        <Link
                          href="/upgrade"
                          className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition"
                        >
                          Upgrade to Pro
                        </Link>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Invoices - Show real data or empty state */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Recent Invoices</h3>
            {invoices.length > 0 && (
              <Link
                href="/my-invoices"
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                View all →
              </Link>
            )}
          </div>
        </div>
        <div className="p-6">
          {invoices.length > 0 ? (
            <div className="space-y-4">
              {invoices.slice(0, 5).map((invoice) => (
                <div key={invoice._id} className="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <FileText className="w-8 h-8 text-gray-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {invoice.invoiceNumber}
                      </p>
                      <p className="text-sm text-gray-500">
                        {invoice.clientName}
                      </p>
                      <p className="text-xs text-gray-400">
                        {new Date(invoice.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {formatCurrency(invoice.amount)}
                      </p>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(invoice.status)}`}>
                        {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                      </span>
                    </div>
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">No invoices yet</h4>
              <p className="text-gray-500 mb-6">
                Create your first invoice to start tracking your business revenue
              </p>
              <div className="flex gap-3 justify-center">
                <Link
                  href="/create"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                >
                  Create Your First Invoice
                </Link>
                <Link
                  href="/templates"
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition"
                >
                  Browse Templates
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default function ClientDashboard() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <h2 className="text-xl text-gray-600 mt-4">Loading dashboard...</h2>
        </div>
      </div>
    }>
      <ClientDashboardContent />
    </Suspense>
  )
}
