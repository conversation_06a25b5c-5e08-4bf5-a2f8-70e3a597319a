import Stripe from 'stripe';

// Determine if we're in test mode
const isTestMode = process.env.STRIPE_MODE === 'test';

// Log the mode (only in development)
if (process.env.NODE_ENV === 'development') {
  console.log(`🔧 Stripe Mode: ${isTestMode ? 'TEST' : 'LIVE'}`);
}

// Select the appropriate keys based on mode
export const stripeSecretKey = isTestMode
  ? process.env.STRIPE_TEST_SECRET_KEY || ''
  : process.env.STRIPE_SECRET_KEY || '';

export const stripePublishableKey = isTestMode
  ? process.env.NEXT_PUBLIC_STRIPE_TEST_PUBLISHABLE_KEY || ''
  : process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || process.env.STRIPE_PUBLISHABLE_KEY || '';

export const stripeWebhookSecret = isTestMode
  ? process.env.STRIPE_TEST_WEBHOOK_SECRET || ''
  : process.env.STRIPE_WEBHOOK_SECRET || '';

export const stripePriceId = isTestMode
  ? process.env.STRIPE_TEST_PRICE_ID || ''
  : process.env.STRIPE_PRO_PRICE_ID || '';

// Validate configuration
export const isStripeConfigured = () => {
  const hasRequiredKeys = !!(
    stripeSecretKey &&
    stripeWebhookSecret &&
    stripePriceId
  );
  
  if (!hasRequiredKeys && process.env.NODE_ENV === 'development') {
    console.warn('⚠️ Stripe configuration incomplete:', {
      hasSecretKey: !!stripeSecretKey,
      hasWebhookSecret: !!stripeWebhookSecret,
      hasPriceId: !!stripePriceId,
      mode: isTestMode ? 'test' : 'live'
    });
  }
  
  return hasRequiredKeys;
};

// Create and export the Stripe instance
export const stripe = stripeSecretKey 
  ? new Stripe(stripeSecretKey, {
      apiVersion: '2023-10-16',
      typescript: true,
    })
  : null;

// Helper to get the current mode
export const getStripeMode = () => isTestMode ? 'test' : 'live';

// Helper to check if we're using test keys
export const isUsingTestKeys = () => {
  return stripeSecretKey.startsWith('sk_test_') || 
         stripePublishableKey.startsWith('pk_test_');
};

// Export all configuration in one object for convenience
export const stripeConfig = {
  secretKey: stripeSecretKey,
  publishableKey: stripePublishableKey,
  webhookSecret: stripeWebhookSecret,
  priceId: stripePriceId,
  mode: getStripeMode(),
  isTestMode,
  isConfigured: isStripeConfigured(),
};

// Type for Stripe mode
export type StripeMode = 'test' | 'live';