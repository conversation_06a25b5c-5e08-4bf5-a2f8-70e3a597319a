import Link from 'next/link'
import { ArrowLeft, Search, MessageSquare, Mail, Video, BookOpen, Clock, Users } from 'lucide-react'

export default function HelpPage() {
  const quickActions = [
    {
      title: "Live Chat",
      description: "Get instant help from our support team",
      icon: MessageSquare,
      href: "/support",
      color: "blue"
    },
    {
      title: "Email Support", 
      description: "Send us a detailed message",
      icon: Mail,
      href: "/contact",
      color: "green"
    },
    {
      title: "Video Tutorials",
      description: "Watch step-by-step guides",
      icon: Video,
      href: "/guides",
      color: "purple"
    }
  ]

  const helpTopics = [
    {
      title: "Getting Started",
      description: "Learn the basics of creating invoices",
      articles: [
        "How to create your first invoice",
        "Setting up your business profile", 
        "Choosing the right template",
        "Adding your logo and branding"
      ],
      href: "/guides/getting-started"
    },
    {
      title: "Invoice Management",
      description: "Manage your invoices effectively",
      articles: [
        "Editing and updating invoices",
        "Tracking payment status",
        "Setting up recurring invoices",
        "Invoice numbering systems"
      ],
      href: "/guides/invoice-management"
    },
    {
      title: "Client Management",
      description: "Organize your client information",
      articles: [
        "Adding and managing clients",
        "Setting up client defaults",
        "Client communication tools",
        "Managing multiple projects"
      ],
      href: "/guides/client-management"
    },
    {
      title: "Payments & Billing",
      description: "Handle payments and billing",
      articles: [
        "Setting up payment processing",
        "Understanding billing cycles",
        "Managing subscriptions",
        "Payment troubleshooting"
      ],
      href: "/guides/payment-setup"
    }
  ]

  const popularArticles = [
    "How to customize invoice templates",
    "Setting up automatic payment reminders",
    "Exporting invoices to PDF",
    "Managing tax calculations",
    "Setting up Stripe integration"
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <Link 
            href="/" 
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>
        </div>
        
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Help Center</h1>
          <p className="text-xl text-gray-600">
            Find answers, guides, and support for all your invoicing needs
          </p>
        </div>
        
        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search for help articles..."
              className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
            />
          </div>
        </div>
        
        {/* Quick Actions */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {quickActions.map((action, index) => {
            const Icon = action.icon
            const colorClasses: Record<string, string> = {
              blue: "bg-blue-100 text-blue-600 hover:bg-blue-200",
              green: "bg-green-100 text-green-600 hover:bg-green-200", 
              purple: "bg-purple-100 text-purple-600 hover:bg-purple-200"
            }
            
            return (
              <Link
                key={index}
                href={action.href}
                className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 group"
              >
                <div className={`w-12 h-12 rounded-lg ${colorClasses[action.color]} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                  <Icon className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{action.title}</h3>
                <p className="text-gray-600">{action.description}</p>
              </Link>
            )
          })}
        </div>
        
        {/* Help Topics */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Browse by Topic</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {helpTopics.map((topic, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{topic.title}</h3>
                <p className="text-gray-600 mb-4">{topic.description}</p>
                <ul className="space-y-2 mb-4">
                  {topic.articles.map((article, articleIndex) => (
                    <li key={articleIndex} className="text-sm text-gray-600 flex items-center">
                      <BookOpen className="w-4 h-4 mr-2 text-gray-400" />
                      {article}
                    </li>
                  ))}
                </ul>
                <Link 
                  href={topic.href}
                  className="text-blue-600 hover:text-blue-700 font-medium text-sm"
                >
                  View all articles →
                </Link>
              </div>
            ))}
          </div>
        </div>
        
        {/* Popular Articles */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Popular Articles</h2>
          <div className="space-y-4">
            {popularArticles.map((article, index) => (
              <Link
                key={index}
                href={`/guides/article-${index + 1}`}
                className="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 transition-colors group"
              >
                <div className="flex items-center space-x-3">
                  <BookOpen className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-900 group-hover:text-blue-600">{article}</span>
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="w-4 h-4 mr-1" />
                  5 min read
                </div>
              </Link>
            ))}
          </div>
        </div>
        
        {/* Contact Support */}
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Still need help?
          </h3>
          <p className="text-gray-600 mb-6">
            Our support team is standing by to help you succeed with your invoicing.
          </p>
          <div className="flex justify-center space-x-4">
            <Link 
              href="/contact"
              className="bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            >
              Contact Support
            </Link>
            <Link 
              href="/guides"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Browse All Guides
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}