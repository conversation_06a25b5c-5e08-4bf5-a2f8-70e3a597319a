'use client';

import { useEffect, useState } from 'react';
import { AlertTriangle, X } from 'lucide-react';
import { isUsingTestKeys, getStripeMode } from '@/lib/stripe-client-config';

export default function StripeTestModeIndicator() {
  const [isTestMode, setIsTestMode] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  
  useEffect(() => {
    // Check Stripe configuration on mount
    const checkStripeMode = async () => {
      try {
        const response = await fetch('/api/stripe/config');
        const data = await response.json();
        setIsTestMode(data.isTestMode || isUsingTestKeys());
      } catch (error) {
        // Fallback to client-side check
        setIsTestMode(isUsingTestKeys());
      }
    };
    
    checkStripeMode();
  }, []);

  // Only show in development or when explicitly in test mode
  if (!isTestMode || !isVisible) {
    return null;
  }

  // Don't show in production unless explicitly configured
  if (process.env.NODE_ENV === 'production' && !process.env.NEXT_PUBLIC_SHOW_TEST_MODE_INDICATOR) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 animate-in slide-in-from-bottom-5 duration-300">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg p-4 pr-12 max-w-sm">
        <button
          onClick={() => setIsVisible(false)}
          className="absolute top-2 right-2 text-yellow-600 hover:text-yellow-800 transition-colors"
          aria-label="Dismiss test mode indicator"
        >
          <X className="w-4 h-4" />
        </button>
        
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-sm font-semibold text-yellow-800">
              Stripe Test Mode Active
            </h3>
            <p className="text-xs text-yellow-700 mt-1">
              Payments are in test mode. No real charges will be made.
            </p>
            <p className="text-xs text-yellow-600 mt-2">
              Mode: <span className="font-mono">{getStripeMode()}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}