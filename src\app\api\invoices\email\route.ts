import { NextRequest } from 'next/server';
import { sendInvoiceEmail, testEmailConfiguration } from '@/lib/email-sender';
import { validateTemplateData } from '@/lib/template-renderer';
import { getServerSession } from 'next-auth';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session?.user?.email) {
      return new Response('Authentication required', { status: 401 });
    }
    
    const { templateId, invoiceData, recipientEmail, senderEmail, options } = await request.json();
    
    // Validate required parameters
    if (!templateId) {
      return new Response(
        JSON.stringify({ error: 'Template ID is required' }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    if (!invoiceData) {
      return new Response(
        JSON.stringify({ error: 'Invoice data is required' }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    if (!recipientEmail) {
      return new Response(
        JSON.stringify({ error: 'Recipient email is required' }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      return new Response(
        JSON.stringify({ error: 'Invalid recipient email format' }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Validate sender email if provided, otherwise use session email
    const emailSender = senderEmail || session.user.email;
    if (senderEmail && !emailRegex.test(senderEmail)) {
      return new Response(
        JSON.stringify({ error: 'Invalid sender email format' }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Validate invoice data
    const validation = validateTemplateData(invoiceData);
    if (!validation.isValid) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid invoice data', 
          errors: validation.errors 
        }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Test email configuration before attempting to send
    const configTest = await testEmailConfiguration();
    if (!configTest.success) {
      return new Response(
        JSON.stringify({ 
          error: 'Email service not configured', 
          message: configTest.message 
        }), 
        { 
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Send the email
    const success = await sendInvoiceEmail(
      templateId, 
      invoiceData, 
      recipientEmail, 
      emailSender, 
      options
    );
    
    if (success) {
      return new Response(
        JSON.stringify({ 
          message: 'Invoice email sent successfully',
          recipient: recipientEmail,
          invoiceNumber: invoiceData.invoiceNumber
        }), 
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    } else {
      return new Response(
        JSON.stringify({ 
          error: 'Failed to send email',
          message: 'Email delivery failed. Please check your email configuration and try again.'
        }), 
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
  } catch (error) {
    console.error('Email sending API error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(
      JSON.stringify({ 
        error: 'Email sending failed', 
        message: errorMessage 
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// GET endpoint to test email configuration
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session?.user?.email) {
      return new Response('Authentication required', { status: 401 });
    }
    
    const configTest = await testEmailConfiguration();
    
    return new Response(
      JSON.stringify({
        configured: configTest.success,
        message: configTest.message
      }), 
      { 
        status: configTest.success ? 200 : 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
  } catch (error) {
    console.error('Email configuration test error:', error);
    
    return new Response(
      JSON.stringify({ 
        configured: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}