'use client'

export const dynamic = 'force-dynamic'

import { useState, useRef, useEffect, useCallback, Suspense } from 'react'
import { useSession, signIn } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { getTemplateById, InvoiceTemplate, invoiceTemplates } from '@/lib/template-data'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Building,
  User,
  Calendar,
  DollarSign,
  Plus,
  Trash2,
  Download,
  Save,
  Send,
  Printer,
  Upload,
  FileText,
  Hash,
  Mail,
  Phone,
  MapPin,
  Globe,
  CreditCard,
  AlertCircle,
  Zap,
  LogIn,
  Clock,
  Sparkles,
  Brain,
  TrendingUp,
  Target,
  Lightbulb,
  BookOpen,
  Users,
  CheckCircle,
  XCircle,
  RefreshCw,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Wand2,
  BarChart3,
  PiggyBank,
  Shield,
  Award,
  Eye,
  HelpCircle,
  Loader2
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { generateInvoiceNumber } from '@/lib/utils'
import UsageIndicator from '@/components/UsageIndicator'
import UpgradePrompt from '@/components/UpgradePrompt'
import { 
  analyzeBusinessDescription, 
  generateAIEnhancedContent,
  customizeTemplateForBusiness 
} from '@/lib/ai-template-generator'

interface LineItem {
  id: string
  description: string
  quantity: number
  rate: number
  amount: number
  category?: string
  isAISuggested?: boolean
}

interface InvoiceData {
  // Business Information
  businessName: string
  businessAddress: string
  businessCity: string
  businessZip: string
  businessEmail: string
  businessPhone: string
  businessWebsite?: string
  businessLogo?: string
  taxId?: string

  // Client Information
  clientName: string
  clientCompany?: string
  clientAddress: string
  clientCity: string
  clientZip: string
  clientEmail?: string
  clientPhone?: string
  clientType?: 'individual' | 'business' | 'recurring' | 'new'

  // Invoice Details
  invoiceNumber: string
  invoiceDate: string
  dueDate: string
  currency: string

  // Line Items
  lineItems: LineItem[]

  // Totals
  subtotal: number
  tax: number
  taxRate: number
  total: number

  // Notes
  notes?: string
  terms?: string
  paymentInstructions?: string
  
  // AI Enhancement Data
  businessDescription?: string
  selectedTemplate?: any
  aiSuggestions?: AISuggestion[]
}

interface AISuggestion {
  id: string
  type: 'field' | 'content' | 'pricing' | 'optimization' | 'upsell'
  field?: string
  title: string
  suggestion: string
  reasoning: string
  confidence: number
  applied: boolean
  dismissed: boolean
  category: 'improvement' | 'compliance' | 'optimization' | 'growth'
}

interface AIAssistant {
  isAnalyzing: boolean
  suggestions: AISuggestion[]
  businessAnalysis: any
  userLearning: UserLearning
}

interface UserLearning {
  frequentServices: string[]
  averageInvoiceAmount: number
  commonClientTypes: string[]
  preferredTerms: string[]
  industryPatterns: Record<string, any>
}

// Enhanced Invoice Preview Component  
const InvoicePreview = ({ data, template }: { data: InvoiceData, template?: InvoiceTemplate | null }) => {
  // Use template HTML if available, otherwise use default layout
  if (template?.htmlTemplate) {
    // For now, we'll use the default layout but could render template HTML
    // This would require a more sophisticated template engine
  }
  
  return (
    <div className="bg-white p-8 shadow-lg" id="invoice-preview" style={{
      fontFamily: template?.styling?.fontFamily || 'Inter, sans-serif',
      '--primary-color': template?.styling?.primaryColor || '#2563eb'
    } as React.CSSProperties}>
      {/* AI Optimization Badge */}
      {data.aiSuggestions && data.aiSuggestions.some(s => s.applied) && (
        <div className="mb-4 flex justify-end">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            <Sparkles className="w-3 h-3 mr-1" />
            AI Optimized
          </span>
        </div>
      )}
      
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          {data.businessLogo ? (
            <img src={data.businessLogo} alt="Logo" className="h-16 mb-4" />
          ) : (
            <h1 className="text-2xl font-bold text-gray-900 mb-4">{data.businessName || 'Your Business Name'}</h1>
          )}
          <div className="text-sm text-gray-600">
            <p>{data.businessAddress}</p>
            <p>{data.businessCity} {data.businessZip}</p>
            <p>{data.businessEmail}</p>
            <p>{data.businessPhone}</p>
            {data.businessWebsite && <p>{data.businessWebsite}</p>}
          </div>
        </div>
        <div className="text-right">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">INVOICE</h2>
          <p className="text-sm text-gray-600">Invoice #: {data.invoiceNumber}</p>
          <p className="text-sm text-gray-600">Date: {data.invoiceDate}</p>
          <p className="text-sm text-gray-600">Due: {data.dueDate}</p>
        </div>
      </div>

      {/* Bill To */}
      <div className="mb-8">
        <h3 className="text-sm font-semibold text-gray-700 mb-2">BILL TO:</h3>
        <div className="text-sm text-gray-900">
          <p className="font-semibold">{data.clientName || 'Client Name'}</p>
          {data.clientCompany && <p>{data.clientCompany}</p>}
          <p>{data.clientAddress}</p>
          <p>{data.clientCity} {data.clientZip}</p>
          {data.clientEmail && <p>{data.clientEmail}</p>}
          {data.clientPhone && <p>{data.clientPhone}</p>}
        </div>
      </div>

      {/* Line Items */}
      <table className="w-full mb-8">
        <thead>
          <tr className="border-b-2 border-gray-300">
            <th className="text-left py-2 text-sm font-semibold text-gray-700">Description</th>
            <th className="text-center py-2 text-sm font-semibold text-gray-700 w-20">Qty</th>
            <th className="text-right py-2 text-sm font-semibold text-gray-700 w-24">Rate</th>
            <th className="text-right py-2 text-sm font-semibold text-gray-700 w-24">Amount</th>
          </tr>
        </thead>
        <tbody>
          {data.lineItems.map((item) => (
            <tr key={item.id} className="border-b border-gray-200">
              <td className="py-3 text-sm text-gray-900 relative">
                {item.description}
                {item.isAISuggested && (
                  <span className="inline-flex items-center ml-2 px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    <Brain className="w-2 h-2 mr-1" />
                    AI
                  </span>
                )}
              </td>
              <td className="py-3 text-sm text-gray-900 text-center">{item.quantity}</td>
              <td className="py-3 text-sm text-gray-900 text-right">
                {data.currency}{item.rate.toFixed(2)}
              </td>
              <td className="py-3 text-sm text-gray-900 text-right">
                {data.currency}{item.amount.toFixed(2)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-64">
          <div className="flex justify-between py-2 border-b border-gray-200">
            <span className="text-sm text-gray-700">Subtotal</span>
            <span className="text-sm text-gray-900">{data.currency}{data.subtotal.toFixed(2)}</span>
          </div>
          {data.taxRate > 0 && (
            <div className="flex justify-between py-2 border-b border-gray-200">
              <span className="text-sm text-gray-700">Tax ({data.taxRate}%)</span>
              <span className="text-sm text-gray-900">{data.currency}{data.tax.toFixed(2)}</span>
            </div>
          )}
          <div className="flex justify-between py-3 font-semibold">
            <span className="text-gray-900">Total</span>
            <span className="text-gray-900">{data.currency}{data.total.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Notes */}
      {(data.notes || data.terms || data.paymentInstructions) && (
        <div className="border-t border-gray-200 pt-6">
          {data.notes && (
            <div className="mb-4">
              <h4 className="text-sm font-semibold text-gray-700 mb-1">Notes</h4>
              <p className="text-sm text-gray-600">{data.notes}</p>
            </div>
          )}
          {data.paymentInstructions && (
            <div className="mb-4">
              <h4 className="text-sm font-semibold text-gray-700 mb-1">Payment Instructions</h4>
              <p className="text-sm text-gray-600">{data.paymentInstructions}</p>
            </div>
          )}
          {data.terms && (
            <div>
              <h4 className="text-sm font-semibold text-gray-700 mb-1">Terms & Conditions</h4>
              <p className="text-sm text-gray-600">{data.terms}</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// AI Assistant Panel Component
const AIAssistantPanel = ({ 
  assistant, 
  onApplySuggestion, 
  onDismissSuggestion,
  onRequestSuggestions,
  invoiceData 
}: {
  assistant: AIAssistant
  onApplySuggestion: (suggestionId: string) => void
  onDismissSuggestion: (suggestionId: string) => void
  onRequestSuggestions: () => void
  invoiceData: InvoiceData
}) => {
  const activeSuggestions = assistant.suggestions.filter(s => !s.applied && !s.dismissed)
  
  return (
    <Card className="p-4 bg-gradient-to-br from-purple-50 to-blue-50 border-purple-200">
      <div className="flex items-center gap-2 mb-4">
        <div className="flex items-center justify-center w-8 h-8 bg-purple-100 rounded-full">
          <Brain className="w-4 h-4 text-purple-600" />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">AI Assistant</h3>
          <p className="text-xs text-gray-600">Smart suggestions for your invoice</p>
        </div>
        <Button
          onClick={onRequestSuggestions}
          variant="ghost"
          size="sm"
          className="ml-auto"
          disabled={assistant.isAnalyzing}
        >
          {assistant.isAnalyzing ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4" />
          )}
        </Button>
      </div>

      {assistant.isAnalyzing && (
        <div className="text-center py-6">
          <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2 text-purple-600" />
          <p className="text-sm text-gray-600">Analyzing your invoice...</p>
        </div>
      )}

      {activeSuggestions.length === 0 && !assistant.isAnalyzing && (
        <div className="text-center py-6">
          <CheckCircle className="w-6 h-6 mx-auto mb-2 text-green-600" />
          <p className="text-sm text-gray-600">Your invoice looks great!</p>
          <Button
            onClick={onRequestSuggestions}
            variant="outline"
            size="sm"
            className="mt-2"
          >
            Get More Suggestions
          </Button>
        </div>
      )}

      <div className="space-y-3">
        {activeSuggestions.map((suggestion) => (
          <motion.div
            key={suggestion.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg p-3 border border-gray-200"
          >
            <div className="flex items-start gap-3">
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                suggestion.category === 'improvement' ? 'bg-blue-100 text-blue-700' :
                suggestion.category === 'compliance' ? 'bg-red-100 text-red-700' :
                suggestion.category === 'optimization' ? 'bg-green-100 text-green-700' :
                'bg-purple-100 text-purple-700'
              }`}>
                {suggestion.category === 'improvement' ? <TrendingUp className="w-3 h-3" /> :
                 suggestion.category === 'compliance' ? <Shield className="w-3 h-3" /> :
                 suggestion.category === 'optimization' ? <Target className="w-3 h-3" /> :
                 <Award className="w-3 h-3" />}
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 mb-1">
                  {suggestion.title}
                </h4>
                <p className="text-xs text-gray-600 mb-2">
                  {suggestion.suggestion}
                </p>
                <p className="text-xs text-gray-500 mb-3">
                  {suggestion.reasoning}
                </p>
                
                <div className="flex items-center gap-2">
                  <Button
                    onClick={() => onApplySuggestion(suggestion.id)}
                    size="sm"
                    className="text-xs bg-purple-600 hover:bg-purple-700"
                  >
                    Apply
                  </Button>
                  <Button
                    onClick={() => onDismissSuggestion(suggestion.id)}
                    variant="ghost"
                    size="sm"
                    className="text-xs"
                  >
                    Dismiss
                  </Button>
                  <div className="ml-auto">
                    <span className="text-xs text-gray-500">
                      {Math.round(suggestion.confidence * 100)}% confidence
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </Card>
  )
}

function CreateInvoicePageContent() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [activeSection, setActiveSection] = useState('business')
  const [isLoading, setIsLoading] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false)
  const [canCreateInvoice, setCanCreateInvoice] = useState(true)
  const [remainingInvoices, setRemainingInvoices] = useState<number | null>(null)
  const [draftId, setDraftId] = useState<string | null>(null)
  const [notification, setNotification] = useState<{type: 'success' | 'error' | 'info', message: string} | null>(null)
  
  // Auto-dismiss notifications
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => {
        setNotification(null)
      }, 5000) // Auto-dismiss after 5 seconds
      
      return () => clearTimeout(timer)
    }
  }, [notification])
  
  // Template and AI Assistant State
  const [selectedTemplate, setSelectedTemplate] = useState<InvoiceTemplate | null>(null)
  const [templateCustomizations, setTemplateCustomizations] = useState<any>(null)
  const [aiAssistant, setAiAssistant] = useState<AIAssistant>({
    isAnalyzing: false,
    suggestions: [],
    businessAnalysis: null,
    userLearning: {
      frequentServices: [],
      averageInvoiceAmount: 0,
      commonClientTypes: [],
      preferredTerms: [],
      industryPatterns: {}
    }
  })
  
  // Initialize invoice data
  const [invoiceData, setInvoiceData] = useState<InvoiceData>({
    // Business Information
    businessName: '',
    businessAddress: '',
    businessCity: '',
    businessZip: '',
    businessEmail: '',
    businessPhone: '',
    businessWebsite: '',
    businessLogo: '',
    taxId: '',

    // Client Information
    clientName: '',
    clientCompany: '',
    clientAddress: '',
    clientCity: '',
    clientZip: '',
    clientEmail: '',
    clientPhone: '',
    clientType: 'new',

    // Invoice Details
    invoiceNumber: '',
    invoiceDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    currency: '$',

    // Line Items
    lineItems: [
      { id: '1', description: '', quantity: 1, rate: 0, amount: 0 }
    ],

    // Totals
    subtotal: 0,
    tax: 0,
    taxRate: 0,
    total: 0,

    // Notes
    notes: '',
    terms: '',
    paymentInstructions: '',
    
    // AI Data
    businessDescription: '',
    aiSuggestions: []
  })

  // Load selected template from templates page
  useEffect(() => {
    const templateId = searchParams.get('template')
    if (templateId) {
      // Load template data
      const template = getTemplateById(templateId)
      if (template) {
        setSelectedTemplate(template)
        
        // Load customizations if available
        const customizations = localStorage.getItem('templateCustomizations')
        const generatedData = localStorage.getItem('generatedTemplateData')
        
        if (customizations) {
          try {
            const parsedCustomizations = JSON.parse(customizations)
            setTemplateCustomizations(parsedCustomizations)
            
            // Apply customizations to invoice data
            applyTemplateCustomizations(template, parsedCustomizations)
          } catch (error) {
            console.error('Error loading template customizations:', error)
          }
        }
        
        if (generatedData) {
          try {
            const generated = JSON.parse(generatedData)
            setInvoiceData(prev => ({
              ...prev,
              selectedTemplate: generated
            }))
            
            // Auto-apply AI suggestions
            setTimeout(() => {
              requestAISuggestions()
            }, 1000)
          } catch (error) {
            console.error('Error loading generated template data:', error)
          }
        }
        
        // Apply template styling and defaults
        applyTemplateDefaults(template)
      }
    }
  }, [searchParams])

  // Check authentication and invoice limits
  useEffect(() => {
    // Allow unauthenticated users to use the create page
    if (status === 'authenticated' && session?.user) {
      checkInvoiceLimit()
      loadUserBusinessInfo()
      loadUserLearningData()
    }
    // Generate invoice number for all users
    generateNewInvoiceNumber()
  }, [status, session])

  // Load user learning data
  const loadUserLearningData = async () => {
    if (!session?.user) return
    
    try {
      const response = await fetch('/api/user/learning')
      if (response.ok) {
        const learningData = await response.json()
        setAiAssistant(prev => ({
          ...prev,
          userLearning: learningData
        }))
      }
    } catch (error) {
      console.error('Error loading user learning data:', error)
    }
  }

  // Request AI suggestions
  const requestAISuggestions = async (businessDesc?: string, analysis?: any) => {
    setAiAssistant(prev => ({ ...prev, isAnalyzing: true }))
    
    try {
      const description = businessDesc || invoiceData.businessDescription || 
        `${invoiceData.businessName} business providing ${invoiceData.lineItems.map(item => item.description).filter(Boolean).join(', ')}`
      
      if (!description.trim()) {
        setAiAssistant(prev => ({ ...prev, isAnalyzing: false }))
        return
      }

      let businessAnalysis = analysis
      if (!businessAnalysis) {
        businessAnalysis = await analyzeBusinessDescription(description)
        setAiAssistant(prev => ({ ...prev, businessAnalysis }))
      }

      // Generate various types of suggestions
      const suggestions: AISuggestion[] = []

      // Field suggestions
      if (!invoiceData.notes) {
        const aiNotes = await generateAIEnhancedContent(businessAnalysis, 'invoice_notes')
        suggestions.push({
          id: 'notes-suggestion',
          type: 'content',
          field: 'notes',
          title: 'Add Professional Notes',
          suggestion: aiNotes,
          reasoning: 'Professional notes improve client relationships and clarify expectations',
          confidence: 0.85,
          applied: false,
          dismissed: false,
          category: 'improvement'
        })
      }

      if (!invoiceData.paymentInstructions) {
        const aiPaymentTerms = await generateAIEnhancedContent(businessAnalysis, 'payment_terms')
        suggestions.push({
          id: 'payment-suggestion',
          type: 'content',
          field: 'paymentInstructions',
          title: 'Add Payment Instructions',
          suggestion: aiPaymentTerms,
          reasoning: 'Clear payment instructions reduce payment delays and disputes',
          confidence: 0.90,
          applied: false,
          dismissed: false,
          category: 'optimization'
        })
      }

      // Line item suggestions based on industry
      if (businessAnalysis.industry && invoiceData.lineItems.length === 1 && !invoiceData.lineItems[0].description) {
        const industryServices = getIndustryServiceSuggestions(businessAnalysis.industry)
        if (industryServices.length > 0) {
          suggestions.push({
            id: 'service-suggestions',
            type: 'field',
            field: 'lineItems',
            title: 'Add Common Services',
            suggestion: `Consider adding: ${industryServices.slice(0, 3).join(', ')}`,
            reasoning: `These are common services for ${businessAnalysis.industry} businesses`,
            confidence: 0.75,
            applied: false,
            dismissed: false,
            category: 'improvement'
          })
        }
      }

      // Pricing optimization suggestions
      if (invoiceData.total > 0) {
        const pricingAnalysis = analyzePricing(invoiceData, businessAnalysis, aiAssistant.userLearning)
        suggestions.push(...pricingAnalysis)
      }

      // Tax compliance suggestions
      if (businessAnalysis.industry === 'healthcare' || businessAnalysis.industry === 'legal') {
        suggestions.push({
          id: 'compliance-note',
          type: 'content',
          field: 'terms',
          title: 'Add Compliance Terms',
          suggestion: getComplianceTerms(businessAnalysis.industry),
          reasoning: `${businessAnalysis.industry} businesses often require specific compliance terms`,
          confidence: 0.80,
          applied: false,
          dismissed: false,
          category: 'compliance'
        })
      }

      setAiAssistant(prev => ({
        ...prev,
        suggestions,
        isAnalyzing: false
      }))

    } catch (error) {
      console.error('Error requesting AI suggestions:', error)
      setAiAssistant(prev => ({ ...prev, isAnalyzing: false }))
    }
  }

  // Get industry-specific service suggestions
  const getIndustryServiceSuggestions = (industry: string): string[] => {
    const suggestions = {
      technology: ['Website Development', 'Mobile App Development', 'Technical Consulting', 'Software Maintenance', 'System Integration'],
      creative: ['Logo Design', 'Brand Identity', 'Website Design', 'Print Design', 'Social Media Graphics'],
      consulting: ['Strategy Consultation', 'Business Analysis', 'Project Management', 'Market Research', 'Implementation Support'],
      healthcare: ['Consultation', 'Treatment', 'Follow-up Appointment', 'Diagnostic Services', 'Therapy Session'],
      legal: ['Legal Consultation', 'Document Review', 'Contract Drafting', 'Court Representation', 'Legal Research'],
      construction: ['Labor', 'Materials', 'Equipment Rental', 'Permits & Inspections', 'Project Management'],
      photography: ['Photo Session', 'Editing Services', 'Print Package', 'Digital Gallery', 'Travel Fee'],
      marketing: ['Campaign Development', 'Social Media Management', 'Content Creation', 'SEO Services', 'Analytics Reporting']
    }
    
    return suggestions[industry as keyof typeof suggestions] || []
  }

  // Analyze pricing for optimization suggestions
  const analyzePricing = (data: InvoiceData, analysis: any, learning: UserLearning): AISuggestion[] => {
    const suggestions: AISuggestion[] = []
    
    // Compare with user's average
    if (learning.averageInvoiceAmount > 0 && data.total < learning.averageInvoiceAmount * 0.7) {
      suggestions.push({
        id: 'pricing-low',
        type: 'pricing',
        title: 'Consider Higher Pricing',
        suggestion: `This invoice (${data.currency}${data.total.toFixed(2)}) is lower than your average (${data.currency}${learning.averageInvoiceAmount.toFixed(2)})`,
        reasoning: 'Consistent pricing helps maintain business value and client expectations',
        confidence: 0.70,
        applied: false,
        dismissed: false,
        category: 'optimization'
      })
    }

    // Suggest upsells
    if (data.lineItems.length === 1 && data.total > 500) {
      suggestions.push({
        id: 'upsell-opportunity',
        type: 'upsell',
        title: 'Upsell Opportunity',
        suggestion: 'Consider adding additional services or maintenance packages',
        reasoning: 'Larger projects often benefit from complementary services',
        confidence: 0.65,
        applied: false,
        dismissed: false,
        category: 'growth'
      })
    }

    return suggestions
  }

  // Get compliance terms for specific industries
  const getComplianceTerms = (industry: string): string => {
    const terms = {
      healthcare: 'This invoice complies with HIPAA regulations. Patient information is protected according to federal privacy laws.',
      legal: 'Attorney-client privilege applies to all services. Time is billed in 6-minute increments as per state bar requirements.',
      finance: 'All services comply with relevant financial regulations and reporting requirements.'
    }
    
    return terms[industry as keyof typeof terms] || ''
  }

  // Apply AI suggestion
  const applyAISuggestion = (suggestionId: string) => {
    const suggestion = aiAssistant.suggestions.find(s => s.id === suggestionId)
    if (!suggestion) return

    switch (suggestion.field) {
      case 'notes':
        setInvoiceData(prev => ({ ...prev, notes: suggestion.suggestion }))
        break
      case 'paymentInstructions':
        setInvoiceData(prev => ({ ...prev, paymentInstructions: suggestion.suggestion }))
        break
      case 'terms':
        setInvoiceData(prev => ({ 
          ...prev, 
          terms: prev.terms ? `${prev.terms}\n\n${suggestion.suggestion}` : suggestion.suggestion 
        }))
        break
      case 'lineItems':
        if (suggestion.id === 'service-suggestions' && aiAssistant.businessAnalysis) {
          const services = getIndustryServiceSuggestions(aiAssistant.businessAnalysis.industry)
          const newItems = services.slice(0, 3).map((service, index) => ({
            id: `ai-${Date.now()}-${index}`,
            description: service,
            quantity: 1,
            rate: 0,
            amount: 0,
            isAISuggested: true
          }))
          
          setInvoiceData(prev => ({
            ...prev,
            lineItems: prev.lineItems[0].description ? [...prev.lineItems, ...newItems] : newItems
          }))
        }
        break
    }

    // Mark suggestion as applied
    setAiAssistant(prev => ({
      ...prev,
      suggestions: prev.suggestions.map(s => 
        s.id === suggestionId ? { ...s, applied: true } : s
      )
    }))
  }

  // Dismiss AI suggestion
  const dismissAISuggestion = (suggestionId: string) => {
    setAiAssistant(prev => ({
      ...prev,
      suggestions: prev.suggestions.map(s => 
        s.id === suggestionId ? { ...s, dismissed: true } : s
      )
    }))
  }

  // Check invoice creation limit
  const checkInvoiceLimit = async () => {
    try {
      const response = await fetch('/api/invoices/can-create')
      const data = await response.json()
      
      setCanCreateInvoice(data.canCreate)
      setRemainingInvoices(data.remainingInvoices)
      
      if (!data.canCreate) {
        setShowUpgradePrompt(true)
      }
    } catch (error) {
      console.error('Error checking invoice limit:', error)
    }
  }

  // Load user's business information
  const loadUserBusinessInfo = async () => {
    if (!session?.user) return
    
    try {
      const response = await fetch('/api/user/profile')
      if (response.ok) {
        const userData = await response.json()
        
        if (userData.businessProfile) {
          setInvoiceData(prev => ({
            ...prev,
            businessName: userData.businessProfile.companyName || '',
            businessAddress: userData.businessProfile.address || '',
            businessCity: userData.businessProfile.city || '',
            businessPhone: userData.businessProfile.phone || '',
            businessLogo: userData.businessProfile.logo || '',
            businessEmail: session.user.email || '',
          }))
        } else {
          // Use basic user info
          setInvoiceData(prev => ({
            ...prev,
            businessName: session.user.name || '',
            businessEmail: session.user.email || '',
          }))
        }
      }
    } catch (error) {
      console.error('Error loading user profile:', error)
    }
  }

  // Generate unique invoice number
  // Apply template customizations
  const applyTemplateCustomizations = (template: InvoiceTemplate, customizations: any) => {
    setInvoiceData(prev => {
      const updated = { ...prev }
      
      // Apply custom field labels and content
      if (customizations.fieldLabels) {
        // Update placeholders and default values based on customizations
        Object.entries(customizations.fieldLabels).forEach(([field, label]) => {
          // This would be used in the UI to show custom labels
        })
      }
      
      // Apply payment terms
      if (customizations.paymentTerms) {
        updated.terms = customizations.paymentTerms
      }
      
      // Apply notes
      if (customizations.notes) {
        updated.notes = customizations.notes
      }
      
      return updated
    })
  }
  
  // Apply template defaults
  const applyTemplateDefaults = (template: InvoiceTemplate) => {
    setInvoiceData(prev => ({
      ...prev,
      // Apply template-specific defaults
      currency: template.id === 'photography' ? '$' : prev.currency,
      taxRate: template.industry === 'Service Business' ? 8.25 : prev.taxRate,
      terms: template.id === 'consulting' ? 'Payment due within 30 days of invoice date' : prev.terms
    }))
  }
  
  const generateNewInvoiceNumber = async () => {
    try {
      const response = await fetch('/api/invoices/generate-number')
      if (response.ok) {
        const { invoiceNumber } = await response.json()
        setInvoiceData(prev => ({ ...prev, invoiceNumber }))
      } else {
        setInvoiceData(prev => ({ ...prev, invoiceNumber: generateInvoiceNumber() }))
      }
    } catch (error) {
      console.error('Error generating invoice number:', error)
      setInvoiceData(prev => ({ ...prev, invoiceNumber: generateInvoiceNumber() }))
    }
  }

  // Auto-save functionality
  const autoSave = useCallback(async () => {
    if (!session?.user || !canCreateInvoice) return
    
    try {
      const endpoint = draftId ? `/api/invoices/${draftId}` : '/api/invoices'
      const method = draftId ? 'PUT' : 'POST'
      
      const response = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...invoiceData,
          status: 'draft',
          userId: session.user.id
        })
      })
      
      if (response.ok) {
        const saved = await response.json()
        if (!draftId) {
          setDraftId(saved._id)
        }
        setLastSaved(new Date())
      }
    } catch (error) {
      console.error('Error auto-saving:', error)
    }
  }, [invoiceData, session, draftId, canCreateInvoice])

  // Set up auto-save interval
  useEffect(() => {
    if (!session?.user || !canCreateInvoice) return
    
    const interval = setInterval(() => {
      autoSave()
    }, 30000)
    
    return () => clearInterval(interval)
  }, [autoSave, session, canCreateInvoice])

  // Smart field suggestions based on context
  const getSmartPlaceholder = (field: string, context: any = {}) => {
    if (aiAssistant.businessAnalysis) {
      const industry = aiAssistant.businessAnalysis.industry
      const businessType = aiAssistant.businessAnalysis.businessType
      
      const placeholders = {
        businessName: {
          technology: 'TechCorp Solutions',
          creative: 'Creative Studio',
          consulting: 'Strategic Consulting Group',
          healthcare: 'Medical Practice',
          legal: 'Law Firm LLC'
        },
        clientCompany: {
          technology: 'Startup Inc.',
          creative: 'Brand Co.',
          consulting: 'Enterprise Corp.',
          healthcare: 'Health System',
          legal: 'Business Corporation'
        },
        notes: {
          technology: 'Thank you for choosing our technical services. We look forward to supporting your digital transformation.',
          creative: 'Thank you for trusting us with your creative vision. We\'re excited to bring your brand to life.',
          consulting: 'We appreciate the opportunity to contribute to your strategic success.',
          healthcare: 'Thank you for choosing our healthcare services. Your health and wellness are our priority.',
          legal: 'Thank you for entrusting us with your legal matters. We are committed to protecting your interests.'
        }
      }
      
      return (placeholders as any)[field]?.[industry] || (placeholders as any)[field]?.technology || 'Enter value'
    }
    
    const defaultPlaceholders = {
      businessName: 'Your Business Name',
      clientCompany: 'Client Company',
      notes: 'Additional notes or comments'
    }
    
    return defaultPlaceholders[field as keyof typeof defaultPlaceholders] || 'Enter value'
  }

  // Handle logo upload
  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setInvoiceData(prev => ({ ...prev, businessLogo: reader.result as string }))
      }
      reader.readAsDataURL(file)
    }
  }

  // Add line item
  const addLineItem = () => {
    const newItem: LineItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      rate: 0,
      amount: 0
    }
    setInvoiceData(prev => ({
      ...prev,
      lineItems: [...prev.lineItems, newItem]
    }))
  }

  // Remove line item
  const removeLineItem = (id: string) => {
    setInvoiceData(prev => ({
      ...prev,
      lineItems: prev.lineItems.filter(item => item.id !== id)
    }))
  }

  // Update line item with AI suggestions
  const updateLineItem = (id: string, field: keyof LineItem, value: any) => {
    setInvoiceData(prev => {
      const updatedItems = prev.lineItems.map(item => {
        if (item.id === id) {
          const updated = { ...item, [field]: value }
          
          // Calculate amount
          if (field === 'quantity' || field === 'rate') {
            updated.amount = updated.quantity * updated.rate
          }
          
          // AI suggestion for rates based on learning
          if (field === 'description' && value && aiAssistant.userLearning.frequentServices.length > 0) {
            const commonService = aiAssistant.userLearning.frequentServices.find(s => 
              s.toLowerCase().includes(value.toLowerCase()) || 
              value.toLowerCase().includes(s.toLowerCase())
            )
            
            if (commonService && updated.rate === 0) {
              // Suggest a rate based on user's history
              updated.rate = aiAssistant.userLearning.averageInvoiceAmount / 10 // Simple heuristic
              updated.amount = updated.quantity * updated.rate
            }
          }
          
          return updated
        }
        return item
      })

      // Calculate totals
      const subtotal = updatedItems.reduce((sum, item) => sum + item.amount, 0)
      const tax = subtotal * (prev.taxRate / 100)
      const total = subtotal + tax

      return {
        ...prev,
        lineItems: updatedItems,
        subtotal,
        tax,
        total
      }
    })
  }

  // Update tax rate
  const updateTaxRate = (rate: number) => {
    setInvoiceData(prev => {
      const tax = prev.subtotal * (rate / 100)
      const total = prev.subtotal + tax
      return { ...prev, taxRate: rate, tax, total }
    })
  }

  // Save invoice
  const saveInvoice = async (status: 'draft' | 'sent' = 'draft') => {
    if (!session?.user) {
      setShowUpgradePrompt(true)
      setNotification({ 
        type: 'info', 
        message: 'Sign up to save your invoices to dashboard' 
      })
      return
    }

    if (!canCreateInvoice) {
      setShowUpgradePrompt(true)
      return
    }

    setIsLoading(true)
    
    try {
      const endpoint = draftId ? `/api/invoices/${draftId}` : '/api/invoices'
      const method = draftId ? 'PUT' : 'POST'
      
      const response = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...invoiceData,
          status,
          userId: session.user.id,
          aiSuggestions: aiAssistant.suggestions.filter(s => s.applied)
        })
      })

      if (response.ok) {
        const savedInvoice = await response.json()
        setDraftId(savedInvoice._id)
        setLastSaved(new Date())
        
        // Save client info and update learning data
        if (invoiceData.clientEmail) {
          await saveClientInfo()
        }
        await updateUserLearning()
        
        alert(`Invoice ${status === 'draft' ? 'saved as draft' : 'created'} successfully!`)
        
        if (status === 'sent') {
          router.push('/my-invoices')
        }
      } else if (response.status === 403) {
        const data = await response.json()
        setShowUpgradePrompt(true)
      } else {
        throw new Error('Failed to save invoice')
      }
    } catch (error) {
      console.error('Error saving invoice:', error)
      alert('Failed to save invoice. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Update user learning data
  const updateUserLearning = async () => {
    try {
      const learningUpdate = {
        frequentServices: invoiceData.lineItems.map(item => item.description).filter(Boolean),
        invoiceAmount: invoiceData.total,
        clientType: invoiceData.clientType,
        terms: [invoiceData.terms, invoiceData.paymentInstructions].filter(Boolean),
        industry: aiAssistant.businessAnalysis?.industry
      }
      
      await fetch('/api/user/learning', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(learningUpdate)
      })
    } catch (error) {
      console.error('Error updating learning data:', error)
    }
  }

  // Save client information for future reuse
  const saveClientInfo = async () => {
    try {
      await fetch('/api/clients', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: invoiceData.clientName,
          email: invoiceData.clientEmail,
          phone: invoiceData.clientPhone,
          address: {
            street: invoiceData.clientAddress,
            city: invoiceData.clientCity,
            zipCode: invoiceData.clientZip
          },
          company: invoiceData.clientCompany,
          type: invoiceData.clientType
        })
      })
    } catch (error) {
      console.error('Error saving client info:', error)
    }
  }

  // Download as PDF
  const downloadPDF = async () => {
    if (!canGeneratePDF()) {
      setNotification({ 
        type: 'error', 
        message: getValidationMessage() 
      })
      return
    }

    // Show upgrade prompt for guests
    if (!session?.user) {
      setShowUpgradePrompt(true)
      setNotification({ 
        type: 'info', 
        message: 'Sign up to download PDF invoices' 
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/invoices/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateId: selectedTemplate?.id || 'modern',
          invoiceData: invoiceData,
          options: {
            format: 'A4',
            margin: '0.5in'
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate PDF')
      }

      // Get the PDF blob
      const blob = await response.blob()
      
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `invoice-${invoiceData.invoiceNumber || 'draft'}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      setNotification({ type: 'success', message: 'PDF downloaded successfully!' })

    } catch (error) {
      console.error('Error downloading PDF:', error)
      setNotification({ 
        type: 'error', 
        message: `Failed to download PDF: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Send invoice via email
  const sendInvoiceEmail = async () => {
    if (!canSendEmail()) {
      setNotification({ 
        type: 'error', 
        message: getValidationMessage() 
      })
      return
    }

    // Show upgrade prompt for guests
    if (!session?.user) {
      setShowUpgradePrompt(true)
      setNotification({ 
        type: 'info', 
        message: 'Sign up to send invoices via email' 
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/invoices/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateId: selectedTemplate?.id || 'modern',
          invoiceData: invoiceData,
          recipientEmail: invoiceData.clientEmail,
          senderEmail: invoiceData.businessEmail,
          options: {
            subject: `Invoice ${invoiceData.invoiceNumber} from ${invoiceData.businessName}`,
            includePDF: true
          }
        })
      })

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to send email')
      }

      setNotification({ 
        type: 'success', 
        message: `Invoice email sent successfully to ${invoiceData.clientEmail}!` 
      })
      
      // Save invoice as sent after successful email
      await saveInvoice('sent')

    } catch (error) {
      console.error('Error sending email:', error)
      setNotification({ 
        type: 'error', 
        message: `Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Print invoice
  const printInvoice = () => {
    window.print()
  }

  // Validation helpers
  const canGeneratePDF = () => {
    return selectedTemplate && invoiceData.businessName && invoiceData.clientName && invoiceData.lineItems.some(item => item.description)
  }

  const canSendEmail = () => {
    return canGeneratePDF() && invoiceData.clientEmail && invoiceData.businessEmail
  }

  const getValidationMessage = () => {
    if (!selectedTemplate) return 'Please select a template'
    if (!invoiceData.businessName) return 'Please enter your business name'
    if (!invoiceData.clientName) return 'Please enter client name'
    if (!invoiceData.lineItems.some(item => item.description)) return 'Please add at least one line item'
    if (!invoiceData.clientEmail) return 'Please enter client email for sending'
    if (!invoiceData.businessEmail) return 'Please enter your business email'
    return 'Invoice is ready!'
  }

  const sections = [
    { id: 'business', label: 'Business Info', icon: Building },
    { id: 'client', label: 'Client Info', icon: User },
    { id: 'details', label: 'Invoice Details', icon: FileText },
    { id: 'items', label: 'Line Items', icon: DollarSign },
    { id: 'notes', label: 'Notes & Terms', icon: FileText }
  ]

  // Show loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex h-screen">
        {/* Left Panel - Form */}
        <div className="w-1/2 bg-white shadow-lg overflow-y-auto">
          <div className="p-6 pb-20">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Create Invoice</h1>
                {selectedTemplate && (
                  <p className="text-sm text-gray-600 mt-1">
                    Using template: <span className="font-medium">{selectedTemplate.name}</span>
                    {templateCustomizations && <span className="text-purple-600 ml-2">• AI Customized</span>}
                  </p>
                )}
              </div>
              <div className="flex items-center space-x-4">
                {/* Guest indicator */}
                {!session?.user && (
                  <div className="flex items-center gap-2 text-sm bg-blue-50 text-blue-700 px-3 py-1 rounded-full">
                    <Eye className="w-4 h-4" />
                    <span>Guest Mode</span>
                  </div>
                )}
                {/* Template Switcher */}
                <div className="relative">
                  <select
                    value={selectedTemplate?.id || ''}
                    onChange={(e) => {
                      const newTemplate = getTemplateById(e.target.value)
                      if (newTemplate) {
                        setSelectedTemplate(newTemplate)
                        applyTemplateDefaults(newTemplate)
                        // Clear customizations when switching templates
                        setTemplateCustomizations(null)
                        localStorage.removeItem('templateCustomizations')
                        localStorage.removeItem('generatedTemplateData')
                        setNotification({ 
                          type: 'success', 
                          message: `Selected ${newTemplate.name} template` 
                        })
                      } else {
                        setSelectedTemplate(null)
                      }
                    }}
                    className={`text-sm bg-white border rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      selectedTemplate ? 'border-gray-300' : 'border-orange-300 bg-orange-50'
                    }`}
                  >
                    <option value="">Select Template Required</option>
                    {invoiceTemplates.map(template => (
                      <option key={template.id} value={template.id}>
                        {template.name} - {template.industry}
                      </option>
                    ))}
                  </select>
                  {!selectedTemplate && (
                    <div className="absolute -top-1 -right-1">
                      <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                    </div>
                  )}
                </div>
                
                {!selectedTemplate && (
                  <Button
                    onClick={() => router.push('/templates')}
                    variant="outline"
                    size="sm"
                    className="text-xs border-orange-300 text-orange-700 hover:bg-orange-50"
                  >
                    Browse Templates
                  </Button>
                )}
                
                {lastSaved && (
                  <span className="text-sm text-gray-500 flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    Saved {lastSaved.toLocaleTimeString()}
                  </span>
                )}
                <UsageIndicator 
                  variant="compact"
                  showUpgradeButton={false}
                />
              </div>
            </div>

            {/* Notification */}
            <AnimatePresence>
              {notification && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className={`mb-6 p-4 rounded-lg border flex items-start justify-between ${
                    notification.type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
                    notification.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
                    'bg-blue-50 border-blue-200 text-blue-800'
                  }`}
                >
                  <div className="flex items-start">
                    {notification.type === 'success' ? (
                      <CheckCircle className="w-5 h-5 mr-3 mt-0.5 text-green-600" />
                    ) : notification.type === 'error' ? (
                      <XCircle className="w-5 h-5 mr-3 mt-0.5 text-red-600" />
                    ) : (
                      <AlertCircle className="w-5 h-5 mr-3 mt-0.5 text-blue-600" />
                    )}
                    <p className="text-sm">{notification.message}</p>
                  </div>
                  <button
                    onClick={() => setNotification(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XCircle className="w-4 h-4" />
                  </button>
                </motion.div>
              )}
            </AnimatePresence>

            {/* AI Assistant Panel */}
            <div className="mb-6">
              <AIAssistantPanel
                assistant={aiAssistant}
                onApplySuggestion={applyAISuggestion}
                onDismissSuggestion={dismissAISuggestion}
                onRequestSuggestions={() => requestAISuggestions()}
                invoiceData={invoiceData}
              />
            </div>

            {/* Usage Warning */}
            {remainingInvoices !== null && remainingInvoices <= 1 && remainingInvoices > 0 && (
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg flex items-start">
                <AlertCircle className="w-5 h-5 text-yellow-600 mr-3 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm text-yellow-800">
                    You have {remainingInvoices} invoice{remainingInvoices === 1 ? '' : 's'} remaining this month.
                  </p>
                  <button
                    onClick={() => setShowUpgradePrompt(true)}
                    className="text-sm text-yellow-700 underline hover:text-yellow-800 mt-1"
                  >
                    Upgrade to Pro for unlimited invoices
                  </button>
                </div>
              </div>
            )}

            {/* Section Tabs */}
            <div className="flex space-x-2 mb-6 overflow-x-auto">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeSection === section.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <section.icon className="w-4 h-4 mr-2" />
                  {section.label}
                </button>
              ))}
            </div>

            {/* Form Sections */}
            <div className="space-y-6">
              {/* Business Information */}
              {activeSection === 'business' && (
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                      <Building className="w-5 h-5 mr-2" />
                      Business Information
                    </h2>
                    <Button
                      onClick={() => requestAISuggestions()}
                      variant="ghost"
                      size="sm"
                      className="text-purple-600 hover:text-purple-700"
                    >
                      <Wand2 className="w-4 h-4 mr-1" />
                      AI Assist
                    </Button>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Business Name
                      </label>
                      <Input
                        value={invoiceData.businessName}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, businessName: e.target.value }))}
                        placeholder={getSmartPlaceholder('businessName')}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email
                        </label>
                        <Input
                          type="email"
                          value={invoiceData.businessEmail}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, businessEmail: e.target.value }))}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone
                        </label>
                        <Input
                          value={invoiceData.businessPhone}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, businessPhone: e.target.value }))}
                          placeholder="(*************"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Address
                      </label>
                      <Input
                        value={invoiceData.businessAddress}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, businessAddress: e.target.value }))}
                        placeholder="123 Business St"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          City
                        </label>
                        <Input
                          value={invoiceData.businessCity}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, businessCity: e.target.value }))}
                          placeholder="New York, NY"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          ZIP Code
                        </label>
                        <Input
                          value={invoiceData.businessZip}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, businessZip: e.target.value }))}
                          placeholder="10001"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Website (Optional)
                      </label>
                      <Input
                        value={invoiceData.businessWebsite}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, businessWebsite: e.target.value }))}
                        placeholder="www.yourbusiness.com"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tax ID (Optional)
                      </label>
                      <Input
                        value={invoiceData.taxId}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, taxId: e.target.value }))}
                        placeholder="XX-XXXXXXX"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Logo (Optional)
                      </label>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full"
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Logo
                      </Button>
                    </div>
                  </div>
                </Card>
              )}

              {/* Client Information */}
              {activeSection === 'client' && (
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                      <User className="w-5 h-5 mr-2" />
                      Client Information
                    </h2>
                    {aiAssistant.businessAnalysis && (
                      <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                        AI Enhanced
                      </span>
                    )}
                  </div>
                  
                  {/* Client Type Selection */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Client Type
                    </label>
                    <div className="grid grid-cols-4 gap-2">
                      {['new', 'recurring', 'business', 'individual'].map((type) => (
                        <button
                          key={type}
                          onClick={() => setInvoiceData(prev => ({ ...prev, clientType: type as any }))}
                          className={`px-3 py-2 text-xs rounded-lg border transition-colors ${
                            invoiceData.clientType === type
                              ? 'bg-blue-600 text-white border-blue-600'
                              : 'bg-white text-gray-600 border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Client Name
                      </label>
                      <Input
                        value={invoiceData.clientName}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, clientName: e.target.value }))}
                        placeholder="Client Name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company (Optional)
                      </label>
                      <Input
                        value={invoiceData.clientCompany}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, clientCompany: e.target.value }))}
                        placeholder={getSmartPlaceholder('clientCompany')}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email (Optional)
                        </label>
                        <Input
                          type="email"
                          value={invoiceData.clientEmail}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, clientEmail: e.target.value }))}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone (Optional)
                        </label>
                        <Input
                          value={invoiceData.clientPhone}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, clientPhone: e.target.value }))}
                          placeholder="(*************"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Address
                      </label>
                      <Input
                        value={invoiceData.clientAddress}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, clientAddress: e.target.value }))}
                        placeholder="123 Client St"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          City
                        </label>
                        <Input
                          value={invoiceData.clientCity}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, clientCity: e.target.value }))}
                          placeholder="Los Angeles, CA"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          ZIP Code
                        </label>
                        <Input
                          value={invoiceData.clientZip}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, clientZip: e.target.value }))}
                          placeholder="90001"
                        />
                      </div>
                    </div>
                  </div>
                </Card>
              )}

              {/* Invoice Details */}
              {activeSection === 'details' && (
                <Card className="p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <FileText className="w-5 h-5 mr-2" />
                    Invoice Details
                  </h2>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Invoice Number
                      </label>
                      <Input
                        value={invoiceData.invoiceNumber}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                        placeholder="INV-001"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Invoice Date
                        </label>
                        <Input
                          type="date"
                          value={invoiceData.invoiceDate}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, invoiceDate: e.target.value }))}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Due Date
                        </label>
                        <Input
                          type="date"
                          value={invoiceData.dueDate}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, dueDate: e.target.value }))}
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Currency
                      </label>
                      <select
                        value={invoiceData.currency}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, currency: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="$">USD ($)</option>
                        <option value="€">EUR (€)</option>
                        <option value="£">GBP (£)</option>
                        <option value="¥">JPY (¥)</option>
                        <option value="₹">INR (₹)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tax Rate (%)
                      </label>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        value={invoiceData.taxRate}
                        onChange={(e) => updateTaxRate(parseFloat(e.target.value) || 0)}
                        placeholder="0"
                      />
                    </div>
                  </div>
                </Card>
              )}

              {/* Line Items */}
              {activeSection === 'items' && (
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                      <DollarSign className="w-5 h-5 mr-2" />
                      Line Items
                    </h2>
                    {aiAssistant.businessAnalysis && (
                      <Button
                        onClick={() => requestAISuggestions()}
                        variant="ghost"
                        size="sm"
                        className="text-purple-600 hover:text-purple-700"
                      >
                        <Lightbulb className="w-4 h-4 mr-1" />
                        Suggest Services
                      </Button>
                    )}
                  </div>
                  <div className="space-y-4">
                    {invoiceData.lineItems.map((item, index) => (
                      <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-gray-700">Item {index + 1}</span>
                            {item.isAISuggested && (
                              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                <Sparkles className="w-2 h-2 mr-1" />
                                AI Suggested
                              </span>
                            )}
                          </div>
                          {invoiceData.lineItems.length > 1 && (
                            <button
                              onClick={() => removeLineItem(item.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                        <div className="space-y-3">
                          <Input
                            value={item.description}
                            onChange={(e) => updateLineItem(item.id, 'description', e.target.value)}
                            placeholder="Service or product description"
                          />
                          <div className="grid grid-cols-3 gap-3">
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Quantity</label>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.quantity}
                                onChange={(e) => updateLineItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                              />
                            </div>
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Rate</label>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.rate}
                                onChange={(e) => updateLineItem(item.id, 'rate', parseFloat(e.target.value) || 0)}
                              />
                            </div>
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Amount</label>
                              <Input
                                type="number"
                                value={item.amount}
                                readOnly
                                className="bg-gray-50"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addLineItem}
                      className="w-full"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Line Item
                    </Button>
                    
                    {/* Pricing insights */}
                    {invoiceData.total > 0 && aiAssistant.userLearning.averageInvoiceAmount > 0 && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-2">
                          <BarChart3 className="w-4 h-4 text-blue-600" />
                          <span className="text-sm font-medium text-blue-900">Pricing Insight</span>
                        </div>
                        <p className="text-sm text-blue-700">
                          This invoice total ({invoiceData.currency}{invoiceData.total.toFixed(2)}) is{' '}
                          {invoiceData.total > aiAssistant.userLearning.averageInvoiceAmount ? 'above' : 'below'} your average of{' '}
                          {invoiceData.currency}{aiAssistant.userLearning.averageInvoiceAmount.toFixed(2)}
                        </p>
                      </div>
                    )}
                  </div>
                </Card>
              )}

              {/* Notes & Terms */}
              {activeSection === 'notes' && (
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                      <FileText className="w-5 h-5 mr-2" />
                      Notes & Terms
                    </h2>
                    <Button
                      onClick={() => requestAISuggestions()}
                      variant="ghost"
                      size="sm"
                      className="text-purple-600 hover:text-purple-700"
                    >
                      <Wand2 className="w-4 h-4 mr-1" />
                      AI Enhance
                    </Button>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Notes (Optional)
                      </label>
                      <textarea
                        value={invoiceData.notes}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, notes: e.target.value }))}
                        placeholder={getSmartPlaceholder('notes')}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Instructions (Optional)
                      </label>
                      <textarea
                        value={invoiceData.paymentInstructions}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, paymentInstructions: e.target.value }))}
                        placeholder="Payment methods, bank details, or online payment links"
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Terms & Conditions (Optional)
                      </label>
                      <textarea
                        value={invoiceData.terms}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, terms: e.target.value }))}
                        placeholder="Payment terms, late fees, cancellation policy, etc."
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </Card>
              )}
            </div>

            {/* Action Buttons */}
            <div className="mt-6 flex space-x-3">
              <Button
                onClick={() => saveInvoice('draft')}
                variant="outline"
                className="flex-1"
                disabled={isLoading || (!session?.user ? false : !canCreateInvoice)}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {!session?.user ? 'Save Draft (Sign up)' : 'Save Draft'}
              </Button>
              <Button
                onClick={() => saveInvoice('sent')}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                disabled={isLoading || (!session?.user ? false : !canCreateInvoice)}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <FileText className="w-4 h-4 mr-2" />
                )}
                {!session?.user ? 'Create Invoice (Sign up)' : 'Create Invoice'}
              </Button>
            </div>
            <div className="mt-3 flex space-x-3">
              <Button
                onClick={downloadPDF}
                variant="outline"
                className="flex-1"
                disabled={isLoading || !canGeneratePDF()}
                title={!canGeneratePDF() ? getValidationMessage() : (!session?.user ? 'Sign up to download PDF' : 'Download invoice as PDF')}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Download className="w-4 h-4 mr-2" />
                )}
                {!session?.user ? 'Download PDF (Pro)' : 'Download PDF'}
              </Button>
              <Button
                onClick={sendInvoiceEmail}
                variant="outline"
                className="flex-1"
                disabled={isLoading || !canSendEmail()}
                title={!canSendEmail() ? getValidationMessage() : (!session?.user ? 'Sign up to send email' : 'Send invoice via email')}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Send className="w-4 h-4 mr-2" />
                )}
                {!session?.user ? 'Send Email (Pro)' : 'Send Email'}
              </Button>
            </div>
            <div className="mt-3 flex space-x-3">
              <Button
                onClick={printInvoice}
                variant="outline"
                className="flex-1"
                disabled={isLoading}
              >
                <Printer className="w-4 h-4 mr-2" />
                Print
              </Button>
              <div className="flex-1">
                {/* Status indicator */}
                <div className="text-center mt-2">
                  {canSendEmail() ? (
                    <div className="flex items-center justify-center text-green-600">
                      <CheckCircle className="w-4 h-4 mr-1" />
                      <span className="text-xs">Ready to send</span>
                    </div>
                  ) : canGeneratePDF() ? (
                    <div className="flex items-center justify-center text-blue-600">
                      <Eye className="w-4 h-4 mr-1" />
                      <span className="text-xs">Ready for PDF</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center text-gray-500">
                      <HelpCircle className="w-4 h-4 mr-1" />
                      <span className="text-xs">{getValidationMessage()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Upgrade Prompt for Free Users and Guests */}
            {(!session?.user || !canCreateInvoice) && (
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start">
                  <Zap className="w-5 h-5 text-blue-600 mr-3 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-blue-900">
                      {!session?.user ? 'Sign Up for Full Access' : 'Upgrade to Pro for Unlimited Invoices'}
                    </p>
                    <p className="text-sm text-blue-700 mt-1">
                      {!session?.user 
                        ? 'Create your account to save invoices, download PDFs, and send emails directly to clients.'
                        : "You've reached your monthly limit. Upgrade now to continue creating invoices with AI assistance."
                      }
                    </p>
                    <div className="flex gap-2 mt-3">
                      {!session?.user ? (
                        <>
                          <Button
                            onClick={() => signIn('google')}
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                            size="sm"
                          >
                            Sign Up Free
                          </Button>
                          <Button
                            onClick={() => router.push('/pricing')}
                            variant="outline"
                            size="sm"
                          >
                            View Pricing
                          </Button>
                        </>
                      ) : (
                        <Button
                          onClick={() => router.push('/upgrade')}
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          size="sm"
                        >
                          Upgrade to Pro
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Live Preview */}
        <div className="w-1/2 bg-gray-100 overflow-y-auto p-8">
          <div className="max-w-[8.5in] mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <InvoicePreview data={invoiceData} template={selectedTemplate} />
            </motion.div>
          </div>
        </div>
      </div>

      {/* Upgrade Prompt Modal */}
      <UpgradePrompt
        isOpen={showUpgradePrompt}
        onClose={() => setShowUpgradePrompt(false)}
        remainingInvoices={remainingInvoices || 0}
        reason="You've reached your monthly invoice limit"
        source="create_invoice_page"
        variant="limit"
      />

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          #invoice-preview, #invoice-preview * {
            visibility: visible;
          }
          #invoice-preview {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
        }
      `}</style>
    </div>
  )
}

export default function CreateInvoicePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <h2 className="text-xl text-gray-600 mt-4">Loading invoice creator...</h2>
        </div>
      </div>
    }>
      <CreateInvoicePageContent />
    </Suspense>
  )
}