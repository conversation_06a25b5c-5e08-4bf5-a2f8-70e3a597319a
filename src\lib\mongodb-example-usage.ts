/**
 * Example Usage of MongoDB Schemas
 * 
 * This file demonstrates how to use the MongoDB schemas and repositories
 * defined in mongodb-schemas.ts
 */

import { ObjectId } from 'mongodb';
import { 
  Users, 
  Invoices, 
  Clients,
  type CreateUserInput,
  type CreateInvoiceInput,
  type CreateClientInput,
  createIndexes,
  validateSchemas
} from './mongodb-schemas';

/**
 * Example: User Management Operations
 */
async function userExamples() {
  console.log('=== User Management Examples ===');
  
  // Create a new user
  const newUserData: CreateUserInput = {
    email: '<EMAIL>',
    name: '<PERSON>',
    googleId: 'google-oauth-id-123',
    image: 'https://example.com/avatar.jpg',
    subscription: {
      plan: 'free',
      invoicesUsed: 0,
      resetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    },
    businessProfile: {
      companyName: '<PERSON>',
      address: '123 Business St',
      city: 'San Francisco',
      country: 'USA',
      phone: '******-123-4567',
      website: 'https://johndoe.com'
    },
    preferences: {
      defaultCurrency: 'USD',
      timezone: 'America/Los_Angeles',
      theme: 'light'
    }
  };
  
  // Create user
  try {
    const user = await Users.create(newUserData);
    console.log('✅ User created:', user.email);
    
    // Find user by email
    const foundUser = await Users.findByEmail('<EMAIL>');
    console.log('✅ User found by email:', foundUser?.name);
    
    // Update user's last login
    await Users.updateLastLogin('<EMAIL>');
    console.log('✅ User last login updated');
    
    // Update subscription
    await Users.updateSubscription(user._id!, { plan: 'pro' });
    console.log('✅ User upgraded to Pro');
    
    // Get user statistics
    const stats = await Users.getStats(user._id!);
    console.log('✅ User stats:', stats);
    
    return user;
  } catch (error) {
    console.error('❌ User operation failed:', error);
    return null;
  }
}

/**
 * Example: Client Management Operations
 */
async function clientExamples(userId: ObjectId) {
  console.log('\n=== Client Management Examples ===');
  
  const newClientData: CreateClientInput = {
    userId,
    name: 'Acme Corporation',
    email: '<EMAIL>',
    phone: '******-987-6543',
    address: {
      street: '456 Corporate Blvd',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA'
    },
    contactPerson: {
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '******-987-6544',
      title: 'CFO'
    },
    businessInfo: {
      type: 'Technology',
      size: 'large',
      taxId: 'TAX-*********'
    },
    paymentTerms: 'Net 30',
    preferredCurrency: 'USD',
    status: 'active',
    tags: ['technology', 'enterprise', 'priority']
  };
  
  try {
    // Create client
    const client = await Clients.create(newClientData);
    console.log('✅ Client created:', client.name);
    
    // Find client by email
    const foundClient = await Clients.findByEmail(userId, '<EMAIL>');
    console.log('✅ Client found by email:', foundClient?.name);
    
    // Search clients
    const searchResults = await Clients.search(userId, 'Acme', { limit: 10 });
    console.log('✅ Search results:', searchResults.length, 'clients found');
    
    // Update client
    await Clients.update(client._id!, { 
      notes: 'Important enterprise client with high volume needs',
      businessInfo: {
        ...client.businessInfo,
        annualRevenue: 5000000
      }
    });
    console.log('✅ Client updated with notes and revenue');
    
    return client;
  } catch (error) {
    console.error('❌ Client operation failed:', error);
    return null;
  }
}

/**
 * Example: Invoice Management Operations
 */
async function invoiceExamples(userId: ObjectId, clientId: ObjectId) {
  console.log('\n=== Invoice Management Examples ===');
  
  const newInvoiceData: CreateInvoiceInput = {
    userId,
    invoiceNumber: '', // Will be auto-generated
    status: 'draft',
    businessInfo: {
      name: 'John Doe Consulting',
      address: '123 Business St',
      city: 'San Francisco',
      email: '<EMAIL>',
      phone: '******-123-4567',
      website: 'https://johndoe.com'
    },
    clientInfo: {
      name: 'Acme Corporation',
      email: '<EMAIL>',
      address: '456 Corporate Blvd',
      city: 'New York',
      phone: '******-987-6543'
    },
    invoiceDate: new Date(),
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    terms: 'Payment due within 30 days. Late fees apply after due date.',
    lineItems: [
      {
        id: '1',
        description: 'Website Development',
        quantity: 1,
        rate: 5000,
        amount: 5000,
        taxable: true,
        category: 'Development'
      },
      {
        id: '2',
        description: 'SEO Optimization',
        quantity: 10,
        rate: 150,
        amount: 1500,
        taxable: true,
        category: 'Marketing'
      }
    ],
    totals: {
      subtotal: 6500,
      taxRate: 0.0875, // 8.75% tax
      taxAmount: 568.75,
      total: 7068.75
    },
    templateId: 'modern-blue',
    currency: 'USD',
    notes: 'Thank you for your business! Payment can be made via bank transfer or check.'
  };
  
  try {
    // Create invoice
    const invoice = await Invoices.create(newInvoiceData);
    console.log('✅ Invoice created:', invoice.invoiceNumber);
    
    // Find invoices by user
    const userInvoices = await Invoices.findByUserId(userId, { 
      limit: 10, 
      sortBy: 'createdAt', 
      sortOrder: 'desc' 
    });
    console.log('✅ User invoices found:', userInvoices.length);
    
    // Update invoice status
    await Invoices.updateStatus(invoice._id!, 'sent');
    console.log('✅ Invoice marked as sent');
    
    // Search invoices
    const searchResults = await Invoices.search(userId, 'Acme', { limit: 5 });
    console.log('✅ Invoice search results:', searchResults.length);
    
    // Get invoice statistics
    const stats = await Invoices.getStatsByUserId(userId);
    console.log('✅ Invoice statistics:', stats);
    
    // Update client's invoice history
    await Clients.updateInvoiceHistory(clientId, invoice.totals.total, true);
    console.log('✅ Client invoice history updated');
    
    return invoice;
  } catch (error) {
    console.error('❌ Invoice operation failed:', error);
    return null;
  }
}

/**
 * Example: Database Setup Operations
 */
async function databaseSetupExamples() {
  console.log('\n=== Database Setup Examples ===');
  
  try {
    // Create indexes for optimal performance
    await createIndexes();
    console.log('✅ Database indexes created');
    
    // Validate schemas
    await validateSchemas();
    console.log('✅ Schema validation rules applied');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
  }
}

/**
 * Main example function that demonstrates all operations
 */
export async function runMongoDBExamples() {
  console.log('🚀 Starting MongoDB Schemas Examples...\n');
  
  try {
    // 1. Database setup
    await databaseSetupExamples();
    
    // 2. User operations
    const user = await userExamples();
    if (!user) return;
    
    // 3. Client operations
    const client = await clientExamples(user._id!);
    if (!client) return;
    
    // 4. Invoice operations
    const invoice = await invoiceExamples(user._id!, client._id!);
    if (!invoice) return;
    
    console.log('\n🎉 All MongoDB operations completed successfully!');
    console.log('\nCreated:');
    console.log(`- User: ${user.name} (${user.email})`);
    console.log(`- Client: ${client.name} (${client.email})`);
    console.log(`- Invoice: ${invoice.invoiceNumber} ($${invoice.totals.total})`);
    
  } catch (error) {
    console.error('💥 Example execution failed:', error);
  }
}

// Export for use in other parts of the application
export {
  userExamples,
  clientExamples,
  invoiceExamples,
  databaseSetupExamples
};