/**
 * Simple Template Engine
 * A lightweight alternative to Handlebars that avoids webpack issues
 */

/**
 * Helper function to safely access nested properties
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Format currency values
 */
function formatCurrency(amount: number | string): string {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  return `$${num.toFixed(2)}`;
}

/**
 * Format date values
 */
function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
}

/**
 * Process helper functions in templates
 */
function processHelpers(template: string, data: any): string {
  let result = template;
  
  // {{formatCurrency value}}
  result = result.replace(/{{formatCurrency\s+([^}]+)}}/g, (match, path) => {
    const value = getNestedValue(data, path.trim());
    return value !== undefined ? formatCurrency(value) : '';
  });
  
  // {{formatDate value}}
  result = result.replace(/{{formatDate\s+([^}]+)}}/g, (match, path) => {
    const value = getNestedValue(data, path.trim());
    return value !== undefined ? formatDate(value) : '';
  });
  
  // {{multiply a b}}
  result = result.replace(/{{multiply\s+([^\s]+)\s+([^}]+)}}/g, (match, a, b) => {
    const valA = getNestedValue(data, a.trim());
    const valB = getNestedValue(data, b.trim());
    if (valA !== undefined && valB !== undefined) {
      return (parseFloat(valA) * parseFloat(valB)).toFixed(2);
    }
    return '';
  });
  
  // {{percentage amount rate}}
  result = result.replace(/{{percentage\s+([^\s]+)\s+([^}]+)}}/g, (match, amount, rate) => {
    const valAmount = getNestedValue(data, amount.trim());
    const valRate = getNestedValue(data, rate.trim());
    if (valAmount !== undefined && valRate !== undefined) {
      return ((parseFloat(valAmount) * parseFloat(valRate)) / 100).toFixed(2);
    }
    return '';
  });
  
  return result;
}

/**
 * Main template rendering function
 */
export function renderSimpleTemplate(template: string, data: any): string {
  let result = template;
  
  // Process helpers first
  result = processHelpers(result, data);
  
  // Handle each loops {{#each array}}...{{/each}}
  result = result.replace(/{{#each\s+(\w+)}}([\s\S]*?){{\/each}}/g, (match, arrayName, content) => {
    const array = data[arrayName] || [];
    return array.map((item: any, index: number) => {
      let itemContent = content;
      
      // Create context with item properties and special variables
      const context = {
        ...item,
        '@index': index,
        '@first': index === 0,
        '@last': index === array.length - 1
      };
      
      // Replace item properties
      Object.keys(context).forEach(key => {
        const regex = new RegExp(`{{\\s*${key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*}}`, 'g');
        itemContent = itemContent.replace(regex, context[key]?.toString() || '');
      });
      
      // Handle nested properties (e.g., {{this.property}})
      itemContent = itemContent.replace(/{{this\.(\w+)}}/g, (m: string, prop: string) => {
        return item[prop]?.toString() || '';
      });
      
      // Process helpers within each loop
      itemContent = processHelpers(itemContent, { ...data, ...item });
      
      return itemContent;
    }).join('');
  });
  
  // Handle conditional blocks {{#if condition}}...{{else}}...{{/if}}
  result = result.replace(/{{#if\s+(\w+)}}([\s\S]*?){{\/if}}/g, (match, condition, content) => {
    // Check for else block
    const elseMatch = content.match(/^([\s\S]*?){{else}}([\s\S]*?)$/);
    if (elseMatch) {
      const [, ifContent, elseContent] = elseMatch;
      return data[condition] ? ifContent : elseContent;
    }
    return data[condition] ? content : '';
  });
  
  // Handle unless blocks {{#unless condition}}...{{/unless}}
  result = result.replace(/{{#unless\s+(\w+)}}([\s\S]*?){{\/unless}}/g, (match, condition, content) => {
    return !data[condition] ? content : '';
  });
  
  // Handle comparison operators {{#if (eq a b)}}...{{/if}}
  result = result.replace(/{{#if\s+\(eq\s+(\w+)\s+['"]([^'"]+)['"]\)}}([\s\S]*?){{\/if}}/g, 
    (match, variable, value, content) => {
      return data[variable] === value ? content : '';
    }
  );
  
  // Handle greater than {{#if (gt a b)}}...{{/if}}
  result = result.replace(/{{#if\s+\(gt\s+(\w+)\s+(\d+)\)}}([\s\S]*?){{\/if}}/g, 
    (match, variable, value, content) => {
      return parseFloat(data[variable]) > parseFloat(value) ? content : '';
    }
  );
  
  // Handle simple {{variable}} replacements (including nested properties)
  result = result.replace(/{{\s*([^}]+)\s*}}/g, (match, path) => {
    // Skip if it's a helper or control structure
    if (path.startsWith('#') || path.startsWith('/') || path.includes('(')) {
      return match;
    }
    
    const value = getNestedValue(data, path);
    if (value !== undefined && value !== null) {
      // If it's an object or array, stringify it
      if (typeof value === 'object') {
        return JSON.stringify(value);
      }
      return value.toString();
    }
    return '';
  });
  
  // Clean up any remaining unmatched template tags
  result = result.replace(/{{[^}]*}}/g, '');
  
  return result;
}

/**
 * Compile template (for compatibility with existing code)
 * Returns a function that can be called with data
 */
export function compileSimpleTemplate(template: string): (data: any) => string {
  return (data: any) => renderSimpleTemplate(template, data);
}

/**
 * Register a custom helper (for future extensibility)
 */
const customHelpers: Map<string, (data: any, ...args: string[]) => string> = new Map();

export function registerHelper(name: string, fn: (data: any, ...args: string[]) => string): void {
  customHelpers.set(name, fn);
}

/**
 * SafeString equivalent for HTML content
 */
export class SafeString {
  constructor(public readonly value: string) {}
  toString(): string {
    return this.value;
  }
}