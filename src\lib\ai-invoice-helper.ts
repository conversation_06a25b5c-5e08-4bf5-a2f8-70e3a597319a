// AI-powered invoice optimization and smart suggestions
// Helps businesses create better invoices that get paid faster

interface IndustryIntelligence {
  industry: string
  commonServices: ServiceTemplate[]
  pricingRanges: PricingGuidance[]
  paymentTerms: PaymentOptimization[]
  professionalLanguage: LanguageTemplates
}

interface ServiceTemplate {
  name: string
  description: string
  typicalRate: {
    min: number
    max: number
    unit: 'hour' | 'project' | 'item' | 'day' | 'month'
  }
  commonAddOns: string[]
  seasonalFactors?: {
    peak: string[]
    slow: string[]
    priceModifier: number
  }
}

interface PricingGuidance {
  serviceType: string
  marketRange: { min: number, max: number }
  factors: {
    experience: number // multiplier based on years
    location: string[] // high-cost markets
    complexity: number // complexity multiplier
  }
  upsellOpportunities: string[]
}

interface PaymentOptimization {
  clientType: string
  recommendedTerms: string
  earlyPaymentDiscount: number
  latePaymentFee: number
  preferredMethods: string[]
  paymentSpeedFactor: number // how much faster they pay
}

interface LanguageTemplates {
  professionalDescriptions: { [key: string]: string[] }
  paymentInstructions: string[]
  followUpMessages: string[]
  thankYouNotes: string[]
}

// Industry-specific intelligence data
export const INDUSTRY_INTELLIGENCE: { [key: string]: IndustryIntelligence } = {
  freelancers: {
    industry: 'freelancers',
    commonServices: [
      {
        name: 'Content Writing',
        description: 'High-quality, SEO-optimized content tailored to your brand voice and target audience',
        typicalRate: { min: 0.10, max: 1.00, unit: 'item' }, // per word
        commonAddOns: ['SEO keyword research', 'Meta descriptions', 'Social media adaptations', 'Rush delivery'],
        seasonalFactors: {
          peak: ['January', 'September'], // New Year, back-to-school content
          slow: ['July', 'December'],
          priceModifier: 1.2
        }
      },
      {
        name: 'Web Development',
        description: 'Custom website development using modern frameworks and best practices',
        typicalRate: { min: 50, max: 200, unit: 'hour' },
        commonAddOns: ['Domain setup', 'Hosting configuration', 'SSL certificate', 'Analytics setup', 'SEO optimization'],
        seasonalFactors: {
          peak: ['October', 'November'], // Holiday e-commerce prep
          slow: ['June', 'July'],
          priceModifier: 1.15
        }
      },
      {
        name: 'Graphic Design',
        description: 'Professional visual design that communicates your brand message effectively',
        typicalRate: { min: 40, max: 150, unit: 'hour' },
        commonAddOns: ['Additional concepts', 'File format variations', 'Print-ready files', 'Usage rights expansion']
      },
      {
        name: 'Virtual Assistant',
        description: 'Reliable administrative support to help streamline your business operations',
        typicalRate: { min: 15, max: 50, unit: 'hour' },
        commonAddOns: ['After-hours availability', 'Specialized software training', 'Project management', 'Customer service']
      }
    ],
    pricingRanges: [
      {
        serviceType: 'Content Writing',
        marketRange: { min: 0.10, max: 1.00 },
        factors: {
          experience: 1.5, // 50% increase per 5 years
          location: ['San Francisco', 'New York', 'Seattle', 'Austin'],
          complexity: 1.3 // technical content premium
        },
        upsellOpportunities: ['SEO optimization', 'Content strategy', 'Social media distribution', 'Performance reporting']
      }
    ],
    paymentTerms: [
      {
        clientType: 'Small Business',
        recommendedTerms: 'Net 15',
        earlyPaymentDiscount: 2,
        latePaymentFee: 1.5,
        preferredMethods: ['PayPal', 'Stripe', 'Bank Transfer'],
        paymentSpeedFactor: 1.2
      },
      {
        clientType: 'Individual/Personal',
        recommendedTerms: 'Due on delivery',
        earlyPaymentDiscount: 0,
        latePaymentFee: 0,
        preferredMethods: ['PayPal', 'Venmo', 'Cash'],
        paymentSpeedFactor: 1.5
      }
    ],
    professionalLanguage: {
      professionalDescriptions: {
        'Content Writing': [
          'Research, write, and edit high-quality content optimized for your target audience and SEO requirements',
          'Develop engaging, brand-aligned content that drives traffic and converts readers into customers',
          'Create compelling copy that establishes thought leadership and builds trust with your audience'
        ],
        'Web Development': [
          'Design and develop custom website functionality using industry-standard frameworks and security practices',
          'Build responsive, user-friendly web applications optimized for performance and search engines',
          'Implement modern web solutions that enhance user experience and drive business results'
        ]
      },
      paymentInstructions: [
        'Payment is due according to the terms outlined above. Please remit payment to maintain project timeline.',
        'Thank you for your business. Payment can be made via the methods listed below for your convenience.',
        'To ensure continued service delivery, please process payment by the due date specified.'
      ],
      followUpMessages: [
        'I hope you\'re pleased with the delivered work. Please let me know if you have any questions.',
        'Thank you for the opportunity to work on this project. I look forward to our continued partnership.',
        'The project has been completed to specifications. Please review and provide feedback at your convenience.'
      ],
      thankYouNotes: [
        'Thank you for your prompt payment and the opportunity to work together.',
        'I appreciate your business and look forward to collaborating on future projects.',
        'Your partnership is valued. Thank you for choosing my services.'
      ]
    }
  },

  creative: {
    industry: 'creative',
    commonServices: [
      {
        name: 'Photography Session',
        description: 'Professional photography services capturing your special moments with artistic excellence',
        typicalRate: { min: 200, max: 800, unit: 'hour' },
        commonAddOns: ['Additional photographer', 'Extended coverage', 'Rush editing', 'Print packages', 'Album design'],
        seasonalFactors: {
          peak: ['May', 'June', 'September', 'October'], // Wedding season
          slow: ['January', 'February'],
          priceModifier: 1.3
        }
      },
      {
        name: 'Logo Design',
        description: 'Custom logo design that captures your brand essence and makes a lasting impression',
        typicalRate: { min: 500, max: 5000, unit: 'project' },
        commonAddOns: ['Brand guidelines', 'Business card design', 'Social media kit', 'Website favicon']
      },
      {
        name: 'Video Production',
        description: 'Professional video production from concept to final edit, optimized for your platform',
        typicalRate: { min: 100, max: 500, unit: 'hour' },
        commonAddOns: ['Drone footage', 'Professional lighting', 'Color grading', 'Motion graphics', 'Music licensing']
      }
    ],
    pricingRanges: [
      {
        serviceType: 'Wedding Photography',
        marketRange: { min: 1500, max: 8000 },
        factors: {
          experience: 1.8,
          location: ['San Francisco', 'New York', 'Napa Valley', 'Martha\'s Vineyard'],
          complexity: 1.4 // destination weddings
        },
        upsellOpportunities: ['Engagement session', 'Bridal portraits', 'Reception extension', 'Photo booth', 'Live streaming']
      }
    ],
    paymentTerms: [
      {
        clientType: 'Wedding Clients',
        recommendedTerms: '50% deposit, 50% before event',
        earlyPaymentDiscount: 3,
        latePaymentFee: 2,
        preferredMethods: ['Bank Transfer', 'Check', 'PayPal'],
        paymentSpeedFactor: 0.8 // Usually slower due to planning timeline
      }
    ],
    professionalLanguage: {
      professionalDescriptions: {
        'Wedding Photography': [
          'Comprehensive wedding photography coverage capturing every precious moment of your special day',
          'Professional wedding documentation with artistic storytelling and timeless elegance',
          'Full-service wedding photography including preparation, ceremony, reception, and portrait sessions'
        ]
      },
      paymentInstructions: [
        'Payment secures your date and confirms our photography services for your special event.',
        'Final payment is due before event commencement to ensure seamless service delivery.'
      ],
      followUpMessages: [
        'Thank you for choosing us to capture your special day. We\'re honored to be part of your celebration.',
        'Your wedding photos are being carefully edited and will be ready for preview within 2-3 weeks.'
      ],
      thankYouNotes: [
        'Congratulations again! Thank you for allowing us to document your beautiful wedding day.',
        'It was an absolute pleasure capturing your love story. Wishing you a lifetime of happiness.'
      ]
    }
  },

  trades: {
    industry: 'trades',
    commonServices: [
      {
        name: 'Home Repair Services',
        description: 'Professional home repair and maintenance services with quality workmanship guaranteed',
        typicalRate: { min: 40, max: 120, unit: 'hour' },
        commonAddOns: ['Materials markup', 'Emergency service fee', 'Travel time', 'Permit acquisition', 'Cleanup service']
      },
      {
        name: 'Cleaning Services',
        description: 'Thorough professional cleaning services using eco-friendly products and proven techniques',
        typicalRate: { min: 25, max: 65, unit: 'hour' },
        commonAddOns: ['Deep cleaning surcharge', 'Organizing services', 'Window cleaning', 'Carpet cleaning', 'Move-out cleaning']
      }
    ],
    pricingRanges: [
      {
        serviceType: 'Handyman Services',
        marketRange: { min: 40, max: 120 },
        factors: {
          experience: 1.4,
          location: ['San Francisco', 'New York', 'Washington DC'],
          complexity: 1.6 // electrical, plumbing premium
        },
        upsellOpportunities: ['Preventive maintenance', 'Material upgrades', 'Additional rooms', 'Warranty extension']
      }
    ],
    paymentTerms: [
      {
        clientType: 'Homeowners',
        recommendedTerms: 'Due on completion',
        earlyPaymentDiscount: 0,
        latePaymentFee: 0,
        preferredMethods: ['Cash', 'Check', 'Credit Card'],
        paymentSpeedFactor: 1.8 // Usually pay immediately
      }
    ],
    professionalLanguage: {
      professionalDescriptions: {
        'Home Repair': [
          'Professional home repair services performed to code with quality materials and expert craftsmanship',
          'Reliable maintenance and repair solutions to keep your home safe, functional, and valuable',
          'Expert home improvement services backed by experience and commitment to customer satisfaction'
        ]
      },
      paymentInstructions: [
        'Payment is due upon completion of work. We accept cash, check, or credit card for your convenience.',
        'Thank you for choosing our services. Payment ensures warranty coverage and future service availability.'
      ],
      followUpMessages: [
        'Thank you for the opportunity to work on your home. Please let us know if you have any questions.',
        'We appreciate your business and stand behind our work. Contact us if any issues arise.'
      ],
      thankYouNotes: [
        'Thank you for your business and trust in our craftsmanship.',
        'We appreciate the opportunity to improve your home and look forward to serving you again.'
      ]
    }
  }
}

/**
 * Generate smart service descriptions based on industry and service type
 */
export function generateSmartDescription(industry: string, serviceType: string, customDetails?: string): string {
  const intelligence = INDUSTRY_INTELLIGENCE[industry]
  if (!intelligence) return customDetails || 'Professional services provided'

  const service = intelligence.commonServices.find(s => 
    s.name.toLowerCase().includes(serviceType.toLowerCase()) ||
    serviceType.toLowerCase().includes(s.name.toLowerCase())
  )

  if (!service) {
    // Use generic professional language for the industry
    const templates = intelligence.professionalLanguage.professionalDescriptions
    const firstTemplate = Object.values(templates)[0]
    return firstTemplate?.[0] || customDetails || 'Professional services provided'
  }

  return service.description + (customDetails ? ` - ${customDetails}` : '')
}

/**
 * Suggest optimal pricing based on industry standards and location
 */
export function suggestPricing(industry: string, serviceType: string, experienceYears: number = 2, location?: string): {
  suggestedRate: number
  unit: string
  reasoning: string
  upsells: string[]
} {
  const intelligence = INDUSTRY_INTELLIGENCE[industry]
  if (!intelligence) {
    return {
      suggestedRate: 50,
      unit: 'hour',
      reasoning: 'Base rate for professional services',
      upsells: []
    }
  }

  const service = intelligence.commonServices.find(s => 
    s.name.toLowerCase().includes(serviceType.toLowerCase())
  )

  if (!service) {
    return {
      suggestedRate: 75,
      unit: 'hour',
      reasoning: 'Standard professional rate',
      upsells: []
    }
  }

  // Calculate base rate from range (favor middle-upper range for professionals)
  const baseRate = service.typicalRate.min + (service.typicalRate.max - service.typicalRate.min) * 0.6

  // Apply experience multiplier
  const experienceMultiplier = 1 + (experienceYears / 5) * 0.3 // 30% increase per 5 years

  // Apply location multiplier if in high-cost market
  const pricingGuidance = intelligence.pricingRanges.find(p => p.serviceType === service.name)
  const locationMultiplier = (location && pricingGuidance?.factors.location.includes(location)) ? 1.2 : 1

  const suggestedRate = Math.round(baseRate * experienceMultiplier * locationMultiplier)

  return {
    suggestedRate,
    unit: service.typicalRate.unit,
    reasoning: `Based on ${industry} industry standards, ${experienceYears} years experience${location ? `, ${location} market` : ''}`,
    upsells: service.commonAddOns || []
  }
}

/**
 * Recommend optimal payment terms for client type and industry
 */
export function recommendPaymentTerms(industry: string, clientType: string, invoiceAmount: number): {
  terms: string
  reasoning: string
  earlyPaymentDiscount?: number
  methods: string[]
} {
  const intelligence = INDUSTRY_INTELLIGENCE[industry]
  if (!intelligence) {
    return {
      terms: 'Net 30',
      reasoning: 'Standard business terms',
      methods: ['Bank Transfer', 'Check']
    }
  }

  const paymentOption = intelligence.paymentTerms.find(p => 
    p.clientType.toLowerCase().includes(clientType.toLowerCase()) ||
    clientType.toLowerCase().includes(p.clientType.toLowerCase())
  ) || intelligence.paymentTerms[0]

  // Adjust terms based on invoice amount
  let adjustedTerms = paymentOption.recommendedTerms
  if (invoiceAmount > 5000 && !adjustedTerms.includes('deposit')) {
    adjustedTerms = '50% deposit, balance on completion'
  }

  return {
    terms: adjustedTerms,
    reasoning: `Optimized for ${clientType} in ${industry} industry`,
    earlyPaymentDiscount: paymentOption.earlyPaymentDiscount > 0 ? paymentOption.earlyPaymentDiscount : undefined,
    methods: paymentOption.preferredMethods
  }
}

/**
 * Generate professional follow-up message templates
 */
export function generateFollowUpMessage(industry: string, messageType: 'payment_reminder' | 'thank_you' | 'project_complete'): string {
  const intelligence = INDUSTRY_INTELLIGENCE[industry]
  if (!intelligence) {
    return 'Thank you for your business. Please let me know if you have any questions.'
  }

  const templates = intelligence.professionalLanguage
  
  switch (messageType) {
    case 'payment_reminder':
      return templates.paymentInstructions[0] || 'Payment is due according to the terms outlined. Thank you for your prompt attention.'
    
    case 'thank_you':
      return templates.thankYouNotes[0] || 'Thank you for your business and the opportunity to work together.'
    
    case 'project_complete':
      return templates.followUpMessages[0] || 'The project has been completed. Please review and let me know if you have any questions.'
    
    default:
      return templates.followUpMessages[0] || 'Thank you for choosing my services.'
  }
}

/**
 * Analyze invoice for optimization opportunities
 */
export function analyzeInvoiceOptimization(invoiceData: {
  industry: string
  serviceDescription: string
  amount: number
  paymentTerms: string
  clientType?: string
}): {
  optimizationScore: number
  suggestions: string[]
  improvements: { field: string, suggestion: string, impact: 'high' | 'medium' | 'low' }[]
} {
  const suggestions: string[] = []
  const improvements: { field: string, suggestion: string, impact: 'high' | 'medium' | 'low' }[] = []
  let score = 70 // Base score

  // Check service description quality
  if (invoiceData.serviceDescription.length < 50) {
    suggestions.push('Add more detailed service description to justify pricing')
    improvements.push({
      field: 'description',
      suggestion: 'Expand description with specific deliverables and value provided',
      impact: 'high'
    })
    score -= 15
  }

  // Check payment terms optimization
  if (invoiceData.paymentTerms === 'Net 30' && invoiceData.amount < 1000) {
    suggestions.push('Consider shorter payment terms for smaller amounts')
    improvements.push({
      field: 'payment_terms',
      suggestion: 'Use "Net 15" or "Due on delivery" for amounts under $1,000',
      impact: 'medium'
    })
    score -= 10
  }

  // Check for upsell opportunities
  const pricing = suggestPricing(invoiceData.industry, invoiceData.serviceDescription)
  if (pricing.upsells.length > 0) {
    suggestions.push('Consider adding complementary services to increase value')
    improvements.push({
      field: 'services',
      suggestion: `Add services like: ${pricing.upsells.slice(0, 2).join(', ')}`,
      impact: 'medium'
    })
  }

  // Check for professional language
  if (!invoiceData.serviceDescription.includes('professional') && !invoiceData.serviceDescription.includes('quality')) {
    improvements.push({
      field: 'language',
      suggestion: 'Use more professional language to enhance perceived value',
      impact: 'low'
    })
    score -= 5
  }

  return {
    optimizationScore: Math.max(score, 0),
    suggestions,
    improvements
  }
}

/**
 * Get seasonal pricing recommendations
 */
export function getSeasonalPricingAdvice(industry: string, serviceType: string, currentMonth: string): {
  isSeasonalService: boolean
  currentSeasonality: 'peak' | 'normal' | 'slow'
  pricingAdjustment: number
  recommendation: string
} {
  const intelligence = INDUSTRY_INTELLIGENCE[industry]
  if (!intelligence) {
    return {
      isSeasonalService: false,
      currentSeasonality: 'normal',
      pricingAdjustment: 1,
      recommendation: 'No seasonal adjustments recommended'
    }
  }

  const service = intelligence.commonServices.find(s => 
    s.name.toLowerCase().includes(serviceType.toLowerCase())
  )

  if (!service?.seasonalFactors) {
    return {
      isSeasonalService: false,
      currentSeasonality: 'normal',
      pricingAdjustment: 1,
      recommendation: 'Service not affected by seasonal demand'
    }
  }

  const { peak, slow, priceModifier } = service.seasonalFactors
  
  let seasonality: 'peak' | 'normal' | 'slow' = 'normal'
  let adjustment = 1
  let recommendation = ''

  if (peak.includes(currentMonth)) {
    seasonality = 'peak'
    adjustment = priceModifier
    recommendation = `Peak season - consider ${Math.round((priceModifier - 1) * 100)}% price increase`
  } else if (slow.includes(currentMonth)) {
    seasonality = 'slow'
    adjustment = 0.9
    recommendation = 'Slow season - consider offering promotions or focus on other services'
  } else {
    recommendation = 'Normal season - standard pricing recommended'
  }

  return {
    isSeasonalService: true,
    currentSeasonality: seasonality,
    pricingAdjustment: adjustment,
    recommendation
  }
}