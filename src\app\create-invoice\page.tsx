'use client'

export const dynamic = 'force-dynamic'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowRight,
  ArrowLeft,
  Sparkles,
  FileText,
  Download,
  Send,
  Copy,
  CheckCircle,
  Save,
  Eye,
  Loader2
} from 'lucide-react'
import { Button } from '../../components/ui/Button'
import { InvoiceForm } from '../../components/invoice/InvoiceForm'
import { Card } from '../../components/ui/Card'

// Import template functions
import { getTemplateById, renderTemplate, prepareInvoiceData } from '../../lib/template-renderer'
import { generateInvoiceNumber } from '../../lib/utils'

interface InvoiceData {
  businessName: string
  businessAddress: string
  businessPhone: string
  businessEmail: string
  businessLogo: string
  clientName: string
  clientAddress: string
  clientPhone: string
  clientEmail: string
  invoiceNumber: string
  invoiceDate: string
  dueDate: string
  lineItems: Array<{
    description: string
    quantity: number
    rate: number
    amount: number
  }>
  taxRate: number
  notes: string
  paymentTerms: string
  subtotal?: number
  tax?: number
  total?: number
}

function CreateInvoiceContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const selectedTemplateId = searchParams.get('template') || 'professional'
  
  // Load selected template
  const [template, setTemplate] = useState<any>(null)
  const [invoiceData, setInvoiceData] = useState<InvoiceData>({
    businessName: '',
    businessAddress: '',
    businessPhone: '',
    businessEmail: '',
    businessLogo: '',
    clientName: '',
    clientAddress: '',
    clientPhone: '',
    clientEmail: '',
    invoiceNumber: generateInvoiceNumber(),
    invoiceDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    lineItems: [{ description: '', quantity: 1, rate: 0, amount: 0 }],
    taxRate: 0,
    notes: '',
    paymentTerms: 'Net 30 days'
  })
  
  const [previewHtml, setPreviewHtml] = useState('')
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [isSendingEmail, setIsSendingEmail] = useState(false)
  const [isSavingDraft, setIsSavingDraft] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  
  // Load template on mount
  useEffect(() => {
    const templateData = getTemplateById(selectedTemplateId)
    setTemplate(templateData)
  }, [selectedTemplateId])
  
  // Update preview in real-time
  useEffect(() => {
    if (!template) return
    
    try {
      // Map form fields to template fields
      const mappedData = {
        companyName: invoiceData.businessName,
        companyAddress: invoiceData.businessAddress,
        companyPhone: invoiceData.businessPhone,
        companyEmail: invoiceData.businessEmail,
        companyLogo: invoiceData.businessLogo,
        clientName: invoiceData.clientName,
        clientAddress: invoiceData.clientAddress,
        clientPhone: invoiceData.clientPhone,
        clientEmail: invoiceData.clientEmail,
        invoiceNumber: invoiceData.invoiceNumber,
        invoiceDate: invoiceData.invoiceDate,
        dueDate: invoiceData.dueDate,
        lineItems: invoiceData.lineItems,
        taxRate: invoiceData.taxRate,
        notes: invoiceData.notes,
        paymentTerms: invoiceData.paymentTerms
      }
      
      const data = prepareInvoiceData(mappedData, selectedTemplateId)
      const html = renderTemplate(selectedTemplateId, data)
      setPreviewHtml(html)
    } catch (error) {
      console.error('Preview generation failed:', error)
    }
  }, [selectedTemplateId, invoiceData, template])
  
  // Handle PDF download
  const handleDownloadPDF = async () => {
    setIsGeneratingPDF(true)
    try {
      const response = await fetch('/api/invoices/pdf', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ templateId: selectedTemplateId, invoiceData })
      })
      
      if (!response.ok) throw new Error('PDF generation failed')
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `invoice-${invoiceData.invoiceNumber}.pdf`
      link.click()
      
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('PDF download failed:', error)
      alert('Failed to generate PDF. Please try again.')
    } finally {
      setIsGeneratingPDF(false)
    }
  }
  
  // Handle email sending
  const handleSendEmail = async () => {
    setIsSendingEmail(true)
    try {
      const response = await fetch('/api/invoices/email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          templateId: selectedTemplateId,
          invoiceData,
          recipientEmail: invoiceData.clientEmail
        })
      })
      
      if (!response.ok) throw new Error('Email sending failed')
      
      alert('Invoice sent successfully!')
      setCurrentStep(3)
    } catch (error) {
      console.error('Email sending failed:', error)
      alert('Failed to send email. Please try again.')
    } finally {
      setIsSendingEmail(false)
    }
  }
  
  // Handle saving draft
  const handleSaveDraft = async () => {
    setIsSavingDraft(true)
    try {
      // Save to localStorage for now
      localStorage.setItem('invoice-draft', JSON.stringify({
        templateId: selectedTemplateId,
        invoiceData,
        savedAt: new Date().toISOString()
      }))
      
      alert('Draft saved successfully!')
    } catch (error) {
      console.error('Save failed:', error)
      alert('Failed to save draft. Please try again.')
    } finally {
      setIsSavingDraft(false)
    }
  }
  
  // Handle template change
  const handleChangeTemplate = () => {
    router.push('/templates')
  }
  
  // Load draft on mount
  useEffect(() => {
    const savedDraft = localStorage.getItem('invoice-draft')
    if (savedDraft) {
      try {
        const parsed = JSON.parse(savedDraft)
        if (parsed.invoiceData) {
          setInvoiceData(parsed.invoiceData)
        }
      } catch (error) {
        console.error('Error loading draft:', error)
      }
    }
  }, [])
  
  const steps = [
    { number: 1, title: 'Invoice Details', icon: FileText },
    { number: 2, title: 'Preview & Actions', icon: Eye },
    { number: 3, title: 'Success', icon: CheckCircle }
  ]
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Create Invoice</h1>
          <p className="text-xl text-gray-600">
            Using {template?.name || 'Professional'} Template
          </p>
        </div>
        
        {/* Progress Indicator */}
        <div className="flex items-center justify-center mb-12">
          {steps.map((step, index) => {
            const Icon = step.icon
            const isActive = currentStep === step.number
            const isCompleted = currentStep > step.number
            
            return (
              <div key={step.number} className="flex items-center">
                <div className={`flex items-center gap-3 px-4 py-2 rounded-full transition-all ${
                  isActive ? 'bg-blue-100 text-blue-700' :
                  isCompleted ? 'bg-green-100 text-green-700' :
                  'bg-gray-100 text-gray-500'
                }`}>
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{step.title}</span>
                </div>
                {index < steps.length - 1 && (
                  <ArrowRight className="w-5 h-5 text-gray-400 mx-4" />
                )}
              </div>
            )
          })}
        </div>
        
        <AnimatePresence mode="wait">
          {/* Step 1: Invoice Form */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="grid lg:grid-cols-2 gap-8"
            >
              {/* Invoice Form */}
              <div className="space-y-6">
                <InvoiceForm 
                  data={invoiceData}
                  onChange={setInvoiceData}
                  template={template}
                />
                
                <div className="flex gap-4">
                  <Button onClick={handleSaveDraft} variant="outline" disabled={isSavingDraft}>
                    {isSavingDraft ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Draft
                      </>
                    )}
                  </Button>
                  <Button onClick={handleChangeTemplate} variant="outline">
                    Change Template
                  </Button>
                  <Button 
                    onClick={() => setCurrentStep(2)}
                    className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    disabled={!invoiceData.businessName || !invoiceData.clientName || !invoiceData.clientEmail}
                  >
                    Preview Invoice
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
              
              {/* Live Preview */}
              <div className="lg:sticky lg:top-8">
                <Card className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Eye className="w-5 h-5 mr-2" />
                    Live Preview
                  </h3>
                  <div className="bg-white border rounded-lg overflow-hidden">
                    <iframe
                      srcDoc={previewHtml}
                      className="w-full h-[600px]"
                      title="Invoice Preview"
                    />
                  </div>
                </Card>
              </div>
            </motion.div>
          )}
          
          {/* Step 2: Preview & Actions */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="max-w-4xl mx-auto"
            >
              <Card className="p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Review & Send Invoice</h2>
                
                {/* Full Preview */}
                <div className="bg-white border rounded-lg overflow-hidden mb-8">
                  <iframe
                    srcDoc={previewHtml}
                    className="w-full h-[700px]"
                    title="Invoice Preview"
                  />
                </div>
                
                {/* Action Buttons */}
                <div className="grid md:grid-cols-3 gap-4">
                  <Button 
                    onClick={handleDownloadPDF} 
                    disabled={isGeneratingPDF}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    {isGeneratingPDF ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4 mr-2" />
                        Download PDF
                      </>
                    )}
                  </Button>
                  
                  <Button 
                    onClick={handleSendEmail} 
                    disabled={isSendingEmail}
                    className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                  >
                    {isSendingEmail ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Send Email
                      </>
                    )}
                  </Button>
                  
                  <Button onClick={() => setCurrentStep(1)} variant="outline">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Edit Invoice
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}
          
          {/* Step 3: Success */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="max-w-2xl mx-auto"
            >
              <Card className="p-8 text-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                  className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
                >
                  <CheckCircle className="w-12 h-12 text-green-600" />
                </motion.div>
                
                <h2 className="text-3xl font-bold text-gray-900 mb-2">
                  Invoice Sent Successfully!
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  Your invoice has been sent to {invoiceData.clientEmail}
                </p>
                
                <div className="flex gap-4">
                  <Button
                    onClick={() => {
                      setInvoiceData({
                        businessName: '',
                        businessAddress: '',
                        businessPhone: '',
                        businessEmail: '',
                        businessLogo: '',
                        clientName: '',
                        clientAddress: '',
                        clientPhone: '',
                        clientEmail: '',
                        invoiceNumber: generateInvoiceNumber(),
                        invoiceDate: new Date().toISOString().split('T')[0],
                        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        lineItems: [{ description: '', quantity: 1, rate: 0, amount: 0 }],
                        taxRate: 0,
                        notes: '',
                        paymentTerms: 'Net 30 days'
                      })
                      setCurrentStep(1)
                    }}
                    className="flex-1"
                  >
                    Create New Invoice
                  </Button>
                  <Button
                    onClick={() => router.push('/my-invoices')}
                    variant="outline"
                    className="flex-1"
                  >
                    View All Invoices
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default function CreateInvoicePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <h2 className="text-xl text-gray-600 mt-4">Loading invoice creator...</h2>
        </div>
      </div>
    }>
      <CreateInvoiceContent />
    </Suspense>
  )
}
