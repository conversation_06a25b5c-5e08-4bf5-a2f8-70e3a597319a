import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // For MVP, we'll allow any authenticated user to view monitoring
    // In production, restrict to admin users only
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view') || 'dashboard';
    const period = searchParams.get('period') || 'today';

    // Calculate date range based on period
    let dateRange;
    const now = new Date();
    
    switch (period) {
      case 'today':
        dateRange = {
          start: new Date(now.setHours(0, 0, 0, 0)),
          end: new Date()
        };
        break;
      case 'week':
        dateRange = {
          start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
          end: new Date()
        };
        break;
      case 'month':
        dateRange = {
          start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
          end: new Date()
        };
        break;
      default:
        dateRange = undefined;
    }

    // Dynamic imports to avoid bundling MongoDB for client
    const { 
      getCriticalMetrics, 
      getSystemHealth, 
      getRealTimeDashboard,
      getEndpointMetrics 
    } = await import('@/lib/analytics-enhanced');

    // Return different data based on view
    switch (view) {
      case 'health':
        const health = await getSystemHealth();
        return NextResponse.json(health);

      case 'metrics':
        const metrics = await getCriticalMetrics(dateRange);
        return NextResponse.json(metrics);

      case 'endpoints':
        const endpoints = await getEndpointMetrics(dateRange);
        return NextResponse.json(endpoints);

      case 'dashboard':
      default:
        const dashboard = await getRealTimeDashboard();
        return NextResponse.json(dashboard);
    }

  } catch (error) {
    console.error('Error fetching monitoring data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch monitoring data' },
      { status: 500 }
    );
  }
}

// Health check endpoint (no auth required)
export async function HEAD(request: NextRequest) {
  try {
    const { getSystemHealth } = await import('@/lib/analytics-enhanced');
    const health = await getSystemHealth();
    
    if (health.status === 'healthy') {
      return new NextResponse(null, { status: 200 });
    } else if (health.status === 'degraded') {
      return new NextResponse(null, { status: 503 });
    } else {
      return new NextResponse(null, { status: 503 });
    }
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}