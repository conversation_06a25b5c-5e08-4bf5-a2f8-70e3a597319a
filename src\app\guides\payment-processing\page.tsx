import type { Metadata } from 'next'
import Link from 'next/link'
import { ArrowRight, CreditCard, Shield, Zap, DollarSign, Clock, CheckCircle, AlertCircle, Settings } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Setting Up Payment Processing for Invoices - Stripe Integration Guide',
  description: 'Configure Stripe and other payment methods for faster invoice payments. Complete setup guide with screenshots.',
  keywords: ['invoice payment processing', 'Stripe invoice', 'payment setup', 'online payments', 'invoice payments', 'payment integration'],
  openGraph: {
    title: 'Setting Up Payment Processing for Invoices - Stripe Integration Guide',
    description: 'Configure Stripe and other payment methods for faster invoice payments. Complete setup guide with screenshots.',
    type: 'article',
  },
}

export default function PaymentProcessingGuide() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-teal-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Payment Processing Setup
            </h1>
            <p className="text-xl text-green-100 max-w-3xl mx-auto">
              Configure Stripe and other payment methods for faster invoice payments
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="max-w-4xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
        <nav className="flex items-center space-x-2 text-sm text-gray-600">
          <Link href="/" className="hover:text-gray-900">Home</Link>
          <span>/</span>
          <Link href="/guides" className="hover:text-gray-900">Guides</Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">Payment Processing</span>
        </nav>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Table of Contents */}
        <div className="bg-green-50 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Table of Contents</h2>
          <ol className="space-y-2 text-green-700">
            <li><a href="#introduction" className="hover:text-green-900">1. Introduction to Payment Processing</a></li>
            <li><a href="#stripe-setup" className="hover:text-green-900">2. Setting Up Stripe Integration</a></li>
            <li><a href="#payment-methods" className="hover:text-green-900">3. Supported Payment Methods</a></li>
            <li><a href="#payment-links" className="hover:text-green-900">4. Creating Payment Links</a></li>
            <li><a href="#tracking-payments" className="hover:text-green-900">5. Tracking and Managing Payments</a></li>
            <li><a href="#security" className="hover:text-green-900">6. Security and Compliance</a></li>
            <li><a href="#fees-pricing" className="hover:text-green-900">7. Understanding Fees and Pricing</a></li>
            <li><a href="#troubleshooting" className="hover:text-green-900">8. Troubleshooting Common Issues</a></li>
          </ol>
        </div>

        {/* Introduction */}
        <section id="introduction" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Introduction to Payment Processing</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Streamlined payment processing is crucial for maintaining healthy cash flow and improving 
            client relationships. Template Invoice integrates with leading payment processors to make 
            it easy for your clients to pay invoices quickly and securely.
          </p>
          
          <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-6 mb-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center">
              <Zap className="w-6 h-6 mr-2 text-green-600" />
              Benefits of Integrated Payment Processing
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Get paid 3x faster than traditional methods</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Reduce payment processing overhead</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Automatic payment tracking and reconciliation</span>
                </li>
              </ul>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Professional payment experience for clients</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Secure, PCI-compliant transactions</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Multiple payment options for client convenience</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="font-semibold text-blue-800 mb-2">Before You Begin</h3>
            <p className="text-blue-700">
              You'll need a business bank account and relevant business documentation to set up payment 
              processing. Have your business tax ID, bank account details, and business verification 
              documents ready.
            </p>
          </div>
        </section>

        {/* Stripe Setup */}
        <section id="stripe-setup" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Setting Up Stripe Integration</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Stripe is our recommended payment processor, offering competitive rates, excellent security, 
            and seamless integration with Template Invoice. Follow these steps to get started:
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start">
                <div className="bg-green-100 rounded-full p-2 mr-4">
                  <CreditCard className="w-6 h-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold mb-2">Step 1: Create a Stripe Account</h3>
                  <p className="text-gray-700 mb-4">
                    Visit stripe.com and create a business account. You'll need to provide:
                  </p>
                  <ul className="list-disc list-inside space-y-2 text-gray-700 mb-4">
                    <li>Business name and description</li>
                    <li>Business address and contact information</li>
                    <li>Tax identification number (EIN or SSN)</li>
                    <li>Bank account details for payouts</li>
                    <li>Identity verification documents</li>
                  </ul>
                  <div className="bg-yellow-50 rounded p-4 text-sm">
                    <strong>Processing Time:</strong> Account verification typically takes 1-2 business days, 
                    but you can start testing immediately with Stripe's test mode.
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start">
                <div className="bg-green-100 rounded-full p-2 mr-4">
                  <Settings className="w-6 h-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold mb-2">Step 2: Connect Stripe to Template Invoice</h3>
                  <ol className="space-y-3">
                    <li className="flex items-start">
                      <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                      <div>
                        <strong>Navigate to Payment Settings:</strong> Go to Settings → Payment Processing in your Template Invoice dashboard
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                      <div>
                        <strong>Click "Connect Stripe":</strong> This will redirect you to Stripe's secure connection page
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                      <div>
                        <strong>Authorize the Connection:</strong> Log in to your Stripe account and approve the integration
                      </div>
                    </li>
                    <li className="flex items-start">
                      <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">4</span>
                      <div>
                        <strong>Verify Connection:</strong> You'll be redirected back to Template Invoice with confirmation
                      </div>
                    </li>
                  </ol>
                </div>
              </div>
            </div>

            <div className="bg-green-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <Shield className="w-6 h-6 mr-2 text-green-600" />
                Security Notice
              </h3>
              <p className="text-green-700">
                Template Invoice never stores your Stripe credentials or payment information. All sensitive 
                data is handled directly by Stripe's secure, PCI-compliant infrastructure. We only receive 
                payment confirmations and transaction IDs.
              </p>
            </div>
          </div>
        </section>

        {/* Payment Methods */}
        <section id="payment-methods" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Supported Payment Methods</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Template Invoice supports a wide range of payment methods through our integrations, 
            allowing your clients to pay using their preferred method:
          </p>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <CreditCard className="w-6 h-6 mr-2 text-blue-600" />
                Credit & Debit Cards
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Visa</span>
                  <span className="text-sm text-green-600">✓ Supported</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Mastercard</span>
                  <span className="text-sm text-green-600">✓ Supported</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">American Express</span>
                  <span className="text-sm text-green-600">✓ Supported</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Discover</span>
                  <span className="text-sm text-green-600">✓ Supported</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Diners Club</span>
                  <span className="text-sm text-green-600">✓ Supported</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <DollarSign className="w-6 h-6 mr-2 text-green-600" />
                Digital Wallets & Bank Transfers
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Apple Pay</span>
                  <span className="text-sm text-green-600">✓ Supported</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Google Pay</span>
                  <span className="text-sm text-green-600">✓ Supported</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">PayPal</span>
                  <span className="text-sm text-blue-600">Coming Soon</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">ACH Bank Transfer</span>
                  <span className="text-sm text-green-600">✓ Supported</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Wire Transfer</span>
                  <span className="text-sm text-green-600">✓ Supported</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-6 mt-6">
            <h3 className="text-xl font-semibold mb-3">International Payment Support</h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Supported Currencies</h4>
                <p className="text-gray-700 text-sm mb-2">Process payments in 135+ currencies including:</p>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• USD, EUR, GBP, CAD, AUD</li>
                  <li>• JPY, CHF, SEK, NOK, DKK</li>
                  <li>• And many more...</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Global Coverage</h4>
                <p className="text-gray-700 text-sm mb-2">Accept payments from customers in:</p>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• 40+ countries directly</li>
                  <li>• 195+ countries via international cards</li>
                  <li>• Automatic currency conversion</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Payment Links */}
        <section id="payment-links" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Creating Payment Links</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Payment links make it incredibly easy for clients to pay invoices. They can be embedded 
            directly in invoices or sent separately for maximum convenience.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Automatic Payment Link Generation</h3>
              <p className="text-gray-700 mb-4">
                When you send an invoice with payment processing enabled, Template Invoice automatically 
                generates secure payment links for each invoice.
              </p>
              
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h4 className="font-semibold mb-2">Example Payment Link Features:</h4>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li>• Unique, secure URL for each invoice</li>
                  <li>• Mobile-optimized payment page</li>
                  <li>• Automatic invoice details pre-filled</li>
                  <li>• Real-time payment confirmation</li>
                  <li>• Automatic receipt generation</li>
                </ul>
              </div>

              <div className="border border-gray-200 rounded p-4">
                <div className="text-sm text-gray-600 mb-2">Sample Payment Link:</div>
                <div className="font-mono text-sm bg-blue-50 p-2 rounded border">
                  https://pay.templateinvoice.com/inv_abc123xyz
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Payment Page Customization</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Branding Options</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Your company logo</li>
                    <li>• Brand colors and styling</li>
                    <li>• Custom thank you messages</li>
                    <li>• Return URL after payment</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Security Features</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• SSL encryption for all data</li>
                    <li>• PCI DSS compliant processing</li>
                    <li>• Fraud detection and prevention</li>
                    <li>• 3D Secure authentication support</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Tracking Payments */}
        <section id="tracking-payments" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Tracking and Managing Payments</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Keep track of all your payments with real-time updates and comprehensive reporting tools 
            built into Template Invoice.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Clock className="w-6 h-6 mr-2 text-blue-600" />
                Real-time Payment Tracking
              </h3>
              
              <div className="grid md:grid-cols-3 gap-4">
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-3">
                    <Clock className="w-6 h-6 text-yellow-600" />
                  </div>
                  <h4 className="font-semibold text-sm mb-1">Pending</h4>
                  <p className="text-xs text-gray-600">Invoice sent, awaiting payment</p>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                    <CreditCard className="w-6 h-6 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-sm mb-1">Processing</h4>
                  <p className="text-xs text-gray-600">Payment initiated, being processed</p>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  </div>
                  <h4 className="font-semibold text-sm mb-1">Paid</h4>
                  <p className="text-xs text-gray-600">Payment completed successfully</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Payment Analytics Dashboard</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Key Metrics</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Total revenue processed</li>
                    <li>• Average payment time</li>
                    <li>• Payment method breakdown</li>
                    <li>• Monthly/quarterly trends</li>
                    <li>• Outstanding invoice amounts</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Automated Notifications</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Payment received confirmations</li>
                    <li>• Failed payment alerts</li>
                    <li>• Weekly payment summaries</li>
                    <li>• Unusual activity warnings</li>
                    <li>• Payout notifications</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Security */}
        <section id="security" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Security and Compliance</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Template Invoice and our payment partners maintain the highest security standards to 
            protect your business and your clients' sensitive information.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Shield className="w-6 h-6 mr-2 text-green-600" />
                Security Certifications & Compliance
              </h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Payment Security</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      PCI DSS Level 1 Certified
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      256-bit SSL Encryption
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      Tokenized Card Storage
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      3D Secure Authentication
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Data Protection</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      GDPR Compliant
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      SOC 2 Type II Certified
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      Regular Security Audits
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                      Encrypted Data Transmission
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-red-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3 flex items-center">
                <AlertCircle className="w-6 h-6 mr-2 text-red-600" />
                Fraud Protection
              </h3>
              <div className="space-y-3 text-red-700">
                <p>Our advanced fraud detection systems protect against:</p>
                <ul className="grid md:grid-cols-2 gap-2 text-sm">
                  <li>• Suspicious payment patterns</li>
                  <li>• Stolen credit card usage</li>
                  <li>• Account takeover attempts</li>
                  <li>• Velocity checks and limits</li>
                  <li>• Geographic risk assessment</li>
                  <li>• Machine learning-based detection</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Fees and Pricing */}
        <section id="fees-pricing" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Understanding Fees and Pricing</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Transparent pricing is important for managing your business finances. Here's a breakdown 
            of payment processing fees and how they work:
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Payment Processing Fees</h3>
              
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4">Payment Method</th>
                      <th className="text-left py-3 px-4">Processing Fee</th>
                      <th className="text-left py-3 px-4">Settlement Time</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    <tr>
                      <td className="py-3 px-4">Credit/Debit Cards</td>
                      <td className="py-3 px-4">2.9% + $0.30</td>
                      <td className="py-3 px-4">2 business days</td>
                    </tr>
                    <tr>
                      <td className="py-3 px-4">ACH Bank Transfer</td>
                      <td className="py-3 px-4">0.8% (max $5.00)</td>
                      <td className="py-3 px-4">5-7 business days</td>
                    </tr>
                    <tr>
                      <td className="py-3 px-4">International Cards</td>
                      <td className="py-3 px-4">3.4% + $0.30</td>
                      <td className="py-3 px-4">2 business days</td>
                    </tr>
                    <tr>
                      <td className="py-3 px-4">Digital Wallets</td>
                      <td className="py-3 px-4">2.9% + $0.30</td>
                      <td className="py-3 px-4">2 business days</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-green-50 rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-3 text-green-800">No Hidden Fees</h3>
                <ul className="space-y-2 text-green-700 text-sm">
                  <li>• No monthly fees</li>
                  <li>• No setup costs</li>
                  <li>• No cancellation fees</li>
                  <li>• No minimum volume requirements</li>
                  <li>• No account maintenance fees</li>
                </ul>
              </div>
              
              <div className="bg-blue-50 rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-3 text-blue-800">Volume Discounts</h3>
                <p className="text-blue-700 text-sm mb-3">
                  Process more than $10,000 monthly? Contact us for custom pricing:
                </p>
                <ul className="space-y-2 text-blue-700 text-sm">
                  <li>• Reduced processing rates</li>
                  <li>• Priority support</li>
                  <li>• Custom integration options</li>
                  <li>• Dedicated account manager</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Troubleshooting */}
        <section id="troubleshooting" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Troubleshooting Common Issues</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Here are solutions to the most common payment processing issues and how to resolve them:
          </p>

          <div className="space-y-4">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-red-700 mb-2">Payment Failed or Declined</h3>
              <div className="text-gray-700 text-sm space-y-2">
                <p><strong>Common Causes:</strong></p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Insufficient funds in customer's account</li>
                  <li>Incorrect card information entered</li>
                  <li>Bank security restrictions</li>
                  <li>Expired payment method</li>
                </ul>
                <p><strong>Solutions:</strong></p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Ask customer to verify payment details</li>
                  <li>Suggest alternative payment method</li>
                  <li>Contact customer's bank if needed</li>
                  <li>Resend payment link if expired</li>
                </ul>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-yellow-700 mb-2">Payment Processing Delays</h3>
              <div className="text-gray-700 text-sm space-y-2">
                <p><strong>Typical Settlement Times:</strong></p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Credit cards: 2 business days</li>
                  <li>ACH transfers: 5-7 business days</li>
                  <li>International payments: 3-5 business days</li>
                </ul>
                <p><strong>Factors Affecting Speed:</strong></p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Banking holidays and weekends</li>
                  <li>Account verification status</li>
                  <li>Payment amount and risk assessment</li>
                </ul>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-blue-700 mb-2">Setting Up Webhooks for Advanced Integration</h3>
              <div className="text-gray-700 text-sm space-y-2">
                <p>For developers wanting real-time payment notifications:</p>
                <ol className="list-decimal list-inside space-y-1">
                  <li>Go to Settings → Developer Options</li>
                  <li>Add your webhook endpoint URL</li>
                  <li>Select events to monitor (payment.succeeded, payment.failed, etc.)</li>
                  <li>Test webhook delivery with sample events</li>
                </ol>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-green-600 to-teal-600 rounded-lg p-8 text-white text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Start Accepting Payments?</h2>
          <p className="mb-6">Set up payment processing and get paid faster with secure, professional payment links.</p>
          <Link href="/settings" className="inline-flex items-center bg-white text-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Configure Payment Settings
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
        </div>

        {/* Related Guides */}
        <div className="mt-12">
          <h3 className="text-xl font-semibold mb-4">Related Guides</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <Link href="/guides/getting-started" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <h4 className="font-semibold mb-2">Getting Started Guide</h4>
              <p className="text-sm text-gray-600">Learn the basics of creating and sending invoices</p>
            </Link>
            <Link href="/guides/automation" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <h4 className="font-semibold mb-2">Invoice Automation</h4>
              <p className="text-sm text-gray-600">Set up recurring billing and automatic reminders</p>
            </Link>
          </div>
        </div>
      </div>
    </main>
  )
}