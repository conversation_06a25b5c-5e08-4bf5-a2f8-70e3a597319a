import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { upgradeUserToPro, downgradeUserTo<PERSON>ree, getUserById } from '@/lib/user-service';
import { trackEvent, trackConversionFunnel } from '@/lib/analytics-service';
import { handleStripeWebhook as handleInvoicePayment } from '@/lib/payment-links';
import { ObjectId } from 'mongodb';
import { stripe, stripeWebhookSecret, isStripeConfigured, getStripeMode } from '@/lib/stripe-config';

// GET handler for testing webhook endpoint availability
export async function GET() {
  console.log('🔍 Webhook GET request received - endpoint is available');
  return NextResponse.json({ 
    status: 'Stripe webhook endpoint active',
    mode: getStripeMode(),
    configured: isStripeConfigured(),
    timestamp: new Date().toISOString()
  });
}

// OPTIONS handler for CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': 'https://api.stripe.com',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Stripe-Signature',
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    console.log('🎯 Stripe webhook POST received at:', new Date().toISOString());
    console.log('🔧 Stripe mode:', getStripeMode());
    
    // Check if Stripe is configured
    if (!stripe || !isStripeConfigured()) {
      console.error('❌ Stripe not configured');
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });
    }

    const body = await request.text();
    const signature = request.headers.get('stripe-signature');
    
    if (!signature) {
      console.error('❌ Missing Stripe signature header');
      return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
    }
    
    console.log('📝 Webhook body length:', body.length);
    console.log('🔐 Signature present:', !!signature);

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, stripeWebhookSecret);
      console.log('✅ Webhook signature verified successfully');
    } catch (err) {
      console.error('❌ Webhook signature verification failed:', err);
      console.error('🔑 Webhook secret length:', stripeWebhookSecret?.length || 0);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    console.log(`📨 Processing Stripe event: ${event.type} (${event.id})`);

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        
        if (session.mode === 'subscription') {
          const userId = session.metadata?.userId;
          const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
          
          if (userId && subscription.customer) {
            await upgradeUserToPro(
              new ObjectId(userId),
              subscription.customer as string,
              subscription.id
            );
            
            // Track successful upgrade completion
            await trackConversionFunnel(userId, 'upgrade_completed', {
              stripeCustomerId: subscription.customer,
              stripeSubscriptionId: subscription.id,
              amount: session.amount_total ? session.amount_total / 100 : 9.99,
              currency: session.currency || 'usd',
              paymentStatus: session.payment_status,
            });

            await trackEvent(userId, 'upgrade_completed', {
              stripeCustomerId: subscription.customer,
              stripeSubscriptionId: subscription.id,
              amount: session.amount_total ? session.amount_total / 100 : 9.99,
              currency: session.currency || 'usd',
              paymentStatus: session.payment_status,
              subscriptionStatus: subscription.status,
            }, {
              source: 'stripe_webhook',
            });
            
            console.log(`User ${userId} upgraded to Pro`);
          }
        } else if (session.mode === 'payment') {
          // Handle invoice payment
          const invoicePaymentResult = await handleInvoicePayment(event);
          if (!invoicePaymentResult.success) {
            console.error('Failed to handle invoice payment:', invoicePaymentResult.error);
          }
        }
        break;
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;
        const userId = subscription.metadata?.userId;

        if (userId) {
          if (subscription.status === 'active') {
            await upgradeUserToPro(
              new ObjectId(userId),
              subscription.customer as string,
              subscription.id
            );
            console.log(`Subscription activated for user ${userId}`);
          } else if (['canceled', 'unpaid', 'past_due'].includes(subscription.status)) {
            await downgradeUserToFree(new ObjectId(userId));
            console.log(`Subscription deactivated for user ${userId}`);
          }
        }
        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        const userId = subscription.metadata?.userId;

        if (userId) {
          await downgradeUserToFree(new ObjectId(userId));
          
          // Track subscription cancellation
          await trackEvent(userId, 'subscription_cancelled', {
            stripeSubscriptionId: subscription.id,
            stripeCustomerId: subscription.customer,
            cancelReason: subscription.cancel_at_period_end ? 'end_of_period' : 'immediate',
            subscriptionStatus: subscription.status,
            hadActiveSubscription: true,
          }, {
            source: 'stripe_webhook',
          });
          
          console.log(`Subscription cancelled for user ${userId}`);
        }
        break;
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice;
        
        if (invoice.subscription) {
          const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string);
          const userId = subscription.metadata?.userId;
          
          if (userId) {
            // Track billing failure
            await trackEvent(userId, 'billing_failed', {
              invoiceId: invoice.id,
              stripeSubscriptionId: subscription.id,
              stripeCustomerId: subscription.customer,
              amount: invoice.amount_due / 100,
              currency: invoice.currency,
              attemptCount: invoice.attempt_count,
              nextPaymentAttempt: invoice.next_payment_attempt,
            }, {
              source: 'stripe_webhook',
            });
            
            console.log(`Payment failed for user ${userId}`);
            // You might want to send an email notification here
          }
        }
        break;
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice;
        
        if (invoice.subscription) {
          const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string);
          const userId = subscription.metadata?.userId;
          
          if (userId) {
            console.log(`Payment succeeded for user ${userId}`);
            // Ensure user is marked as Pro
            await upgradeUserToPro(
              new ObjectId(userId),
              subscription.customer as string,
              subscription.id
            );
          }
        }
        break;
      }

      case 'payment_intent.succeeded': {
        // Handle direct payment intent success (for invoice payments)
        const paymentIntentResult = await handleInvoicePayment(event);
        if (!paymentIntentResult.success) {
          console.error('Failed to handle payment intent:', paymentIntentResult.error);
        }
        break;
      }


      default:
        console.log(`Unhandled event type: ${event.type}`);
        
        // Try to handle with invoice payment handler for any invoice-related events
        if (event.type.includes('payment') || event.type.includes('checkout')) {
          const fallbackResult = await handleInvoicePayment(event);
          if (fallbackResult.success && fallbackResult.message) {
            console.log('Handled by invoice payment processor:', fallbackResult.message);
          }
        }
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}