// Email Service for Template Invoice System
import nodemailer from 'nodemailer';
import { InvoiceDocument } from './models';
import { generateInvoicePDFWithRetry, generatePDFFilename } from './pdf-export';

// Email configuration
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// Email template types
type EmailTemplate = 'invoice_send' | 'payment_received' | 'payment_reminder' | 'payment_overdue';

// Email attachment interface
interface EmailAttachment {
  filename: string;
  content: Buffer;
  contentType: string;
}

// Email send result
interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  deliveryTime?: number;
}

// Get email configuration from environment
function getEmailConfig(): EmailConfig {
  const config: EmailConfig = {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || '',
      pass: process.env.SMTP_PASS || ''
    }
  };

  if (!config.auth.user || !config.auth.pass) {
    throw new Error('Email configuration is incomplete. Please set SMTP_USER and SMTP_PASS environment variables.');
  }

  return config;
}

// Create email transporter
async function createTransporter() {
  const config = getEmailConfig();
  
  const transporter = nodemailer.createTransport({
    host: config.host,
    port: config.port,
    secure: config.secure,
    auth: config.auth,
    tls: {
      rejectUnauthorized: false
    }
  });

  // Verify connection
  try {
    await transporter.verify();
    return transporter;
  } catch (error) {
    throw new Error(`Email connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Generate professional email templates
function generateEmailTemplate(
  template: EmailTemplate, 
  invoice: InvoiceDocument,
  options: {
    senderName?: string;
    customMessage?: string;
    paymentLink?: string;
  } = {}
): { subject: string; html: string; text: string } {
  const { senderName = invoice.businessInfo.name, customMessage, paymentLink } = options;
  
  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  switch (template) {
    case 'invoice_send':
      return {
        subject: `Invoice ${invoice.invoiceNumber} from ${senderName}`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>Invoice ${invoice.invoiceNumber}</title>
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background: #f8f9fa; padding: 30px; border-radius: 8px; margin-bottom: 30px; text-align: center; }
              .invoice-details { background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 25px; margin-bottom: 30px; }
              .amount { font-size: 24px; font-weight: bold; color: #1f2937; }
              .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; margin: 20px 0; }
              .footer { color: #6b7280; font-size: 14px; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; }
              .details-row { display: flex; justify-content: space-between; margin-bottom: 10px; }
              .label { font-weight: 500; color: #6b7280; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1 style="margin: 0; color: #1f2937;">New Invoice</h1>
                <p style="margin: 10px 0 0 0; color: #6b7280;">Invoice ${invoice.invoiceNumber}</p>
              </div>
              
              <div class="invoice-details">
                <div class="details-row">
                  <span class="label">From:</span>
                  <span>${invoice.businessInfo.name}</span>
                </div>
                <div class="details-row">
                  <span class="label">Invoice Number:</span>
                  <span>${invoice.invoiceNumber}</span>
                </div>
                <div class="details-row">
                  <span class="label">Issue Date:</span>
                  <span>${formatDate(invoice.createdAt)}</span>
                </div>
                <div class="details-row">
                  <span class="label">Due Date:</span>
                  <span>${formatDate(invoice.dueDate)}</span>
                </div>
                <div class="details-row" style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                  <span class="label">Total Amount:</span>
                  <span class="amount">${formatCurrency(invoice.totals.total, invoice.currency)}</span>
                </div>
              </div>

              ${customMessage ? `
                <div style="background: #f3f4f6; padding: 20px; border-radius: 6px; margin-bottom: 30px;">
                  <h3 style="margin: 0 0 10px 0; color: #1f2937;">Message from ${senderName}:</h3>
                  <p style="margin: 0; color: #4b5563;">${customMessage}</p>
                </div>
              ` : ''}

              ${paymentLink ? `
                <div style="text-align: center; margin: 30px 0;">
                  <a href="${paymentLink}" class="button">Pay Now</a>
                  <p style="color: #6b7280; font-size: 14px; margin-top: 10px;">
                    Click the button above to pay this invoice securely online
                  </p>
                </div>
              ` : ''}

              <div class="footer">
                <p><strong>Payment Instructions:</strong></p>
                <p>Please find the detailed invoice attached as a PDF. If you have any questions about this invoice, please contact us.</p>
                <p style="margin-top: 20px;">
                  <strong>Contact Information:</strong><br>
                  ${invoice.businessInfo.email ? `Email: ${invoice.businessInfo.email}<br>` : ''}
                  ${invoice.businessInfo.phone ? `Phone: ${invoice.businessInfo.phone}<br>` : ''}
                </p>
                <p style="margin-top: 20px; font-size: 12px; color: #9ca3af;">
                  This invoice was generated automatically. Please do not reply to this email.
                </p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
Invoice ${invoice.invoiceNumber} from ${senderName}

From: ${invoice.businessInfo.name}
Invoice Number: ${invoice.invoiceNumber}
Issue Date: ${formatDate(invoice.createdAt)}
Due Date: ${formatDate(invoice.dueDate)}
Total Amount: ${formatCurrency(invoice.totals.total, invoice.currency)}

${customMessage ? `Message from ${senderName}:\n${customMessage}\n\n` : ''}

${paymentLink ? `Pay online: ${paymentLink}\n\n` : ''}

Please find the detailed invoice attached as a PDF. If you have any questions about this invoice, please contact us.

Contact Information:
${invoice.businessInfo.email ? `Email: ${invoice.businessInfo.email}\n` : ''}
${invoice.businessInfo.phone ? `Phone: ${invoice.businessInfo.phone}\n` : ''}

This invoice was generated automatically. Please do not reply to this email.
        `
      };

    case 'payment_received':
      return {
        subject: `Payment Received - Invoice ${invoice.invoiceNumber}`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background: #10b981; color: white; padding: 30px; border-radius: 8px; margin-bottom: 30px; text-align: center; }
              .content { background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 25px; margin-bottom: 30px; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1 style="margin: 0;">Payment Received!</h1>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">Thank you for your payment</p>
              </div>
              
              <div class="content">
                <p>Dear ${invoice.clientInfo.name},</p>
                <p>We have received your payment for invoice ${invoice.invoiceNumber}.</p>
                <p><strong>Amount Paid:</strong> ${formatCurrency(invoice.totals.total, invoice.currency)}</p>
                <p><strong>Payment Date:</strong> ${formatDate(new Date())}</p>
                <p>Thank you for your prompt payment! A receipt has been generated and is attached to this email.</p>
                <p>Best regards,<br>${senderName}</p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
Payment Received - Invoice ${invoice.invoiceNumber}

Dear ${invoice.clientInfo.name},

We have received your payment for invoice ${invoice.invoiceNumber}.

Amount Paid: ${formatCurrency(invoice.totals.total, invoice.currency)}
Payment Date: ${formatDate(new Date())}

Thank you for your prompt payment! A receipt has been generated and is attached to this email.

Best regards,
${senderName}
        `
      };

    case 'payment_reminder':
      const daysPastDue = Math.ceil((new Date().getTime() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24));
      return {
        subject: `Payment Reminder - Invoice ${invoice.invoiceNumber}`,
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background: #f59e0b; color: white; padding: 30px; border-radius: 8px; margin-bottom: 30px; text-align: center; }
              .content { background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 25px; margin-bottom: 30px; }
              .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1 style="margin: 0;">Payment Reminder</h1>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">Invoice ${invoice.invoiceNumber}</p>
              </div>
              
              <div class="content">
                <p>Dear ${invoice.clientInfo.name},</p>
                <p>This is a friendly reminder that payment for invoice ${invoice.invoiceNumber} was due on ${formatDate(invoice.dueDate)}${daysPastDue > 0 ? ` (${daysPastDue} days ago)` : ''}.</p>
                <p><strong>Amount Due:</strong> ${formatCurrency(invoice.totals.total, invoice.currency)}</p>
                ${paymentLink ? `
                  <div style="text-align: center; margin: 20px 0;">
                    <a href="${paymentLink}" class="button">Pay Now</a>
                  </div>
                ` : ''}
                <p>If you have already made this payment, please disregard this reminder. If you have any questions or need to discuss payment arrangements, please contact us.</p>
                <p>Best regards,<br>${senderName}</p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
Payment Reminder - Invoice ${invoice.invoiceNumber}

Dear ${invoice.clientInfo.name},

This is a friendly reminder that payment for invoice ${invoice.invoiceNumber} was due on ${formatDate(invoice.dueDate)}${daysPastDue > 0 ? ` (${daysPastDue} days ago)` : ''}.

Amount Due: ${formatCurrency(invoice.totals.total, invoice.currency)}

${paymentLink ? `Pay online: ${paymentLink}\n\n` : ''}

If you have already made this payment, please disregard this reminder. If you have any questions or need to discuss payment arrangements, please contact us.

Best regards,
${senderName}
        `
      };

    default:
      throw new Error(`Unknown email template: ${template}`);
  }
}

// Send invoice via email
export async function sendInvoiceEmail(
  invoice: InvoiceDocument,
  options: {
    to?: string;
    cc?: string[];
    bcc?: string[];
    template?: EmailTemplate;
    customMessage?: string;
    paymentLink?: string;
    senderName?: string;
    includePDF?: boolean;
    logoUrl?: string;
  } = {}
): Promise<EmailResult> {
  const startTime = Date.now();
  
  try {
    const {
      to = invoice.clientInfo.email,
      cc,
      bcc,
      template = 'invoice_send',
      customMessage,
      paymentLink,
      senderName = invoice.businessInfo.name,
      includePDF = true,
      logoUrl
    } = options;

    if (!to) {
      throw new Error('Recipient email address is required');
    }

    // Validate email address
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(to)) {
      throw new Error('Invalid recipient email address');
    }

    // Create transporter
    const transporter = await createTransporter();

    // Generate email content
    const { subject, html, text } = generateEmailTemplate(template, invoice, {
      senderName,
      customMessage,
      paymentLink
    });

    // Prepare attachments
    const attachments: EmailAttachment[] = [];
    
    if (includePDF) {
      try {
        const pdfBuffer = await generateInvoicePDFWithRetry(invoice, { logoUrl });
        const filename = generatePDFFilename(invoice);
        
        attachments.push({
          filename,
          content: pdfBuffer,
          contentType: 'application/pdf'
        });
      } catch (pdfError) {
        console.error('PDF generation failed for email:', pdfError);
        // Continue without PDF attachment
      }
    }

    // Send email
    const mailOptions = {
      from: `"${senderName}" <${process.env.SMTP_USER}>`,
      to,
      cc,
      bcc,
      subject,
      text,
      html,
      attachments: attachments.map(att => ({
        filename: att.filename,
        content: att.content,
        contentType: att.contentType
      }))
    };

    const result = await transporter.sendMail(mailOptions);
    
    const deliveryTime = Date.now() - startTime;

    return {
      success: true,
      messageId: result.messageId,
      deliveryTime
    };

  } catch (error) {
    const deliveryTime = Date.now() - startTime;
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      deliveryTime
    };
  }
}

// Send payment reminder
export async function sendPaymentReminder(
  invoice: InvoiceDocument,
  options: {
    to?: string;
    customMessage?: string;
    paymentLink?: string;
    senderName?: string;
  } = {}
): Promise<EmailResult> {
  return sendInvoiceEmail(invoice, {
    ...options,
    template: 'payment_reminder',
    includePDF: true
  });
}

// Send payment confirmation
export async function sendPaymentConfirmation(
  invoice: InvoiceDocument,
  options: {
    to?: string;
    senderName?: string;
    receiptPDF?: Buffer;
  } = {}
): Promise<EmailResult> {
  const { receiptPDF } = options;
  
  const result = await sendInvoiceEmail(invoice, {
    ...options,
    template: 'payment_received',
    includePDF: false // We'll attach receipt instead
  });

  // If receipt PDF is provided, send it as attachment
  if (receiptPDF && result.success) {
    // This would require modifying the sendInvoiceEmail function to accept custom attachments
    // For now, we'll just return the result
  }

  return result;
}

// Batch send invoices
export async function sendBatchInvoices(
  invoices: InvoiceDocument[],
  options: {
    template?: EmailTemplate;
    customMessage?: string;
    paymentLink?: (invoice: InvoiceDocument) => string;
    senderName?: string;
    includePDF?: boolean;
    logoUrl?: string;
    batchSize?: number;
    delayBetweenBatches?: number;
  } = {}
): Promise<{ results: EmailResult[]; totalSent: number; totalFailed: number }> {
  const {
    batchSize = 5,
    delayBetweenBatches = 1000,
    paymentLink,
    ...commonOptions
  } = options;

  const results: EmailResult[] = [];
  let totalSent = 0;
  let totalFailed = 0;

  // Process in batches to avoid rate limiting
  for (let i = 0; i < invoices.length; i += batchSize) {
    const batch = invoices.slice(i, i + batchSize);
    
    const batchPromises = batch.map(invoice => 
      sendInvoiceEmail(invoice, {
        ...commonOptions,
        paymentLink: paymentLink ? paymentLink(invoice) : undefined
      })
    );

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Count successes and failures
    batchResults.forEach(result => {
      if (result.success) {
        totalSent++;
      } else {
        totalFailed++;
      }
    });

    // Delay between batches (except for the last batch)
    if (i + batchSize < invoices.length) {
      await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
    }
  }

  return { results, totalSent, totalFailed };
}

// Validate email configuration
export async function validateEmailConfig(): Promise<{ isValid: boolean; error?: string }> {
  try {
    await createTransporter();
    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Test email sending
export async function sendTestEmail(
  to: string,
  senderName: string = 'Template Invoice System'
): Promise<EmailResult> {
  try {
    const transporter = await createTransporter();

    const mailOptions = {
      from: `"${senderName}" <${process.env.SMTP_USER}>`,
      to,
      subject: 'Test Email from Template Invoice System',
      text: 'This is a test email to verify your email configuration is working correctly.',
      html: `
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #1f2937;">Test Email Successful!</h2>
          <p>This is a test email to verify your email configuration is working correctly.</p>
          <p style="color: #6b7280; font-size: 14px;">Sent from Template Invoice System</p>
        </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);

    return {
      success: true,
      messageId: result.messageId
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}