import type { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowR<PERSON>, Users, Plus, Search, FileText, Clock, Filter, Settings } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Managing Clients and Contacts - Organization Guide',
  description: 'Organize client information efficiently. Learn to add, edit, and manage client contacts for faster invoicing.',
  keywords: ['client management', 'invoice contacts', 'organize clients', 'client database', 'contact management', 'customer organization'],
  openGraph: {
    title: 'Managing Clients and Contacts - Organization Guide',
    description: 'Organize client information efficiently. Learn to add, edit, and manage client contacts for faster invoicing.',
    type: 'article',
  },
}

export default function ManagingClientsGuide() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Managing Clients & Contacts
            </h1>
            <p className="text-xl text-indigo-100 max-w-3xl mx-auto">
              Organize client information efficiently for faster invoicing and better relationships
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-gray-900">Home</Link>
          <span>/</span>
          <Link href="/guides" className="hover:text-gray-900">Guides</Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">Managing Clients</span>
        </nav>

        {/* Introduction */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Streamline Your Client Management</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Efficient client management is the foundation of smooth invoicing operations. Template Invoice's 
            client management system helps you organize contact information, track payment history, and 
            create invoices faster than ever before.
          </p>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-indigo-600" />
              </div>
              <h3 className="font-semibold mb-2">Centralized Database</h3>
              <p className="text-gray-600 text-sm">Store all client information in one secure, searchable location</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2">Faster Invoicing</h3>
              <p className="text-gray-600 text-sm">Pre-filled client details save time on every invoice</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <FileText className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold mb-2">Complete History</h3>
              <p className="text-gray-600 text-sm">Track all invoices and payments for each client</p>
            </div>
          </div>
        </section>

        {/* Adding Clients */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Adding New Clients</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Start building your client database by adding comprehensive contact information. The more 
            details you provide, the more efficient your invoicing process becomes.
          </p>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center">
              <Plus className="w-6 h-6 mr-2 text-indigo-600" />
              Step-by-Step Client Addition
            </h3>
            
            <ol className="space-y-4">
              <li className="flex">
                <span className="bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-4 mt-0.5">1</span>
                <div className="flex-1">
                  <h4 className="font-semibold mb-1">Navigate to Client Management</h4>
                  <p className="text-gray-700 text-sm">From your dashboard, click "Clients" in the main navigation or visit the <Link href="/dashboard" className="text-indigo-600 hover:underline">client management section</Link>.</p>
                </div>
              </li>
              <li className="flex">
                <span className="bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-4 mt-0.5">2</span>
                <div className="flex-1">
                  <h4 className="font-semibold mb-1">Click "Add New Client"</h4>
                  <p className="text-gray-700 text-sm">Look for the prominent "Add Client" button, usually located at the top right of the client list.</p>
                </div>
              </li>
              <li className="flex">
                <span className="bg-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-4 mt-0.5">3</span>
                <div className="flex-1">
                  <h4 className="font-semibold mb-1">Fill in Client Details</h4>
                  <p className="text-gray-700 text-sm">Complete the client information form with as much detail as possible for best results.</p>
                </div>
              </li>
            </ol>
          </div>

          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Required vs. Optional Information</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-blue-800 mb-3">Required Fields</h4>
                <ul className="space-y-2 text-blue-700 text-sm">
                  <li>• Company/Client name</li>
                  <li>• Primary email address</li>
                  <li>• Billing address</li>
                  <li>• Contact person name</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-blue-800 mb-3">Optional (But Recommended)</h4>
                <ul className="space-y-2 text-blue-700 text-sm">
                  <li>• Phone number</li>
                  <li>• Website URL</li>
                  <li>• Tax ID or VAT number</li>
                  <li>• Custom payment terms</li>
                  <li>• Internal notes</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Organizing Clients */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Organizing Your Client Database</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            As your client list grows, organization becomes crucial. Use these features to keep your 
            contacts manageable and easily accessible.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Filter className="w-6 h-6 mr-2 text-indigo-600" />
                Filtering and Sorting Options
              </h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Sort By</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Name (A-Z or Z-A)</li>
                    <li>• Date added (newest/oldest first)</li>
                    <li>• Last invoice date</li>
                    <li>• Total revenue</li>
                    <li>• Payment status</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Filter By</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Active vs. inactive clients</li>
                    <li>• Payment terms (Net 30, due on receipt, etc.)</li>
                    <li>• Geographic location</li>
                    <li>• Industry or client type</li>
                    <li>• Outstanding balances</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Search className="w-6 h-6 mr-2 text-indigo-600" />
                Advanced Search Features
              </h3>
              
              <div className="space-y-4">
                <div className="border border-gray-200 rounded p-4">
                  <h4 className="font-semibold mb-2">Quick Search</h4>
                  <p className="text-gray-700 text-sm mb-2">
                    Use the search bar to quickly find clients by:
                  </p>
                  <ul className="list-disc list-inside text-gray-700 text-xs space-y-1">
                    <li>Company name</li>
                    <li>Contact person name</li>
                    <li>Email address</li>
                    <li>Phone number</li>
                  </ul>
                </div>
                
                <div className="border border-gray-200 rounded p-4">
                  <h4 className="font-semibold mb-2">Smart Suggestions</h4>
                  <p className="text-gray-700 text-sm">
                    Template Invoice automatically suggests clients as you type, making invoice 
                    creation faster and reducing data entry errors.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Client History */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Client History and Analytics</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Understanding your client relationships through historical data helps you make better 
            business decisions and provide superior service.
          </p>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-xl font-semibold mb-4">Client Profile Overview</h3>
            <p className="text-gray-700 mb-4">
              Click on any client to view their complete profile, including:
            </p>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Invoice History</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Complete list of all invoices sent</li>
                  <li>• Payment status for each invoice</li>
                  <li>• Average payment time</li>
                  <li>• Total revenue from client</li>
                  <li>• Outstanding balance</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Communication Log</h4>
                <ul className="space-y-2 text-gray-700 text-sm">
                  <li>• Email correspondence history</li>
                  <li>• Payment reminder sent dates</li>
                  <li>• Notes and comments</li>
                  <li>• Important dates and milestones</li>
                  <li>• Custom tags and categories</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-2">Payment Performance</h4>
              <ul className="text-green-700 text-sm space-y-1">
                <li>• Average days to pay</li>
                <li>• Payment reliability score</li>
                <li>• Preferred payment methods</li>
              </ul>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">Revenue Analytics</h4>
              <ul className="text-blue-700 text-sm space-y-1">
                <li>• Monthly/yearly trends</li>
                <li>• Project frequency</li>
                <li>• Average invoice amount</li>
              </ul>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 className="font-semibold text-purple-800 mb-2">Relationship Health</h4>
              <ul className="text-purple-700 text-sm space-y-1">
                <li>• Last contact date</li>
                <li>• Engagement level</li>
                <li>• Follow-up reminders</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Client Settings */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Client-Specific Settings</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Customize how you interact with each client by setting up personalized preferences 
            and automation rules.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Settings className="w-6 h-6 mr-2 text-indigo-600" />
                Customizable Client Preferences
              </h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Payment Terms</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Custom payment due dates</li>
                    <li>• Preferred payment methods</li>
                    <li>• Late payment fee settings</li>
                    <li>• Discount terms for early payment</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Communication Preferences</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Email templates and signatures</li>
                    <li>• Reminder schedule preferences</li>
                    <li>• Language and currency settings</li>
                    <li>• CC recipients for invoices</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3 text-yellow-800">Pro Tips for Client Management</h3>
              <ul className="space-y-2 text-yellow-700">
                <li>• Set up custom fields for industry-specific information</li>
                <li>• Use tags to categorize clients by project type or size</li>
                <li>• Enable automatic birthday or anniversary reminders</li>
                <li>• Create client groups for bulk communications</li>
                <li>• Set spending or credit limits for risk management</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Best Practices */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Client Management Best Practices</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-green-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3 text-green-800">Do's</h3>
              <ul className="space-y-2 text-green-700">
                <li>• Keep client information updated regularly</li>
                <li>• Add detailed notes after important conversations</li>
                <li>• Set up automated reminders for follow-ups</li>
                <li>• Use consistent naming conventions</li>
                <li>• Back up your client database regularly</li>
              </ul>
            </div>
            
            <div className="bg-red-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3 text-red-800">Don'ts</h3>
              <ul className="space-y-2 text-red-700">
                <li>• Don't ignore inactive clients completely</li>
                <li>• Avoid duplicate client entries</li>
                <li>• Don't store sensitive data unnecessarily</li>
                <li>• Don't forget to update payment preferences</li>
                <li>• Avoid overcomplicating your organization system</li>
              </ul>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-lg p-8 text-white text-center">
          <h2 className="text-2xl font-bold mb-4">Start Organizing Your Clients Today</h2>
          <p className="mb-6">Build a comprehensive client database and streamline your invoicing process.</p>
          <Link href="/dashboard" className="inline-flex items-center bg-white text-indigo-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Manage Clients
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
        </div>

        {/* Related Guides */}
        <div className="mt-12">
          <h3 className="text-xl font-semibold mb-4">Related Guides</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <Link href="/guides/getting-started" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <h4 className="font-semibold mb-2">Getting Started Guide</h4>
              <p className="text-sm text-gray-600">Learn the basics of creating your first invoice</p>
            </Link>
            <Link href="/guides/automation" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <h4 className="font-semibold mb-2">Invoice Automation</h4>
              <p className="text-sm text-gray-600">Set up recurring invoices and automated workflows</p>
            </Link>
          </div>
        </div>
      </div>
    </main>
  )
}