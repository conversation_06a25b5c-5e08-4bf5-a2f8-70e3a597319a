'use client'

import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, Pagination } from 'swiper/modules'
import { motion } from 'framer-motion'
import { Star } from 'lucide-react'
import 'swiper/css'
import 'swiper/css/pagination'

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'CEO, TechStart',
    content: 'This platform transformed how we handle invoicing. What used to take hours now takes minutes.',
    rating: 5,
    image: '/api/placeholder/100/100'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Freelance Designer',
    content: 'The templates are gorgeous and professional. My clients are always impressed.',
    rating: 5,
    image: '/api/placeholder/100/100'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'CFO, GlobalTech',
    content: 'The analytics dashboard gives us insights we never had before. Game-changing for our finance team.',
    rating: 5,
    image: '/api/placeholder/100/100'
  },
  {
    id: 4,
    name: '<PERSON>',
    role: 'Small Business Owner',
    content: 'Simple, intuitive, and powerful. Exactly what I needed to manage my invoicing.',
    rating: 5,
    image: '/api/placeholder/100/100'
  },
]

export default function TestimonialSlider() {
  return (
    <div id="testimonials" className="py-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        className="text-center mb-16"
      >
        <h2 className="text-4xl lg:text-5xl font-bold mb-4">
          Loved by Thousands
        </h2>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          See what our customers have to say about their experience
        </p>
      </motion.div>

      <Swiper
        modules={[Autoplay, Pagination]}
        spaceBetween={30}
        slidesPerView={1}
        autoplay={{ delay: 5000, disableOnInteraction: false }}
        pagination={{ clickable: true }}
        className="testimonial-swiper pb-16"
      >
        {testimonials.map((testimonial) => (
          <SwiperSlide key={testimonial.id}>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white rounded-2xl shadow-xl p-8 lg:p-12 max-w-3xl mx-auto"
            >
              <div className="flex items-center gap-1 mb-6">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              <p className="text-xl lg:text-2xl text-gray-700 mb-8 italic">
                "{testimonial.content}"
              </p>
              <div className="flex items-center gap-4">
                <img 
                  src={testimonial.image} 
                  alt={testimonial.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div>
                  <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                  <p className="text-gray-600">{testimonial.role}</p>
                </div>
              </div>
            </motion.div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  )
}