'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface SubscriptionCardProps {
  className?: string;
}

export default function SubscriptionCard({ className = '' }: SubscriptionCardProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [upgrading, setUpgrading] = useState(false);

  if (!session?.user) return null;

  const { subscription } = session.user;
  const isPro = subscription?.plan === 'pro';
  const invoicesUsed = subscription?.invoicesUsed || 0;
  const remainingInvoices = isPro ? 'Unlimited' : Math.max(0, 3 - invoicesUsed);

  const handleUpgrade = async () => {
    setUpgrading(true);
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          successUrl: `${window.location.origin}/dashboard?upgraded=true`,
          cancelUrl: window.location.href,
        }),
      });

      const data = await response.json();

      if (data.url) {
        window.location.href = data.url;
      } else {
        console.error('Failed to create checkout session');
        alert('Failed to start upgrade process. Please try again.');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setUpgrading(false);
    }
  };

  const handleManageSubscription = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/stripe/portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          returnUrl: window.location.href,
        }),
      });

      const data = await response.json();

      if (data.url) {
        window.location.href = data.url;
      } else {
        console.error('Failed to create portal session');
        alert('Failed to access billing portal. Please try again.');
      }
    } catch (error) {
      console.error('Error accessing billing portal:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Subscription</h3>
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
          isPro 
            ? 'bg-green-100 text-green-800' 
            : 'bg-blue-100 text-blue-800'
        }`}>
          {isPro ? 'Pro' : 'Free'}
        </span>
      </div>

      {/* Current Plan */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">Current Plan</span>
          <span className="font-medium text-gray-900">
            {isPro ? 'Pro - $9.99/month' : 'Free Plan'}
          </span>
        </div>
        
        {/* Usage */}
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">Invoices this month</span>
          <span className="font-medium text-gray-900">
            {isPro ? 'Unlimited' : `${invoicesUsed}/3`}
          </span>
        </div>

        {!isPro && (
          <div className="mt-3">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Usage</span>
              <span>{invoicesUsed}/3</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  invoicesUsed === 3 ? 'bg-red-500' : 
                  invoicesUsed === 2 ? 'bg-yellow-500' : 
                  'bg-blue-500'
                }`}
                style={{ width: `${(invoicesUsed / 3) * 100}%` }}
              />
            </div>
            {invoicesUsed === 3 && (
              <p className="text-xs text-red-600 mt-1">
                You've reached your monthly limit. Upgrade to Pro for unlimited invoices.
              </p>
            )}
          </div>
        )}
      </div>

      {/* Next Billing (Pro users only) */}
      {isPro && subscription?.resetDate && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Next billing</span>
            <span className="text-sm font-medium text-gray-900">
              {new Date(subscription.resetDate).toLocaleDateString()}
            </span>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-2">
        {!isPro ? (
          <button
            onClick={handleUpgrade}
            disabled={upgrading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {upgrading ? 'Processing...' : 'Upgrade to Pro - $9.99/month'}
          </button>
        ) : (
          <button
            onClick={handleManageSubscription}
            disabled={loading}
            className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Loading...' : 'Manage Subscription'}
          </button>
        )}

        {isPro && (
          <p className="text-xs text-gray-500 text-center">
            Cancel anytime through subscription management
          </p>
        )}
      </div>

      {/* Pro Benefits (Free users only) */}
      {!isPro && (
        <div className="mt-4 p-3 bg-blue-50 rounded-md">
          <p className="text-sm font-medium text-blue-900 mb-2">Pro Plan includes:</p>
          <ul className="text-xs text-blue-800 space-y-1">
            <li>• Unlimited invoices</li>
            <li>• All premium templates</li>
            <li>• Priority support</li>
            <li>• Advanced analytics</li>
            <li>• Custom branding</li>
          </ul>
        </div>
      )}
    </div>
  );
}