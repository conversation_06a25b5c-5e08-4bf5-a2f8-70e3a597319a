import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const metric = await request.json();
    
    // Validate the metric data
    if (!metric.name || typeof metric.value !== 'number') {
      return NextResponse.json({ error: 'Invalid metric data' }, { status: 400 });
    }
    
    // In production, you might want to:
    // 1. Send to Google Analytics
    // 2. Store in your database
    // 3. Send to monitoring services like DataDog, New Relic, etc.
    
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to external analytics service
      // await fetch('https://your-analytics-service.com/metrics', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     timestamp: Date.now(),
      //     metric: metric,
      //     userAgent: request.headers.get('user-agent'),
      //     url: request.headers.get('referer'),
      //   }),
      // });
      
      console.log('Web Vital recorded:', metric);
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Web Vitals API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}