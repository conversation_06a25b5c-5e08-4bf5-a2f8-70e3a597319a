import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createInvoice, getUserInvoices } from '@/lib/invoice-service';
import { canCreateInvoice, incrementInvoiceCount } from '@/lib/invoice-limits';
import { trackEvent, trackConversionFunnel } from '@/lib/analytics-service';
import { ObjectId } from 'mongodb';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userAgent = request.headers.get('user-agent') || undefined;
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     undefined;

    // Check if user can create invoices (freemium logic)
    const canCreate = await canCreateInvoice(session.user.id);
    if (!canCreate.canCreate) {
      // Track when user hits limit
      await trackConversionFunnel(session.user.id, 'limit_hit', {
        remainingInvoices: canCreate.remainingInvoices,
        reason: canCreate.reason,
        userPlan: session.user.subscription?.plan || 'free',
        invoicesUsed: session.user.subscription?.invoicesUsed || 0,
      });

      await trackEvent(session.user.id, 'invoice_limit_hit', {
        remainingInvoices: canCreate.remainingInvoices,
        reason: canCreate.reason,
        userPlan: session.user.subscription?.plan || 'free',
        invoicesUsed: session.user.subscription?.invoicesUsed || 0,
        attemptedAction: 'create_invoice',
      }, {
        userAgent,
        ipAddress,
        source: 'invoice_creation_api',
      });

      return NextResponse.json({
        error: 'Invoice limit reached',
        reason: canCreate.reason,
        remainingInvoices: canCreate.remainingInvoices,
        showUpgrade: true,
      }, { status: 403 });
    }

    const invoiceData = await request.json();
    
    // Ensure userId matches the authenticated user
    invoiceData.userId = new ObjectId(session.user.id);

    const invoice = await createInvoice(invoiceData);

    // Track successful invoice creation
    await trackEvent(session.user.id, 'invoice_created', {
      invoiceId: invoice._id?.toString(),
      invoiceNumber: invoice.invoiceNumber,
      invoiceTotal: invoice.totals.total,
      currency: invoice.currency,
      userPlan: session.user.subscription?.plan || 'free',
      invoicesUsed: (session.user.subscription?.invoicesUsed || 0) + 1,
      templateId: invoice.templateId,
    }, {
      userAgent,
      ipAddress,
      source: 'invoice_creation_api',
    });

    return NextResponse.json(invoice, { status: 201 });

  } catch (error) {
    console.error('Error creating invoice:', error);
    return NextResponse.json(
      { error: 'Failed to create invoice' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') as any;
    const search = searchParams.get('search') || undefined;
    const sortBy = searchParams.get('sortBy') as any || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') as any || 'desc';

    const result = await getUserInvoices(session.user.id, {
      page,
      limit,
      status,
      search,
      sortBy,
      sortOrder
    });

    return NextResponse.json({ 
      invoices: result.invoices,
      total: result.total,
      hasMore: result.hasMore,
      page,
      limit
    });

  } catch (error) {
    console.error('Error fetching invoices:', error);
    return NextResponse.json(
      { error: 'Failed to fetch invoices' },
      { status: 500 }
    );
  }
}