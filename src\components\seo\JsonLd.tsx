import Script from 'next/script'

interface JsonLdProps {
  data: Record<string, any>
}

export function JsonLd({ data }: JsonLdProps) {
  return (
    <Script
      id={`json-ld-${data['@type']}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data)
      }}
      strategy="beforeInteractive"
    />
  )
}

// Specific schema components
interface OrganizationSchemaProps {
  name: string
  url: string
  logo: string
  sameAs?: string[]
  contactPoint?: {
    email: string
    contactType: string
    availableLanguage?: string[]
  }
}

export function OrganizationSchema({
  name,
  url,
  logo,
  sameAs = [],
  contactPoint
}: OrganizationSchemaProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name,
    url,
    logo,
    sameAs,
    ...(contactPoint && {
      contactPoint: {
        '@type': 'ContactPoint',
        ...contactPoint
      }
    })
  }
  
  return <JsonLd data={data} />
}

interface WebApplicationSchemaProps {
  name: string
  description: string
  url: string
  applicationCategory?: string
  operatingSystem?: string
  offers?: {
    price: string | number
    priceCurrency: string
    description?: string
  }
  aggregateRating?: {
    ratingValue: string | number
    reviewCount: string | number
  }
}

export function WebApplicationSchema({
  name,
  description,
  url,
  applicationCategory = 'BusinessApplication',
  operatingSystem = 'Web Browser',
  offers,
  aggregateRating
}: WebApplicationSchemaProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name,
    description,
    url,
    applicationCategory,
    operatingSystem,
    ...(offers && {
      offers: {
        '@type': 'Offer',
        ...offers
      }
    }),
    ...(aggregateRating && {
      aggregateRating: {
        '@type': 'AggregateRating',
        ...aggregateRating
      }
    })
  }
  
  return <JsonLd data={data} />
}

interface FAQItem {
  question: string
  answer: string
}

interface FAQSchemaProps {
  items: FAQItem[]
}

export function FAQSchema({ items }: FAQSchemaProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: items.map(item => ({
      '@type': 'Question',
      name: item.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: item.answer
      }
    }))
  }
  
  return <JsonLd data={data} />
}

interface BreadcrumbItem {
  name: string
  url: string
}

interface BreadcrumbSchemaProps {
  items: BreadcrumbItem[]
}

export function BreadcrumbSchema({ items }: BreadcrumbSchemaProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  }
  
  return <JsonLd data={data} />
}

interface ProductSchemaProps {
  name: string
  description: string
  image?: string
  brand?: string
  offers?: {
    price: string | number
    priceCurrency: string
    availability?: string
  }
  aggregateRating?: {
    ratingValue: string | number
    reviewCount: string | number
  }
  category?: string
}

export function ProductSchema({
  name,
  description,
  image,
  brand,
  offers,
  aggregateRating,
  category
}: ProductSchemaProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name,
    description,
    ...(image && { image }),
    ...(brand && {
      brand: {
        '@type': 'Brand',
        name: brand
      }
    }),
    ...(offers && {
      offers: {
        '@type': 'Offer',
        ...offers
      }
    }),
    ...(aggregateRating && {
      aggregateRating: {
        '@type': 'AggregateRating',
        ...aggregateRating
      }
    }),
    ...(category && { category })
  }
  
  return <JsonLd data={data} />
}

interface ArticleSchemaProps {
  headline: string
  description: string
  author: string
  datePublished: string
  dateModified?: string
  image?: string
  publisher?: {
    name: string
    logo: string
  }
}

export function ArticleSchema({
  headline,
  description,
  author,
  datePublished,
  dateModified,
  image,
  publisher
}: ArticleSchemaProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline,
    description,
    author: {
      '@type': 'Person',
      name: author
    },
    datePublished,
    ...(dateModified && { dateModified }),
    ...(image && { image }),
    ...(publisher && {
      publisher: {
        '@type': 'Organization',
        name: publisher.name,
        logo: {
          '@type': 'ImageObject',
          url: publisher.logo
        }
      }
    })
  }
  
  return <JsonLd data={data} />
}

interface HowToStep {
  name: string
  text: string
  image?: string
  url?: string
}

interface HowToSchemaProps {
  name: string
  description: string
  image?: string
  totalTime?: string
  estimatedCost?: {
    value: string | number
    currency: string
  }
  supply?: string[]
  tool?: string[]
  step: HowToStep[]
}

export function HowToSchema({
  name,
  description,
  image,
  totalTime,
  estimatedCost,
  supply,
  tool,
  step
}: HowToSchemaProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'HowTo',
    name,
    description,
    ...(image && { image }),
    ...(totalTime && { totalTime }),
    ...(estimatedCost && {
      estimatedCost: {
        '@type': 'MonetaryAmount',
        ...estimatedCost
      }
    }),
    ...(supply && { supply: supply.map(s => ({ '@type': 'HowToSupply', name: s })) }),
    ...(tool && { tool: tool.map(t => ({ '@type': 'HowToTool', name: t })) }),
    step: step.map((s, index) => ({
      '@type': 'HowToStep',
      name: s.name,
      text: s.text,
      ...(s.image && { image: s.image }),
      ...(s.url && { url: s.url }),
      position: index + 1
    }))
  }
  
  return <JsonLd data={data} />
}

interface LocalBusinessSchemaProps {
  name: string
  description: string
  url: string
  telephone?: string
  address?: {
    streetAddress: string
    addressLocality: string
    addressRegion: string
    postalCode: string
    addressCountry: string
  }
  geo?: {
    latitude: number
    longitude: number
  }
  openingHours?: string[]
  priceRange?: string
}

export function LocalBusinessSchema({
  name,
  description,
  url,
  telephone,
  address,
  geo,
  openingHours,
  priceRange
}: LocalBusinessSchemaProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    name,
    description,
    url,
    ...(telephone && { telephone }),
    ...(address && {
      address: {
        '@type': 'PostalAddress',
        ...address
      }
    }),
    ...(geo && {
      geo: {
        '@type': 'GeoCoordinates',
        latitude: geo.latitude,
        longitude: geo.longitude
      }
    }),
    ...(openingHours && { openingHours }),
    ...(priceRange && { priceRange })
  }
  
  return <JsonLd data={data} />
}