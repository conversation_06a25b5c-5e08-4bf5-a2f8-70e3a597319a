'use client';

import { useEffect } from 'react';

interface GAProps {
  measurementId: string;
}

export default function GoogleAnalytics({ measurementId }: GAProps) {
  useEffect(() => {
    // Only load in production
    if (process.env.NODE_ENV !== 'production') return;
    
    // Load Google Analytics
    const script = document.createElement('script');
    script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
    script.async = true;
    document.head.appendChild(script);
    
    script.onload = () => {
      (window as any).dataLayer = (window as any).dataLayer || [];
      function gtag(...args: any[]) {
        (window as any).dataLayer.push(args);
      }
      (window as any).gtag = gtag;
      
      gtag('js', new Date());
      gtag('config', measurementId, {
        page_title: document.title,
        page_location: window.location.href,
      });
    };
    
    return () => {
      // Cleanup if component unmounts
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [measurementId]);
  
  return null;
}