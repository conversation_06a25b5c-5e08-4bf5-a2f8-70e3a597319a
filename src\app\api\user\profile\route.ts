import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserById } from '@/lib/user-service';

/**
 * API Route: Get user profile
 * GET /api/user/profile
 * 
 * Returns the authenticated user's profile including business information
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile from database
    const user = await getUserById(session.user.id);
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Return user profile (excluding sensitive data)
    return NextResponse.json({
      id: user._id,
      name: user.name,
      email: user.email,
      image: user.image,
      subscription: user.subscription,
      businessProfile: (user as any).businessProfile || null,
      preferences: (user as any).preferences || {},
      createdAt: user.createdAt
    });

  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user profile' },
      { status: 500 }
    );
  }
}

/**
 * API Route: Update user profile
 * PUT /api/user/profile
 * 
 * Updates the authenticated user's profile including business information
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const updates = await request.json();
    
    // Validate allowed fields
    const allowedFields = ['name', 'businessProfile', 'preferences'];
    const filteredUpdates: any = {};
    
    for (const field of allowedFields) {
      if (updates[field] !== undefined) {
        filteredUpdates[field] = updates[field];
      }
    }

    // Update user profile
    const { ObjectId } = await import('mongodb');
    const clientPromise = await import('@/lib/mongodb');
    const client = await (clientPromise as any).default;
    const db = client.db();
    const usersCollection = db.collection('users');

    const userObjectId = typeof session.user.id === 'string' ? new ObjectId(session.user.id) : session.user.id;
    
    const result = await usersCollection.findOneAndUpdate(
      { _id: userObjectId },
      { $set: { ...filteredUpdates, updatedAt: new Date() } },
      { returnDocument: 'after' }
    );
    
    if (!result) {
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Profile updated successfully',
      user: {
        id: result._id,
        name: result.name,
        email: result.email,
        businessProfile: (result as any).businessProfile || null,
        preferences: (result as any).preferences || {}
      }
    });

  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json(
      { error: 'Failed to update user profile' },
      { status: 500 }
    );
  }
}