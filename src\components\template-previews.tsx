'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Check, Eye, Palette, FileText, Sparkles, Users, Briefcase, User } from 'lucide-react'

// Sample invoice data for previews
const sampleInvoiceData = {
  business: {
    name: "Acme Solutions Inc.",
    address: "123 Business Street",
    city: "San Francisco, CA 94107",
    email: "<EMAIL>",
    phone: "+****************",
    website: "www.acmesolutions.com"
  },
  client: {
    name: "Tech Startup Co.",
    address: "456 Innovation Ave",
    city: "Palo Alto, CA 94301",
    email: "<EMAIL>"
  },
  invoice: {
    number: "INV-2024-001",
    date: "January 15, 2024",
    dueDate: "February 14, 2024",
    terms: "Net 30"
  },
  items: [
    { description: "Website Development", quantity: 1, rate: 5000, amount: 5000 },
    { description: "UI/UX Design", quantity: 40, rate: 125, amount: 5000 },
    { description: "Content Management Setup", quantity: 1, rate: 1500, amount: 1500 }
  ],
  subtotal: 11500,
  tax: 920,
  total: 12420,
  notes: "Thank you for your business! Payment is due within 30 days."
}

// Template metadata
export interface TemplateMetadata {
  id: string
  name: string
  description: string
  industries: string[]
  features: string[]
  icon: React.ReactNode
  color: string
  preview: React.ComponentType<{ data: typeof sampleInvoiceData; isSelected?: boolean }>
}

// Professional Template
const ProfessionalTemplate = ({ data, isSelected }: { data: typeof sampleInvoiceData; isSelected?: boolean }) => (
  <div className={`bg-white border rounded-lg p-6 transition-all ${isSelected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-sm hover:shadow-md'}`}>
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b pb-4">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{data.business.name}</h1>
            <div className="text-sm text-gray-600 mt-1">
              <p>{data.business.address}</p>
              <p>{data.business.city}</p>
              <p>{data.business.email} • {data.business.phone}</p>
            </div>
          </div>
          <div className="text-right">
            <h2 className="text-xl font-semibold text-gray-800">INVOICE</h2>
            <p className="text-sm text-gray-600">{data.invoice.number}</p>
          </div>
        </div>
      </div>

      {/* Invoice Details */}
      <div className="grid grid-cols-2 gap-6">
        <div>
          <h3 className="font-semibold text-gray-800 mb-2">Bill To:</h3>
          <div className="text-sm text-gray-600">
            <p className="font-medium">{data.client.name}</p>
            <p>{data.client.address}</p>
            <p>{data.client.city}</p>
            <p>{data.client.email}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Date:</span>
              <span>{data.invoice.date}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Due Date:</span>
              <span>{data.invoice.dueDate}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Terms:</span>
              <span>{data.invoice.terms}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Line Items */}
      <div>
        <div className="bg-gray-50 px-4 py-2 font-semibold text-sm text-gray-700 border-t border-b">
          <div className="grid grid-cols-12 gap-4">
            <div className="col-span-6">Description</div>
            <div className="col-span-2 text-center">Qty</div>
            <div className="col-span-2 text-right">Rate</div>
            <div className="col-span-2 text-right">Amount</div>
          </div>
        </div>
        {data.items.map((item, index) => (
          <div key={index} className="px-4 py-2 text-sm border-b">
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-6">{item.description}</div>
              <div className="col-span-2 text-center">{item.quantity}</div>
              <div className="col-span-2 text-right">${item.rate.toLocaleString()}</div>
              <div className="col-span-2 text-right">${item.amount.toLocaleString()}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Totals */}
      <div className="flex justify-end">
        <div className="w-64 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Subtotal:</span>
            <span>${data.subtotal.toLocaleString()}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Tax (8%):</span>
            <span>${data.tax.toLocaleString()}</span>
          </div>
          <div className="flex justify-between font-bold text-lg border-t pt-2">
            <span>Total:</span>
            <span>${data.total.toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Notes */}
      <div className="text-sm text-gray-600 pt-4 border-t">
        <p className="font-medium mb-1">Notes:</p>
        <p>{data.notes}</p>
      </div>
    </div>
  </div>
)

// Modern Template
const ModernTemplate = ({ data, isSelected }: { data: typeof sampleInvoiceData; isSelected?: boolean }) => (
  <div className={`bg-gradient-to-br from-blue-50 to-indigo-50 border rounded-xl p-6 transition-all ${isSelected ? 'ring-2 ring-indigo-500 shadow-lg' : 'shadow-sm hover:shadow-md'}`}>
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">{data.business.name.charAt(0)}</span>
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">
              {data.business.name}
            </h1>
            <p className="text-sm text-gray-600">{data.business.email}</p>
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h2 className="text-2xl font-bold text-indigo-600">INVOICE</h2>
          <p className="text-gray-600">{data.invoice.number}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="font-semibold text-indigo-600 mb-3">Bill To</h3>
          <div className="text-sm space-y-1">
            <p className="font-medium text-gray-900">{data.client.name}</p>
            <p className="text-gray-600">{data.client.address}</p>
            <p className="text-gray-600">{data.client.city}</p>
            <p className="text-gray-600">{data.client.email}</p>
          </div>
        </div>
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="font-semibold text-indigo-600 mb-3">Invoice Details</h3>
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-600">Date:</span>
              <span className="text-gray-900">{data.invoice.date}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Due:</span>
              <span className="text-gray-900">{data.invoice.dueDate}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Terms:</span>
              <span className="text-gray-900">{data.invoice.terms}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Line Items */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="bg-indigo-600 text-white px-4 py-3 font-semibold text-sm">
          <div className="grid grid-cols-12 gap-4">
            <div className="col-span-6">Description</div>
            <div className="col-span-2 text-center">Qty</div>
            <div className="col-span-2 text-right">Rate</div>
            <div className="col-span-2 text-right">Amount</div>
          </div>
        </div>
        {data.items.map((item, index) => (
          <div key={index} className={`px-4 py-3 text-sm ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}`}>
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-6 font-medium">{item.description}</div>
              <div className="col-span-2 text-center">{item.quantity}</div>
              <div className="col-span-2 text-right">${item.rate.toLocaleString()}</div>
              <div className="col-span-2 text-right font-medium">${item.amount.toLocaleString()}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Totals */}
      <div className="flex justify-end">
        <div className="bg-white rounded-lg p-4 shadow-sm w-64">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Subtotal:</span>
              <span>${data.subtotal.toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Tax:</span>
              <span>${data.tax.toLocaleString()}</span>
            </div>
            <div className="flex justify-between font-bold text-lg text-indigo-600 border-t pt-2">
              <span>Total:</span>
              <span>${data.total.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

// Classic Template
const ClassicTemplate = ({ data, isSelected }: { data: typeof sampleInvoiceData; isSelected?: boolean }) => (
  <div className={`bg-white border-2 border-gray-800 p-6 transition-all ${isSelected ? 'ring-2 ring-gray-700 shadow-lg' : 'shadow-sm hover:shadow-md'}`}>
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center border-b-2 border-gray-800 pb-4">
        <h1 className="text-3xl font-serif font-bold text-gray-900 mb-2">{data.business.name}</h1>
        <div className="text-sm text-gray-700 space-y-1">
          <p>{data.business.address} • {data.business.city}</p>
          <p>{data.business.phone} • {data.business.email}</p>
        </div>
      </div>

      {/* Invoice Title */}
      <div className="text-center">
        <h2 className="text-2xl font-serif font-bold text-gray-900 border-b border-gray-400 inline-block px-4 pb-1">
          INVOICE
        </h2>
        <p className="text-lg font-semibold mt-2">{data.invoice.number}</p>
      </div>

      {/* Details */}
      <div className="grid grid-cols-2 gap-8">
        <div>
          <h3 className="font-serif font-bold text-gray-900 border-b border-gray-400 pb-1 mb-3">BILL TO:</h3>
          <div className="text-sm space-y-1">
            <p className="font-semibold">{data.client.name}</p>
            <p>{data.client.address}</p>
            <p>{data.client.city}</p>
            <p>{data.client.email}</p>
          </div>
        </div>
        <div>
          <h3 className="font-serif font-bold text-gray-900 border-b border-gray-400 pb-1 mb-3">DETAILS:</h3>
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span className="font-semibold">Date:</span>
              <span>{data.invoice.date}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">Due Date:</span>
              <span>{data.invoice.dueDate}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">Terms:</span>
              <span>{data.invoice.terms}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Line Items */}
      <div className="border-2 border-gray-800">
        <div className="bg-gray-800 text-white px-4 py-2 font-bold text-sm">
          <div className="grid grid-cols-12 gap-4">
            <div className="col-span-6">DESCRIPTION</div>
            <div className="col-span-2 text-center">QTY</div>
            <div className="col-span-2 text-right">RATE</div>
            <div className="col-span-2 text-right">AMOUNT</div>
          </div>
        </div>
        {data.items.map((item, index) => (
          <div key={index} className="px-4 py-2 text-sm border-b border-gray-300 last:border-b-0">
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-6">{item.description}</div>
              <div className="col-span-2 text-center">{item.quantity}</div>
              <div className="col-span-2 text-right">${item.rate.toLocaleString()}</div>
              <div className="col-span-2 text-right">${item.amount.toLocaleString()}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Totals */}
      <div className="flex justify-end">
        <div className="border-2 border-gray-800 w-64">
          <div className="space-y-0">
            <div className="flex justify-between px-4 py-2 text-sm border-b border-gray-300">
              <span className="font-semibold">Subtotal:</span>
              <span>${data.subtotal.toLocaleString()}</span>
            </div>
            <div className="flex justify-between px-4 py-2 text-sm border-b border-gray-300">
              <span className="font-semibold">Tax:</span>
              <span>${data.tax.toLocaleString()}</span>
            </div>
            <div className="flex justify-between px-4 py-3 font-bold text-lg bg-gray-800 text-white">
              <span>TOTAL:</span>
              <span>${data.total.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Notes */}
      <div className="border-t-2 border-gray-800 pt-4">
        <h4 className="font-serif font-bold text-gray-900 mb-2">NOTES:</h4>
        <p className="text-sm text-gray-700">{data.notes}</p>
      </div>
    </div>
  </div>
)

// Creative Template
const CreativeTemplate = ({ data, isSelected }: { data: typeof sampleInvoiceData; isSelected?: boolean }) => (
  <div className={`bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 border rounded-2xl p-6 transition-all ${isSelected ? 'ring-2 ring-purple-500 shadow-lg' : 'shadow-sm hover:shadow-md'}`}>
    <div className="space-y-6">
      {/* Header */}
      <div className="relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full opacity-10"></div>
        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
            <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-600 bg-clip-text text-transparent mb-2">
            {data.business.name}
          </h1>
          <p className="text-gray-600">{data.business.email} • {data.business.phone}</p>
        </div>
      </div>

      {/* Invoice Header */}
      <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 shadow-sm">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Invoice</h2>
            <p className="text-purple-600 font-semibold">{data.invoice.number}</p>
          </div>
          <div className="text-right text-sm">
            <p><span className="font-medium">Date:</span> {data.invoice.date}</p>
            <p><span className="font-medium">Due:</span> {data.invoice.dueDate}</p>
          </div>
        </div>
      </div>

      {/* Client Info */}
      <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 shadow-sm">
        <h3 className="font-bold text-purple-600 mb-3">Bill To</h3>
        <div className="text-sm space-y-1">
          <p className="font-semibold text-gray-900">{data.client.name}</p>
          <p className="text-gray-600">{data.client.address}</p>
          <p className="text-gray-600">{data.client.city}</p>
          <p className="text-gray-600">{data.client.email}</p>
        </div>
      </div>

      {/* Line Items */}
      <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-sm overflow-hidden">
        <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-3 font-semibold text-sm">
          <div className="grid grid-cols-12 gap-4">
            <div className="col-span-6">Description</div>
            <div className="col-span-2 text-center">Qty</div>
            <div className="col-span-2 text-right">Rate</div>
            <div className="col-span-2 text-right">Amount</div>
          </div>
        </div>
        {data.items.map((item, index) => (
          <div key={index} className="px-4 py-3 text-sm border-b border-gray-200 last:border-b-0">
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-6 font-medium">{item.description}</div>
              <div className="col-span-2 text-center">{item.quantity}</div>
              <div className="col-span-2 text-right">${item.rate.toLocaleString()}</div>
              <div className="col-span-2 text-right font-medium">${item.amount.toLocaleString()}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Totals */}
      <div className="flex justify-end">
        <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 shadow-sm w-64">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Subtotal:</span>
              <span>${data.subtotal.toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Tax:</span>
              <span>${data.tax.toLocaleString()}</span>
            </div>
            <div className="flex justify-between font-bold text-lg bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent border-t pt-2">
              <span>Total:</span>
              <span>${data.total.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

// Simple Template
const SimpleTemplate = ({ data, isSelected }: { data: typeof sampleInvoiceData; isSelected?: boolean }) => (
  <div className={`bg-white border rounded-lg p-6 transition-all ${isSelected ? 'ring-2 ring-green-500 shadow-lg' : 'shadow-sm hover:shadow-md'}`}>
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start pb-4 border-b">
        <div>
          <h1 className="text-xl font-bold text-gray-900">{data.business.name}</h1>
          <p className="text-sm text-gray-600">{data.business.email}</p>
        </div>
        <div className="text-right">
          <h2 className="text-lg font-semibold">Invoice</h2>
          <p className="text-sm text-gray-600">{data.invoice.number}</p>
          <p className="text-sm text-gray-600">{data.invoice.date}</p>
        </div>
      </div>

      {/* Client */}
      <div>
        <h3 className="font-medium text-gray-900 mb-2">Bill To:</h3>
        <div className="text-sm text-gray-600">
          <p>{data.client.name}</p>
          <p>{data.client.address}</p>
          <p>{data.client.city}</p>
        </div>
      </div>

      {/* Items */}
      <div>
        <div className="grid grid-cols-12 gap-4 py-2 text-sm font-medium text-gray-700 border-b">
          <div className="col-span-6">Description</div>
          <div className="col-span-2 text-center">Qty</div>
          <div className="col-span-2 text-right">Rate</div>
          <div className="col-span-2 text-right">Amount</div>
        </div>
        {data.items.map((item, index) => (
          <div key={index} className="grid grid-cols-12 gap-4 py-2 text-sm border-b last:border-b-0">
            <div className="col-span-6">{item.description}</div>
            <div className="col-span-2 text-center">{item.quantity}</div>
            <div className="col-span-2 text-right">${item.rate.toLocaleString()}</div>
            <div className="col-span-2 text-right">${item.amount.toLocaleString()}</div>
          </div>
        ))}
      </div>

      {/* Total */}
      <div className="flex justify-end pt-4 border-t">
        <div className="w-48 space-y-1">
          <div className="flex justify-between text-sm">
            <span>Subtotal:</span>
            <span>${data.subtotal.toLocaleString()}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Tax:</span>
            <span>${data.tax.toLocaleString()}</span>
          </div>
          <div className="flex justify-between font-bold border-t pt-1">
            <span>Total:</span>
            <span>${data.total.toLocaleString()}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
)

// Service-based Template
const ServiceTemplate = ({ data, isSelected }: { data: typeof sampleInvoiceData; isSelected?: boolean }) => (
  <div className={`bg-white border rounded-lg p-6 transition-all ${isSelected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-sm hover:shadow-md'}`}>
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-blue-600">{data.business.name}</h1>
          <p className="text-sm text-gray-600 mt-1">Professional Services</p>
          <div className="mt-2 text-sm text-gray-600">
            <p>{data.business.address}</p>
            <p>{data.business.city}</p>
            <p className="mt-1">{data.business.email} • {data.business.phone}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="bg-blue-50 p-3 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-600">INVOICE</h2>
            <p className="text-sm text-blue-600">{data.invoice.number}</p>
          </div>
        </div>
      </div>

      {/* Service Period & Client */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-2">Service Period</h3>
          <div className="text-sm text-gray-600">
            <p>From: {data.invoice.date}</p>
            <p>To: {data.invoice.dueDate}</p>
            <p className="mt-2 font-medium">Payment Terms: {data.invoice.terms}</p>
          </div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-2">Client</h3>
          <div className="text-sm text-gray-600">
            <p className="font-medium">{data.client.name}</p>
            <p>{data.client.address}</p>
            <p>{data.client.city}</p>
            <p className="mt-1">{data.client.email}</p>
          </div>
        </div>
      </div>

      {/* Services Provided */}
      <div>
        <h3 className="font-semibold text-gray-800 mb-3">Services Provided</h3>
        <div className="bg-blue-50 rounded-lg overflow-hidden">
          <div className="bg-blue-600 text-white px-4 py-2 font-semibold text-sm">
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-6">Service Description</div>
              <div className="col-span-2 text-center">Hours/Qty</div>
              <div className="col-span-2 text-right">Rate</div>
              <div className="col-span-2 text-right">Total</div>
            </div>
          </div>
          {data.items.map((item, index) => (
            <div key={index} className="px-4 py-3 text-sm border-b border-blue-100 last:border-b-0">
              <div className="grid grid-cols-12 gap-4">
                <div className="col-span-6">
                  <p className="font-medium">{item.description}</p>
                  <p className="text-xs text-gray-500 mt-1">Professional service delivery</p>
                </div>
                <div className="col-span-2 text-center">{item.quantity}</div>
                <div className="col-span-2 text-right">${item.rate.toLocaleString()}</div>
                <div className="col-span-2 text-right font-medium">${item.amount.toLocaleString()}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Payment Summary */}
      <div className="flex justify-end">
        <div className="w-80 bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-3">Payment Summary</h4>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Services Subtotal:</span>
              <span>${data.subtotal.toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Tax (8%):</span>
              <span>${data.tax.toLocaleString()}</span>
            </div>
            <div className="flex justify-between font-bold text-lg text-blue-600 border-t pt-2 mt-3">
              <span>Amount Due:</span>
              <span>${data.total.toLocaleString()}</span>
            </div>
          </div>
          <div className="mt-4 p-3 bg-blue-100 rounded text-xs text-blue-700">
            <p className="font-medium">Payment due within 30 days</p>
            <p>Late payments subject to 1.5% monthly service charge</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-sm text-gray-600 pt-4 border-t">
        <p className="font-medium mb-1">Thank you for choosing our professional services!</p>
        <p>{data.notes}</p>
      </div>
    </div>
  </div>
)

// Template definitions
export const templateDefinitions: TemplateMetadata[] = [
  {
    id: 'professional',
    name: 'Professional',
    description: 'Clean and corporate design perfect for established businesses',
    industries: ['Corporate', 'Consulting', 'Finance', 'Legal'],
    features: ['Clean layout', 'Professional typography', 'Corporate branding'],
    icon: <Briefcase className="w-5 h-5" />,
    color: 'blue',
    preview: ProfessionalTemplate
  },
  {
    id: 'modern',
    name: 'Modern',
    description: 'Contemporary styling with gradients and modern elements',
    industries: ['Technology', 'Startups', 'Digital Agencies', 'SaaS'],
    features: ['Gradient backgrounds', 'Modern cards', 'Tech-friendly'],
    icon: <Sparkles className="w-5 h-5" />,
    color: 'indigo',
    preview: ModernTemplate
  },
  {
    id: 'classic',
    name: 'Classic',
    description: 'Traditional business format with timeless appeal',
    industries: ['Manufacturing', 'Traditional Business', 'Healthcare', 'Education'],
    features: ['Traditional layout', 'Professional borders', 'Timeless design'],
    icon: <FileText className="w-5 h-5" />,
    color: 'gray',
    preview: ClassicTemplate
  },
  {
    id: 'creative',
    name: 'Creative',
    description: 'Designer-friendly with colors and creative elements',
    industries: ['Design', 'Marketing', 'Creative Agencies', 'Media'],
    features: ['Colorful gradients', 'Creative elements', 'Eye-catching design'],
    icon: <Palette className="w-5 h-5" />,
    color: 'purple',
    preview: CreativeTemplate
  },
  {
    id: 'simple',
    name: 'Simple',
    description: 'Minimal and basic layout for straightforward invoicing',
    industries: ['Small Business', 'Retail', 'General', 'E-commerce'],
    features: ['Minimal design', 'Easy to read', 'Quick setup'],
    icon: <Eye className="w-5 h-5" />,
    color: 'green',
    preview: SimpleTemplate
  },
  {
    id: 'service',
    name: 'Service-based',
    description: 'Optimized for freelancers and service providers',
    industries: ['Freelancing', 'Consulting', 'Professional Services', 'Contractors'],
    features: ['Service-focused', 'Time tracking', 'Client details'],
    icon: <User className="w-5 h-5" />,
    color: 'blue',
    preview: ServiceTemplate
  }
]

// Main Template Previews Component
interface TemplatePreviewsProps {
  selectedTemplate?: string
  onTemplateSelect?: (templateId: string) => void
  onUseTemplate?: (templateId: string) => void
}

export default function TemplatePreviews({ 
  selectedTemplate, 
  onTemplateSelect, 
  onUseTemplate 
}: TemplatePreviewsProps) {
  const [hoveredTemplate, setHoveredTemplate] = useState<string | null>(null)

  const handleTemplateClick = (templateId: string) => {
    onTemplateSelect?.(templateId)
  }

  const handleUseTemplate = (templateId: string) => {
    onUseTemplate?.(templateId)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Template</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Select from our professionally designed invoice templates. Each template is optimized 
          for different industries and business types.
        </p>
      </div>

      {/* Template Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {templateDefinitions.map((template) => {
          const PreviewComponent = template.preview
          const isSelected = selectedTemplate === template.id
          const isHovered = hoveredTemplate === template.id

          return (
            <div
              key={template.id}
              className="space-y-4"
              onMouseEnter={() => setHoveredTemplate(template.id)}
              onMouseLeave={() => setHoveredTemplate(null)}
            >
              {/* Template Info */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg bg-${template.color}-100 text-${template.color}-600`}>
                    {template.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{template.name}</h3>
                    <p className="text-sm text-gray-600">{template.description}</p>
                  </div>
                </div>
                {isSelected && (
                  <div className="flex items-center gap-1 text-green-600">
                    <Check className="w-4 h-4" />
                    <span className="text-sm font-medium">Selected</span>
                  </div>
                )}
              </div>

              {/* Template Preview */}
              <div
                className={`relative cursor-pointer transition-all duration-200 ${
                  isHovered ? 'scale-[1.02]' : ''
                }`}
                onClick={() => handleTemplateClick(template.id)}
              >
                {/* Scale down for preview */}
                <div className="transform scale-75 origin-top-left w-[133.33%] h-[133.33%] overflow-hidden">
                  <PreviewComponent data={sampleInvoiceData} isSelected={isSelected} />
                </div>

                {/* Overlay on hover */}
                {isHovered && !isSelected && (
                  <div className="absolute inset-0 bg-black/10 backdrop-blur-[1px] flex items-center justify-center rounded-lg">
                    <div className="bg-white rounded-lg p-3 shadow-lg">
                      <p className="text-sm font-medium text-gray-900">Click to select</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Template Metadata */}
              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-1">Best for:</h4>
                  <div className="flex flex-wrap gap-1">
                    {template.industries.map((industry) => (
                      <span
                        key={industry}
                        className={`px-2 py-1 text-xs rounded-full bg-${template.color}-100 text-${template.color}-700`}
                      >
                        {industry}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-1">Features:</h4>
                  <div className="flex flex-wrap gap-1">
                    {template.features.map((feature) => (
                      <span
                        key={feature}
                        className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <button
                    onClick={() => handleTemplateClick(template.id)}
                    className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isSelected
                        ? 'bg-green-100 text-green-700 border border-green-200'
                        : `bg-${template.color}-100 text-${template.color}-700 hover:bg-${template.color}-200`
                    }`}
                  >
                    {isSelected ? 'Selected' : 'Select Template'}
                  </button>
                  <Link
                    href={`/create?template=${template.id}`}
                    className="px-4 py-2 bg-black text-white rounded-lg text-sm font-medium hover:bg-gray-900 transition-colors"
                  >
                    Use Template
                  </Link>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Selected Template Actions */}
      {selectedTemplate && (
        <div className="bg-gray-50 rounded-lg p-6 text-center">
          <h3 className="font-semibold text-gray-900 mb-2">
            {templateDefinitions.find(t => t.id === selectedTemplate)?.name} Template Selected
          </h3>
          <p className="text-gray-600 mb-4">
            Ready to create your invoice with this template?
          </p>
          <div className="flex gap-3 justify-center">
            <button
              onClick={() => onTemplateSelect?.('')}
              className="px-4 py-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              Choose Different Template
            </button>
            <Link
              href={`/create?template=${selectedTemplate}`}
              className="px-6 py-2 bg-black text-white rounded-lg font-medium hover:bg-gray-900 transition-colors"
            >
              Create Invoice
            </Link>
          </div>
        </div>
      )}
    </div>
  )
}