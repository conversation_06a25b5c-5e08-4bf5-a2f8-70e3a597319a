import React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { Mail, Phone, MapPin } from 'lucide-react';
import Link from 'next/link';

export interface FooterLink {
  label: string;
  href: string;
}

export interface FooterSection {
  title: string;
  links: FooterLink[];
}


export interface FooterProps {
  sections?: FooterSection[];
  companyName?: string;
  companyDescription?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
    address?: string;
  };
  bottomText?: string;
  className?: string;
}

const defaultSections: FooterSection[] = [
  {
    title: 'Product',
    links: [
      { label: 'Templates', href: '/templates' },
      { label: 'Create Invoice', href: '/create' },
      { label: 'Pricing', href: '/pricing' },
      { label: 'Dashboard', href: '/dashboard' },
    ],
  },
  {
    title: 'Company',
    links: [
      { label: 'About Us', href: '/about' },
      { label: 'Contact', href: '/contact' },
      { label: 'Support', href: '/support' },
      { label: 'Careers', href: '/careers' },
    ],
  },
  {
    title: 'Resources',
    links: [
      { label: 'My Invoices', href: '/my-invoices' },
      { label: 'Help Center', href: '/help' },
      { label: 'Guides', href: '/guides' },
    ],
  },
  {
    title: 'Legal',
    links: [
      { label: 'Privacy Policy', href: '/privacy' },
      { label: 'Terms of Service', href: '/terms' },
      { label: 'Cookie Policy', href: '/cookies' },
    ],
  },
];


const Footer: React.FC<FooterProps> = ({
  sections = defaultSections,
  companyName = 'Your Company',
  companyDescription = 'Building amazing products for amazing people.',
  contactInfo,
  bottomText = `© ${new Date().getFullYear()} Your Company. All rights reserved.`,
  className,
}) => {
  return (
    <footer className={cn('bg-gray-900 text-gray-300', className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <h3 className="text-white text-2xl font-bold mb-4">{companyName}</h3>
            <p className="text-gray-400 mb-6">{companyDescription}</p>
            
            {contactInfo && (
              <div className="space-y-3">
                {contactInfo.email && (
                  <div className="flex items-center">
                    <Mail className="w-4 h-4 mr-3 text-gray-500" />
                    <a
                      href={`mailto:${contactInfo.email}`}
                      className="hover:text-white transition-colors"
                    >
                      {contactInfo.email}
                    </a>
                  </div>
                )}
                {contactInfo.phone && (
                  <div className="flex items-center">
                    <Phone className="w-4 h-4 mr-3 text-gray-500" />
                    <a
                      href={`tel:${contactInfo.phone}`}
                      className="hover:text-white transition-colors"
                    >
                      {contactInfo.phone}
                    </a>
                  </div>
                )}
                {contactInfo.address && (
                  <div className="flex items-start">
                    <MapPin className="w-4 h-4 mr-3 mt-0.5 text-gray-500" />
                    <span>{contactInfo.address}</span>
                  </div>
                )}
              </div>
            )}

          </div>

          {/* Links Sections */}
          {sections.map((section, index) => (
            <div key={index}>
              <h4 className="text-white font-semibold mb-4">{section.title}</h4>
              <ul className="space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="hover:text-white transition-colors"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">{bottomText}</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="text-sm hover:text-white transition-colors">
                Privacy
              </Link>
              <Link href="/terms" className="text-sm hover:text-white transition-colors">
                Terms
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export { Footer };