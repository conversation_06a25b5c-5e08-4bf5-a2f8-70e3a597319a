import type { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Clock, DollarSign, FileText, Globe, Palette, Users, Zap, Star, TrendingUp, CheckCircle, Download } from 'lucide-react';
import StructuredData from '@/components/StructuredData';

export const metadata: Metadata = {
  title: 'Free Freelancer Invoice Generator | Professional Invoice Templates',
  description: 'Create professional freelancer invoices in minutes. Specialized templates for writers, developers, designers, and consultants. Free PDF download.',
  keywords: 'freelancer invoice generator, freelance invoice template, writer invoice, developer invoice, designer invoice, consultant invoice, freelance billing, hourly rate invoice',
  alternates: {
    canonical: 'https://templateinvoices.com/invoice-generator/freelancer',
  },
  openGraph: {
    title: 'Free Freelancer Invoice Generator | Professional Templates',
    description: 'Create professional invoices tailored for freelancers. Perfect for writers, developers, designers, and consultants.',
    url: 'https://templateinvoices.com/invoice-generator/freelancer',
    images: [{ url: '/og-freelancer-invoice.jpg', width: 1200, height: 630 }],
  },
};

export default function FreelancerInvoicePage() {
  return (
    <>
      <StructuredData 
        type="industry" 
        pageData={{ 
          industry: 'Freelancer',
          title: 'Freelancer Invoice Generator',
          description: 'Create professional freelancer invoices in minutes with specialized templates'
        }} 
      />
      {/* Hero Section */}
      <section className='bg-gradient-to-br from-purple-50 via-white to-blue-50 py-16'>
        <div className='container mx-auto px-4'>
          <div className='max-w-4xl mx-auto text-center'>
            <h1 className='text-4xl md:text-5xl font-bold mb-6'>
              Free Freelancer Invoice Generator
            </h1>
            <p className='text-xl text-gray-600 mb-8'>
              Create professional invoices tailored specifically for freelance work. 
              Perfect for writers, developers, designers, consultants, and independent contractors.
            </p>
            
            <div className='flex flex-col sm:flex-row gap-4 justify-center mb-12'>
              <Link 
                href='/create?template=freelancer'
                className='bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center'
              >
                Create Freelancer Invoice <ArrowRight className='ml-2 h-5 w-5' />
              </Link>
              <Link 
                href='/templates#freelancer'
                className='border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-50 transition-colors'
              >
                View Freelance Templates
              </Link>
            </div>
            
            {/* Trust Indicators */}
            <div className='flex flex-wrap justify-center items-center gap-8 text-sm text-gray-500'>
              <div className='flex items-center gap-2'>
                <Users className='h-4 w-4' />
                <span>Used by 5,000+ freelancers</span>
              </div>
              <div className='flex items-center gap-2'>
                <Star className='h-4 w-4' />
                <span>4.9/5 rating</span>
              </div>
              <div className='flex items-center gap-2'>
                <Zap className='h-4 w-4' />
                <span>Quick & professional</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className='py-16 bg-white'>
        <div className='container mx-auto px-4'>
          <div className='max-w-5xl mx-auto'>
            <h2 className='text-3xl font-bold text-center mb-12'>
              Freelancer Invoice Features Built for Your Success
            </h2>
            
            <div className='grid md:grid-cols-3 gap-8'>
              <div className='text-center'>
                <div className='bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <Clock className='h-8 w-8 text-purple-600' />
                </div>
                <h3 className='text-xl font-semibold mb-3'>Hourly & Project Billing</h3>
                <p className='text-gray-600'>
                  Track billable hours or charge fixed project fees. Calculate totals automatically with built-in rate calculations.
                </p>
              </div>
              
              <div className='text-center'>
                <div className='bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <DollarSign className='h-8 w-8 text-green-600' />
                </div>
                <h3 className='text-xl font-semibold mb-3'>Multiple Currencies</h3>
                <p className='text-gray-600'>
                  Invoice international clients in their preferred currency. Support for USD, EUR, GBP, and more.
                </p>
              </div>
              
              <div className='text-center'>
                <div className='bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <FileText className='h-8 w-8 text-blue-600' />
                </div>
                <h3 className='text-xl font-semibold mb-3'>Professional Templates</h3>
                <p className='text-gray-600'>
                  Industry-specific templates for writers, developers, designers, consultants, and more freelance professionals.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className='py-16 bg-gray-50'>
        <div className='container mx-auto px-4'>
          <div className='max-w-4xl mx-auto'>
            <div className='bg-white p-8 rounded-lg shadow-lg'>
              <h2 className='text-2xl font-semibold mb-6'>
                Everything Freelancers Need for Professional Invoicing
              </h2>
              <div className='grid md:grid-cols-2 gap-6'>
                <div className='flex items-start gap-3'>
                  <CheckCircle className='h-5 w-5 text-green-500 flex-shrink-0 mt-1' />
                  <div>
                    <h4 className='font-semibold mb-1'>Hourly Rate Tracking</h4>
                    <p className='text-gray-600 text-sm'>Automatically calculate totals based on hours worked and hourly rate</p>
                  </div>
                </div>
                
                <div className='flex items-start gap-3'>
                  <CheckCircle className='h-5 w-5 text-green-500 flex-shrink-0 mt-1' />
                  <div>
                    <h4 className='font-semibold mb-1'>Project Milestones</h4>
                    <p className='text-gray-600 text-sm'>Bill for completed project phases or milestones</p>
                  </div>
                </div>
                
                <div className='flex items-start gap-3'>
                  <CheckCircle className='h-5 w-5 text-green-500 flex-shrink-0 mt-1' />
                  <div>
                    <h4 className='font-semibold mb-1'>Expense Tracking</h4>
                    <p className='text-gray-600 text-sm'>Add billable expenses and reimbursements to invoices</p>
                  </div>
                </div>
                
                <div className='flex items-start gap-3'>
                  <CheckCircle className='h-5 w-5 text-green-500 flex-shrink-0 mt-1' />
                  <div>
                    <h4 className='font-semibold mb-1'>Tax Calculations</h4>
                    <p className='text-gray-600 text-sm'>Automatic tax calculations for different regions</p>
                  </div>
                </div>
                
                <div className='flex items-start gap-3'>
                  <CheckCircle className='h-5 w-5 text-green-500 flex-shrink-0 mt-1' />
                  <div>
                    <h4 className='font-semibold mb-1'>Payment Terms</h4>
                    <p className='text-gray-600 text-sm'>Set custom payment terms and late fee policies</p>
                  </div>
                </div>
                
                <div className='flex items-start gap-3'>
                  <CheckCircle className='h-5 w-5 text-green-500 flex-shrink-0 mt-1' />
                  <div>
                    <h4 className='font-semibold mb-1'>Client Management</h4>
                    <p className='text-gray-600 text-sm'>Save client details for quick invoice creation</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Templates by Profession */}
      <section className='py-16 bg-white'>
        <div className='container mx-auto px-4'>
          <div className='max-w-5xl mx-auto'>
            <h2 className='text-3xl font-bold text-center mb-4'>
              Freelancer Invoice Templates by Profession
            </h2>
            <p className='text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto'>
              Choose a template designed specifically for your freelance profession
            </p>
            
            <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-6'>
              <Link href='/create?template=writer' className='group'>
                <div className='bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer group-hover:bg-blue-50'>
                  <div className='text-4xl mb-3'>✍️</div>
                  <h3 className='font-semibold text-gray-800 mb-2'>Freelance Writers</h3>
                  <p className='text-sm text-gray-600'>Per-word rates, article tracking, revision rounds</p>
                </div>
              </Link>
              
              <Link href='/create?template=developer' className='group'>
                <div className='bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer group-hover:bg-blue-50'>
                  <div className='text-4xl mb-3'>💻</div>
                  <h3 className='font-semibold text-gray-800 mb-2'>Web Developers</h3>
                  <p className='text-sm text-gray-600'>Hourly coding, project phases, maintenance</p>
                </div>
              </Link>
              
              <Link href='/create?template=designer' className='group'>
                <div className='bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer group-hover:bg-blue-50'>
                  <div className='text-4xl mb-3'>🎨</div>
                  <h3 className='font-semibold text-gray-800 mb-2'>Graphic Designers</h3>
                  <p className='text-sm text-gray-600'>Design concepts, revisions, usage rights</p>
                </div>
              </Link>
              
              <Link href='/create?template=consultant' className='group'>
                <div className='bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer group-hover:bg-blue-50'>
                  <div className='text-4xl mb-3'>💼</div>
                  <h3 className='font-semibold text-gray-800 mb-2'>Consultants</h3>
                  <p className='text-sm text-gray-600'>Consulting hours, reports, strategic planning</p>
                </div>
              </Link>
              
              <Link href='/create?template=photographer' className='group'>
                <div className='bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer group-hover:bg-blue-50'>
                  <div className='text-4xl mb-3'>📸</div>
                  <h3 className='font-semibold text-gray-800 mb-2'>Photographers</h3>
                  <p className='text-sm text-gray-600'>Shoots, editing, image licensing, prints</p>
                </div>
              </Link>
              
              <Link href='/create?template=marketer' className='group'>
                <div className='bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer group-hover:bg-blue-50'>
                  <div className='text-4xl mb-3'>📱</div>
                  <h3 className='font-semibold text-gray-800 mb-2'>Digital Marketers</h3>
                  <p className='text-sm text-gray-600'>Campaign management, ads, analytics</p>
                </div>
              </Link>
              
              <Link href='/create?template=videographer' className='group'>
                <div className='bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer group-hover:bg-blue-50'>
                  <div className='text-4xl mb-3'>🎥</div>
                  <h3 className='font-semibold text-gray-800 mb-2'>Video Editors</h3>
                  <p className='text-sm text-gray-600'>Video production, editing hours, formats</p>
                </div>
              </Link>
              
              <Link href='/create?template=virtual-assistant' className='group'>
                <div className='bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow cursor-pointer group-hover:bg-blue-50'>
                  <div className='text-4xl mb-3'>🤝</div>
                  <h3 className='font-semibold text-gray-800 mb-2'>Virtual Assistants</h3>
                  <p className='text-sm text-gray-600'>Admin tasks, hourly support, packages</p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className='py-16 bg-gray-50'>
        <div className='container mx-auto px-4'>
          <div className='max-w-4xl mx-auto'>
            <h2 className='text-3xl font-bold text-center mb-12'>
              How to Create a Freelancer Invoice
            </h2>
            
            <div className='space-y-8'>
              <div className='flex gap-4'>
                <div className='bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 font-semibold'>
                  1
                </div>
                <div>
                  <h3 className='text-xl font-semibold mb-2'>Choose Your Template</h3>
                  <p className='text-gray-600'>
                    Select a template designed for your freelance profession. Each template includes 
                    industry-specific fields and professional formatting.
                  </p>
                </div>
              </div>
              
              <div className='flex gap-4'>
                <div className='bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 font-semibold'>
                  2
                </div>
                <div>
                  <h3 className='text-xl font-semibold mb-2'>Enter Your Details</h3>
                  <p className='text-gray-600'>
                    Add your business information, client details, and services provided. Include 
                    hourly rates, project fees, or milestone payments.
                  </p>
                </div>
              </div>
              
              <div className='flex gap-4'>
                <div className='bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 font-semibold'>
                  3
                </div>
                <div>
                  <h3 className='text-xl font-semibold mb-2'>Customize & Brand</h3>
                  <p className='text-gray-600'>
                    Add your logo, adjust colors to match your brand, and include payment terms 
                    specific to your freelance business.
                  </p>
                </div>
              </div>
              
              <div className='flex gap-4'>
                <div className='bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 font-semibold'>
                  4
                </div>
                <div>
                  <h3 className='text-xl font-semibold mb-2'>Download or Send</h3>
                  <p className='text-gray-600'>
                    Download your professional invoice as a PDF or send it directly to your client 
                    via email. Track payment status and send reminders.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className='py-16 bg-white'>
        <div className='container mx-auto px-4'>
          <div className='max-w-4xl mx-auto'>
            <h2 className='text-3xl font-bold text-center mb-12'>
              Freelancer Invoice FAQs
            </h2>
            
            <div className='space-y-6'>
              <div className='bg-gray-50 p-6 rounded-lg'>
                <h3 className='text-lg font-semibold mb-2'>
                  What should I include in a freelance invoice?
                </h3>
                <p className='text-gray-600'>
                  A professional freelance invoice should include: your business name and contact info, 
                  client details, unique invoice number, service description, dates of service, 
                  hourly rate or project fee, total amount due, payment terms, and payment methods accepted.
                </p>
              </div>
              
              <div className='bg-gray-50 p-6 rounded-lg'>
                <h3 className='text-lg font-semibold mb-2'>
                  How do I calculate hourly rates on an invoice?
                </h3>
                <p className='text-gray-600'>
                  List each task with the number of hours worked and your hourly rate. Our invoice 
                  generator automatically calculates the total for each line item and the overall 
                  invoice total, including any taxes or fees.
                </p>
              </div>
              
              <div className='bg-gray-50 p-6 rounded-lg'>
                <h3 className='text-lg font-semibold mb-2'>
                  Can I add expenses to my freelance invoice?
                </h3>
                <p className='text-gray-600'>
                  Yes! You can add billable expenses like travel, materials, software licenses, or 
                  other project-related costs. Each expense can be itemized with descriptions and amounts.
                </p>
              </div>
              
              <div className='bg-gray-50 p-6 rounded-lg'>
                <h3 className='text-lg font-semibold mb-2'>
                  What payment terms should freelancers use?
                </h3>
                <p className='text-gray-600'>
                  Common payment terms for freelancers include Net 15 (payment due within 15 days), 
                  Net 30, or Due Upon Receipt. You can also request partial upfront payment or 
                  milestone-based payments for larger projects.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className='py-16 bg-blue-600'>
        <div className='container mx-auto px-4 text-center'>
          <h2 className='text-3xl font-bold text-white mb-4'>
            Start Creating Professional Freelance Invoices
          </h2>
          <p className='text-xl text-blue-100 mb-8 max-w-2xl mx-auto'>
            Join thousands of freelancers who use our invoice generator to get paid faster 
            and look more professional.
          </p>
          <div className='flex flex-col sm:flex-row gap-4 justify-center'>
            <Link 
              href='/create?template=freelancer'
              className='bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center'
            >
              Create Free Invoice <ArrowRight className='ml-2 h-5 w-5' />
            </Link>
            <Link 
              href='/pricing'
              className='border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white/10 transition-colors'
            >
              View Pro Features
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}