export interface InvoiceTemplate {
  id: string;
  name: string;
  industry: string;
  description: string;
  previewImage: string;
  htmlTemplate: string;
  fields: string[];
  styling: {
    primaryColor: string;
    fontFamily: string;
    layout: string;
  };
}

export async function getAllTemplates() {
  return invoiceTemplates;
}

export const invoiceTemplates: InvoiceTemplate[] = [
  {
    id: 'professional',
    name: 'Professional',
    industry: 'General Business',
    description: 'Clean corporate design perfect for established businesses',
    previewImage: '/templates/professional-preview.png',
    fields: ['companyLogo', 'businessInfo', 'clientInfo', 'invoiceNumber', 'date', 'dueDate', 'lineItems', 'subtotal', 'tax', 'total', 'notes'],
    styling: {
      primaryColor: '#2563eb',
      fontFamily: 'Inter, sans-serif',
      layout: 'two-column'
    },
    htmlTemplate: `
      <div style="max-width: 800px; margin: 0 auto; padding: 40px; font-family: Inter, sans-serif; color: #1f2937;">
        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 40px;">
          <div>
            <div style="width: 120px; height: 60px; background: #f3f4f6; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
              <span style="color: #6b7280; font-size: 12px;">LOGO</span>
            </div>
            <h1 style="color: #2563eb; font-size: 32px; font-weight: 700; margin: 0;">INVOICE</h1>
          </div>
          <div style="text-align: right;">
            <div style="background: #2563eb; color: white; padding: 8px 16px; border-radius: 6px; font-weight: 600; margin-bottom: 16px;">
              #{{invoiceNumber}}
            </div>
            <p style="margin: 4px 0; color: #6b7280;">Date: {{date}}</p>
            <p style="margin: 4px 0; color: #6b7280;">Due: {{dueDate}}</p>
          </div>
        </div>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px;">
          <div>
            <h3 style="color: #374151; font-size: 14px; font-weight: 600; text-transform: uppercase; margin-bottom: 12px; letter-spacing: 0.5px;">From</h3>
            <div style="color: #1f2937;">
              <p style="font-weight: 600; margin: 0 0 8px 0;">{{businessName}}</p>
              <p style="margin: 2px 0; color: #6b7280;">{{businessAddress}}</p>
              <p style="margin: 2px 0; color: #6b7280;">{{businessPhone}}</p>
              <p style="margin: 2px 0; color: #6b7280;">{{businessEmail}}</p>
            </div>
          </div>
          <div>
            <h3 style="color: #374151; font-size: 14px; font-weight: 600; text-transform: uppercase; margin-bottom: 12px; letter-spacing: 0.5px;">To</h3>
            <div style="color: #1f2937;">
              <p style="font-weight: 600; margin: 0 0 8px 0;">{{clientName}}</p>
              <p style="margin: 2px 0; color: #6b7280;">{{clientAddress}}</p>
              <p style="margin: 2px 0; color: #6b7280;">{{clientPhone}}</p>
              <p style="margin: 2px 0; color: #6b7280;">{{clientEmail}}</p>
            </div>
          </div>
        </div>
        
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
          <thead>
            <tr style="background: #f9fafb; border-bottom: 2px solid #e5e7eb;">
              <th style="text-align: left; padding: 16px; font-weight: 600; color: #374151;">Description</th>
              <th style="text-align: center; padding: 16px; font-weight: 600; color: #374151;">Qty</th>
              <th style="text-align: right; padding: 16px; font-weight: 600; color: #374151;">Rate</th>
              <th style="text-align: right; padding: 16px; font-weight: 600; color: #374151;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {{#lineItems}}
            <tr style="border-bottom: 1px solid #e5e7eb;">
              <td style="padding: 16px; color: #1f2937;">{{description}}</td>
              <td style="padding: 16px; text-align: center; color: #6b7280;">{{quantity}}</td>
              <td style="padding: 16px; text-align: right; color: #6b7280;">\${{rate}}</td>
              <td style="padding: 16px; text-align: right; font-weight: 600; color: #1f2937;">\${{amount}}</td>
            </tr>
            {{/lineItems}}
          </tbody>
        </table>
        
        <div style="display: flex; justify-content: flex-end;">
          <div style="width: 300px;">
            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
              <span style="color: #6b7280;">Subtotal</span>
              <span style="color: #1f2937; font-weight: 600;">\${{subtotal}}</span>
            </div>
            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
              <span style="color: #6b7280;">Tax</span>
              <span style="color: #1f2937; font-weight: 600;">\${{tax}}</span>
            </div>
            <div style="display: flex; justify-content: space-between; padding: 16px 0; border-bottom: 2px solid #2563eb;">
              <span style="color: #1f2937; font-weight: 700; font-size: 18px;">Total</span>
              <span style="color: #2563eb; font-weight: 700; font-size: 18px;">\${{total}}</span>
            </div>
          </div>
        </div>
        
        {{#notes}}
        <div style="margin-top: 40px; padding: 20px; background: #f9fafb; border-radius: 8px;">
          <h4 style="color: #374151; margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">Notes</h4>
          <p style="color: #6b7280; margin: 0; line-height: 1.6;">{{notes}}</p>
        </div>
        {{/notes}}
      </div>
    `
  },
  
  {
    id: 'modern',
    name: 'Modern',
    industry: 'Creative',
    description: 'Contemporary design with bold typography and vibrant colors',
    previewImage: '/templates/modern-preview.png',
    fields: ['companyLogo', 'businessInfo', 'clientInfo', 'invoiceNumber', 'date', 'dueDate', 'lineItems', 'subtotal', 'tax', 'total', 'notes'],
    styling: {
      primaryColor: '#7c3aed',
      fontFamily: 'Inter, sans-serif',
      layout: 'split-design'
    },
    htmlTemplate: `
      <div style="max-width: 800px; margin: 0 auto; font-family: Inter, sans-serif; background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%); min-height: 1000px;">
        <div style="background: white; margin: 40px; padding: 40px; border-radius: 16px; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 3px solid #7c3aed;">
            <div>
              <h1 style="color: #7c3aed; font-size: 36px; font-weight: 800; margin: 0; letter-spacing: -1px;">INVOICE</h1>
              <p style="color: #6b7280; margin: 8px 0 0 0; font-size: 16px;">Professional Services</p>
            </div>
            <div style="text-align: right;">
              <div style="background: linear-gradient(135deg, #7c3aed, #a855f7); color: white; padding: 12px 20px; border-radius: 12px; font-weight: 700; font-size: 18px; margin-bottom: 16px;">
                #{{invoiceNumber}}
              </div>
              <p style="margin: 4px 0; color: #374151; font-weight: 500;">{{date}}</p>
              <p style="margin: 4px 0; color: #ef4444; font-weight: 600;">Due: {{dueDate}}</p>
            </div>
          </div>
          
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px;">
            <div style="background: #f8fafc; padding: 24px; border-radius: 12px; border-left: 4px solid #7c3aed;">
              <h3 style="color: #7c3aed; font-size: 14px; font-weight: 700; text-transform: uppercase; margin-bottom: 16px; letter-spacing: 1px;">Service Provider</h3>
              <div style="color: #1f2937;">
                <p style="font-weight: 700; margin: 0 0 12px 0; font-size: 18px;">{{businessName}}</p>
                <p style="margin: 4px 0; color: #4b5563;">{{businessAddress}}</p>
                <p style="margin: 4px 0; color: #4b5563;">{{businessPhone}}</p>
                <p style="margin: 4px 0; color: #7c3aed; font-weight: 600;">{{businessEmail}}</p>
              </div>
            </div>
            <div style="background: #f8fafc; padding: 24px; border-radius: 12px; border-left: 4px solid #10b981;">
              <h3 style="color: #10b981; font-size: 14px; font-weight: 700; text-transform: uppercase; margin-bottom: 16px; letter-spacing: 1px;">Client</h3>
              <div style="color: #1f2937;">
                <p style="font-weight: 700; margin: 0 0 12px 0; font-size: 18px;">{{clientName}}</p>
                <p style="margin: 4px 0; color: #4b5563;">{{clientAddress}}</p>
                <p style="margin: 4px 0; color: #4b5563;">{{clientPhone}}</p>
                <p style="margin: 4px 0; color: #10b981; font-weight: 600;">{{clientEmail}}</p>
              </div>
            </div>
          </div>
          
          <div style="background: #1f2937; color: white; padding: 24px; border-radius: 12px; margin-bottom: 30px;">
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr>
                  <th style="text-align: left; padding: 12px 0; font-weight: 700; color: #f3f4f6; border-bottom: 2px solid #374151;">Service</th>
                  <th style="text-align: center; padding: 12px 0; font-weight: 700; color: #f3f4f6; border-bottom: 2px solid #374151;">Qty</th>
                  <th style="text-align: right; padding: 12px 0; font-weight: 700; color: #f3f4f6; border-bottom: 2px solid #374151;">Rate</th>
                  <th style="text-align: right; padding: 12px 0; font-weight: 700; color: #f3f4f6; border-bottom: 2px solid #374151;">Total</th>
                </tr>
              </thead>
              <tbody>
                {{#lineItems}}
                <tr>
                  <td style="padding: 16px 0; color: #e5e7eb; border-bottom: 1px solid #374151;">{{description}}</td>
                  <td style="padding: 16px 0; text-align: center; color: #9ca3af;">{{quantity}}</td>
                  <td style="padding: 16px 0; text-align: right; color: #9ca3af;">\${{rate}}</td>
                  <td style="padding: 16px 0; text-align: right; font-weight: 700; color: #f3f4f6;">\${{amount}}</td>
                </tr>
                {{/lineItems}}
              </tbody>
            </table>
          </div>
          
          <div style="display: flex; justify-content: flex-end;">
            <div style="background: linear-gradient(135deg, #f8fafc, #e2e8f0); padding: 24px; border-radius: 12px; width: 320px;">
              <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #cbd5e1;">
                <span style="color: #64748b; font-weight: 500;">Subtotal</span>
                <span style="color: #1f2937; font-weight: 600;">\${{subtotal}}</span>
              </div>
              <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #cbd5e1;">
                <span style="color: #64748b; font-weight: 500;">Tax</span>
                <span style="color: #1f2937; font-weight: 600;">\${{tax}}</span>
              </div>
              <div style="display: flex; justify-content: space-between; padding: 16px 0; background: linear-gradient(135deg, #7c3aed, #a855f7); margin: 16px -24px -24px -24px; padding: 20px 24px; border-radius: 0 0 12px 12px; color: white;">
                <span style="font-weight: 800; font-size: 20px;">TOTAL</span>
                <span style="font-weight: 800; font-size: 20px;">\${{total}}</span>
              </div>
            </div>
          </div>
          
          {{#notes}}
          <div style="margin-top: 40px; padding: 24px; background: linear-gradient(135deg, #f0f9ff, #e0f2fe); border-radius: 12px; border-left: 4px solid #0ea5e9;">
            <h4 style="color: #0c4a6e; margin: 0 0 12px 0; font-size: 16px; font-weight: 700;">Additional Notes</h4>
            <p style="color: #0369a1; margin: 0; line-height: 1.6; font-weight: 500;">{{notes}}</p>
          </div>
          {{/notes}}
        </div>
      </div>
    `
  },
  
  {
    id: 'freelancer',
    name: 'Freelancer',
    industry: 'Freelance',
    description: 'Simple, clean design perfect for independent contractors',
    previewImage: '/templates/freelancer-preview.png',
    fields: ['businessInfo', 'clientInfo', 'invoiceNumber', 'date', 'dueDate', 'lineItems', 'subtotal', 'tax', 'total', 'paymentTerms'],
    styling: {
      primaryColor: '#059669',
      fontFamily: 'Inter, sans-serif',
      layout: 'minimal'
    },
    htmlTemplate: `
      <div style="max-width: 700px; margin: 0 auto; padding: 40px 20px; font-family: Inter, sans-serif; color: #1f2937; line-height: 1.6;">
        <div style="text-align: center; margin-bottom: 40px; padding-bottom: 30px; border-bottom: 2px solid #059669;">
          <h1 style="color: #059669; font-size: 28px; font-weight: 600; margin: 0; letter-spacing: 2px;">INVOICE</h1>
          <p style="color: #6b7280; margin: 8px 0 0 0; font-size: 14px;">Freelance Services</p>
        </div>
        
        <div style="display: flex; justify-content: space-between; margin-bottom: 40px;">
          <div style="flex: 1;">
            <div style="margin-bottom: 30px;">
              <h3 style="color: #059669; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 12px; letter-spacing: 1px;">From</h3>
              <div>
                <p style="font-weight: 600; margin: 0 0 6px 0; font-size: 16px;">{{businessName}}</p>
                <p style="margin: 2px 0; color: #6b7280; font-size: 14px;">{{businessAddress}}</p>
                <p style="margin: 2px 0; color: #6b7280; font-size: 14px;">{{businessPhone}}</p>
                <p style="margin: 2px 0; color: #059669; font-weight: 500; font-size: 14px;">{{businessEmail}}</p>
              </div>
            </div>
            
            <div>
              <h3 style="color: #059669; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 12px; letter-spacing: 1px;">To</h3>
              <div>
                <p style="font-weight: 600; margin: 0 0 6px 0; font-size: 16px;">{{clientName}}</p>
                <p style="margin: 2px 0; color: #6b7280; font-size: 14px;">{{clientAddress}}</p>
                <p style="margin: 2px 0; color: #6b7280; font-size: 14px;">{{clientPhone}}</p>
                <p style="margin: 2px 0; color: #059669; font-weight: 500; font-size: 14px;">{{clientEmail}}</p>
              </div>
            </div>
          </div>
          
          <div style="text-align: right; padding-left: 40px;">
            <div style="background: #059669; color: white; padding: 12px 20px; border-radius: 8px; margin-bottom: 20px;">
              <p style="margin: 0; font-weight: 600; font-size: 16px;">#{{invoiceNumber}}</p>
            </div>
            <div style="background: #f0fdf4; padding: 16px; border-radius: 8px; border: 1px solid #bbf7d0;">
              <p style="margin: 0 0 8px 0; color: #374151; font-size: 14px;"><strong>Date:</strong> {{date}}</p>
              <p style="margin: 0; color: #dc2626; font-size: 14px; font-weight: 600;"><strong>Due:</strong> {{dueDate}}</p>
            </div>
          </div>
        </div>
        
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
          <thead>
            <tr style="background: #f9fafb;">
              <th style="text-align: left; padding: 16px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Description</th>
              <th style="text-align: center; padding: 16px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; width: 80px;">Hours</th>
              <th style="text-align: right; padding: 16px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; width: 100px;">Rate</th>
              <th style="text-align: right; padding: 16px; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; width: 100px;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {{#lineItems}}
            <tr style="border-bottom: 1px solid #f3f4f6;">
              <td style="padding: 16px; color: #1f2937;">{{description}}</td>
              <td style="padding: 16px; text-align: center; color: #6b7280;">{{quantity}}</td>
              <td style="padding: 16px; text-align: right; color: #6b7280;">\${{rate}}</td>
              <td style="padding: 16px; text-align: right; font-weight: 600; color: #1f2937;">\${{amount}}</td>
            </tr>
            {{/lineItems}}
          </tbody>
        </table>
        
        <div style="display: flex; justify-content: flex-end; margin-bottom: 30px;">
          <div style="width: 280px; background: #f9fafb; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
              <span style="color: #6b7280;">Subtotal</span>
              <span style="color: #1f2937; font-weight: 600;">\${{subtotal}}</span>
            </div>
            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
              <span style="color: #6b7280;">Tax</span>
              <span style="color: #1f2937; font-weight: 600;">\${{tax}}</span>
            </div>
            <div style="display: flex; justify-content: space-between; padding: 12px 0; margin-top: 8px; background: #059669; color: white; margin: 12px -20px -20px -20px; padding: 16px 20px; border-radius: 0 0 8px 8px;">
              <span style="font-weight: 700; font-size: 16px;">Total</span>
              <span style="font-weight: 700; font-size: 16px;">\${{total}}</span>
            </div>
          </div>
        </div>
        
        <div style="text-align: center; padding: 20px; background: #f0fdf4; border-radius: 8px; border: 1px solid #bbf7d0; margin-bottom: 20px;">
          <p style="margin: 0; color: #166534; font-weight: 600; font-size: 14px;">Payment due within 30 days. Thank you for your business!</p>
        </div>
        
        {{#notes}}
        <div style="padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #059669;">
          <h4 style="color: #374151; margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">Notes</h4>
          <p style="color: #6b7280; margin: 0; font-size: 14px;">{{notes}}</p>
        </div>
        {{/notes}}
      </div>
    `
  },
  
  {
    id: 'photography',
    name: 'Photography',
    industry: 'Photography',
    description: 'Elegant design tailored for photographers and visual artists',
    previewImage: '/templates/photography-preview.png',
    fields: ['companyLogo', 'businessInfo', 'clientInfo', 'invoiceNumber', 'date', 'dueDate', 'lineItems', 'subtotal', 'tax', 'total', 'notes'],
    styling: {
      primaryColor: '#dc2626',
      fontFamily: 'Inter, sans-serif',
      layout: 'artistic'
    },
    htmlTemplate: `
      <div style="max-width: 800px; margin: 0 auto; padding: 40px; font-family: Inter, sans-serif; background: #fafafa;">
        <div style="background: white; padding: 50px; border-radius: 12px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
          <div style="text-align: center; margin-bottom: 50px;">
            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #dc2626, #ef4444); border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 20px;">
              <span style="color: white; font-size: 24px; font-weight: 700;">📷</span>
            </div>
            <h1 style="color: #1f2937; font-size: 32px; font-weight: 300; margin: 0; letter-spacing: 3px;">INVOICE</h1>
            <div style="width: 60px; height: 2px; background: #dc2626; margin: 20px auto;"></div>
          </div>
          
          <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 50px;">
            <div style="flex: 1;">
              <h2 style="color: #dc2626; font-size: 18px; font-weight: 600; margin: 0 0 20px 0;">{{businessName}}</h2>
              <div style="color: #6b7280; line-height: 1.8;">
                <p style="margin: 0;">{{businessAddress}}</p>
                <p style="margin: 0;">{{businessPhone}}</p>
                <p style="margin: 0; color: #dc2626; font-weight: 500;">{{businessEmail}}</p>
              </div>
            </div>
            
            <div style="text-align: right;">
              <div style="background: linear-gradient(135deg, #1f2937, #374151); color: white; padding: 16px 24px; border-radius: 8px; margin-bottom: 20px;">
                <p style="margin: 0; font-size: 18px; font-weight: 600;">#{{invoiceNumber}}</p>
              </div>
              <div style="color: #6b7280;">
                <p style="margin: 0 0 8px 0;"><strong>Date:</strong> {{date}}</p>
                <p style="margin: 0; color: #dc2626; font-weight: 600;"><strong>Due:</strong> {{dueDate}}</p>
              </div>
            </div>
          </div>
          
          <div style="background: #f8fafc; padding: 30px; border-radius: 8px; margin-bottom: 30px;">
            <h3 style="color: #1f2937; font-size: 16px; font-weight: 600; margin: 0 0 16px 0;">Bill To:</h3>
            <div style="color: #374151;">
              <p style="font-weight: 600; margin: 0 0 8px 0; font-size: 18px;">{{clientName}}</p>
              <p style="margin: 0 0 4px 0;">{{clientAddress}}</p>
              <p style="margin: 0 0 4px 0;">{{clientPhone}}</p>
              <p style="margin: 0; color: #dc2626; font-weight: 500;">{{clientEmail}}</p>
            </div>
          </div>
          
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
            <thead>
              <tr style="background: #1f2937; color: white;">
                <th style="text-align: left; padding: 20px; font-weight: 600; border-radius: 8px 0 0 0;">Photography Service</th>
                <th style="text-align: center; padding: 20px; font-weight: 600;">Quantity</th>
                <th style="text-align: right; padding: 20px; font-weight: 600;">Rate</th>
                <th style="text-align: right; padding: 20px; font-weight: 600; border-radius: 0 8px 0 0;">Total</th>
              </tr>
            </thead>
            <tbody>
              {{#lineItems}}
              <tr style="background: #fafafa; border-bottom: 1px solid #e5e7eb;">
                <td style="padding: 20px; color: #1f2937; font-weight: 500;">{{description}}</td>
                <td style="padding: 20px; text-align: center; color: #6b7280;">{{quantity}}</td>
                <td style="padding: 20px; text-align: right; color: #6b7280;">\${{rate}}</td>
                <td style="padding: 20px; text-align: right; font-weight: 600; color: #1f2937;">\${{amount}}</td>
              </tr>
              {{/lineItems}}
            </tbody>
          </table>
          
          <div style="display: flex; justify-content: flex-end;">
            <div style="width: 350px;">
              <div style="padding: 20px; background: #f8fafc; border-radius: 8px;">
                <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
                  <span style="color: #6b7280;">Subtotal</span>
                  <span style="color: #1f2937; font-weight: 600;">\${{subtotal}}</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
                  <span style="color: #6b7280;">Tax</span>
                  <span style="color: #1f2937; font-weight: 600;">\${{tax}}</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 16px 0; background: linear-gradient(135deg, #dc2626, #ef4444); color: white; margin: 16px -20px -20px -20px; padding: 20px; border-radius: 0 0 8px 8px;">
                  <span style="font-weight: 700; font-size: 18px;">TOTAL</span>
                  <span style="font-weight: 700; font-size: 18px;">\${{total}}</span>
                </div>
              </div>
            </div>
          </div>
          
          {{#notes}}
          <div style="margin-top: 40px; text-align: center;">
            <div style="background: #fef2f2; padding: 24px; border-radius: 8px; border: 1px solid #fecaca;">
              <h4 style="color: #991b1b; margin: 0 0 12px 0; font-size: 16px; font-weight: 600;">Special Notes</h4>
              <p style="color: #7f1d1d; margin: 0; font-style: italic;">{{notes}}</p>
            </div>
          </div>
          {{/notes}}
          
          <div style="text-align: center; margin-top: 40px; padding-top: 30px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; margin: 0; font-size: 14px;">Thank you for choosing our photography services!</p>
          </div>
        </div>
      </div>
    `
  },
  
  {
    id: 'consulting',
    name: 'Consulting',
    industry: 'Consulting',
    description: 'Professional template designed for consultants and advisors',
    previewImage: '/templates/consulting-preview.png',
    fields: ['companyLogo', 'businessInfo', 'clientInfo', 'invoiceNumber', 'date', 'dueDate', 'lineItems', 'subtotal', 'tax', 'total', 'notes', 'paymentTerms'],
    styling: {
      primaryColor: '#1e40af',
      fontFamily: 'Inter, sans-serif',
      layout: 'executive'
    },
    htmlTemplate: `
      <div style="max-width: 800px; margin: 0 auto; padding: 40px; font-family: Inter, sans-serif; color: #1f2937;">
        <div style="border: 2px solid #1e40af; border-radius: 12px; overflow: hidden;">
          <div style="background: linear-gradient(135deg, #1e40af, #3b82f6); color: white; padding: 40px; text-align: center;">
            <h1 style="font-size: 36px; font-weight: 700; margin: 0; letter-spacing: 2px;">CONSULTING INVOICE</h1>
            <p style="margin: 16px 0 0 0; font-size: 16px; opacity: 0.9;">Professional Advisory Services</p>
          </div>
          
          <div style="padding: 40px; background: white;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 40px;">
              <div style="flex: 1; padding-right: 40px;">
                <div style="background: #f8fafc; padding: 24px; border-radius: 8px; border-left: 4px solid #1e40af; margin-bottom: 24px;">
                  <h3 style="color: #1e40af; font-size: 14px; font-weight: 700; text-transform: uppercase; margin: 0 0 16px 0;">Consultant</h3>
                  <div>
                    <p style="font-weight: 700; margin: 0 0 8px 0; font-size: 18px; color: #1f2937;">{{businessName}}</p>
                    <p style="margin: 4px 0; color: #6b7280;">{{businessAddress}}</p>
                    <p style="margin: 4px 0; color: #6b7280;">{{businessPhone}}</p>
                    <p style="margin: 4px 0; color: #1e40af; font-weight: 600;">{{businessEmail}}</p>
                  </div>
                </div>
                
                <div style="background: #f0f9ff; padding: 24px; border-radius: 8px; border-left: 4px solid #0ea5e9;">
                  <h3 style="color: #0c4a6e; font-size: 14px; font-weight: 700; text-transform: uppercase; margin: 0 0 16px 0;">Client</h3>
                  <div>
                    <p style="font-weight: 700; margin: 0 0 8px 0; font-size: 18px; color: #1f2937;">{{clientName}}</p>
                    <p style="margin: 4px 0; color: #6b7280;">{{clientAddress}}</p>
                    <p style="margin: 4px 0; color: #6b7280;">{{clientPhone}}</p>
                    <p style="margin: 4px 0; color: #0ea5e9; font-weight: 600;">{{clientEmail}}</p>
                  </div>
                </div>
              </div>
              
              <div style="text-align: right;">
                <div style="background: #1f2937; color: white; padding: 20px; border-radius: 8px; margin-bottom: 24px;">
                  <p style="margin: 0 0 8px 0; color: #9ca3af; font-size: 14px;">Invoice Number</p>
                  <p style="margin: 0; font-size: 24px; font-weight: 700;">#{{invoiceNumber}}</p>
                </div>
                
                <div style="background: #fef3c7; padding: 20px; border-radius: 8px; border: 1px solid #fbbf24;">
                  <div style="margin-bottom: 12px;">
                    <p style="margin: 0; color: #92400e; font-size: 14px; font-weight: 600;">Issue Date</p>
                    <p style="margin: 0; color: #1f2937; font-weight: 600;">{{date}}</p>
                  </div>
                  <div>
                    <p style="margin: 0; color: #dc2626; font-size: 14px; font-weight: 600;">Due Date</p>
                    <p style="margin: 0; color: #dc2626; font-weight: 700;">{{dueDate}}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div style="margin-bottom: 30px;">
              <h3 style="color: #1e40af; font-size: 18px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Consulting Services Provided</h3>
              
              <table style="width: 100%; border-collapse: collapse; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
                <thead>
                  <tr style="background: #1e40af; color: white;">
                    <th style="text-align: left; padding: 20px; font-weight: 700;">Service Description</th>
                    <th style="text-align: center; padding: 20px; font-weight: 700; width: 100px;">Hours</th>
                    <th style="text-align: right; padding: 20px; font-weight: 700; width: 120px;">Hourly Rate</th>
                    <th style="text-align: right; padding: 20px; font-weight: 700; width: 120px;">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {{#lineItems}}
                  <tr style="background: #fafafa; border-bottom: 1px solid #e5e7eb;">
                    <td style="padding: 20px; color: #1f2937; font-weight: 500;">{{description}}</td>
                    <td style="padding: 20px; text-align: center; color: #6b7280; font-weight: 600;">{{quantity}}</td>
                    <td style="padding: 20px; text-align: right; color: #6b7280; font-weight: 600;">\${{rate}}</td>
                    <td style="padding: 20px; text-align: right; font-weight: 700; color: #1f2937;">\${{amount}}</td>
                  </tr>
                  {{/lineItems}}
                </tbody>
              </table>
            </div>
            
            <div style="display: flex; justify-content: flex-end; margin-bottom: 30px;">
              <div style="width: 400px; background: #f8fafc; border-radius: 8px; overflow: hidden; border: 1px solid #e5e7eb;">
                <div style="padding: 20px;">
                  <div style="display: flex; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #e5e7eb;">
                    <span style="color: #6b7280; font-weight: 500;">Subtotal</span>
                    <span style="color: #1f2937; font-weight: 600; font-size: 16px;">\${{subtotal}}</span>
                  </div>
                  <div style="display: flex; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #e5e7eb;">
                    <span style="color: #6b7280; font-weight: 500;">Tax</span>
                    <span style="color: #1f2937; font-weight: 600; font-size: 16px;">\${{tax}}</span>
                  </div>
                </div>
                <div style="background: linear-gradient(135deg, #1e40af, #3b82f6); color: white; padding: 20px;">
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="font-weight: 700; font-size: 20px;">TOTAL DUE</span>
                    <span style="font-weight: 700; font-size: 24px;">\${{total}}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div style="background: #eff6ff; padding: 24px; border-radius: 8px; border: 1px solid #bfdbfe; text-align: center; margin-bottom: 20px;">
              <p style="margin: 0; color: #1e40af; font-weight: 600; font-size: 16px;">Payment Terms: Net 30 Days</p>
              <p style="margin: 8px 0 0 0; color: #3730a3; font-size: 14px;">Please remit payment within 30 days of invoice date</p>
            </div>
            
            {{#notes}}
            <div style="background: #f9fafb; padding: 24px; border-radius: 8px; border-left: 4px solid #6b7280;">
              <h4 style="color: #374151; margin: 0 0 12px 0; font-size: 16px; font-weight: 600;">Project Notes</h4>
              <p style="color: #4b5563; margin: 0; line-height: 1.6;">{{notes}}</p>
            </div>
            {{/notes}}
          </div>
          
          <div style="background: #1f2937; color: white; padding: 24px; text-align: center;">
            <p style="margin: 0; font-size: 14px; opacity: 0.8;">Thank you for your business. We value our professional relationship.</p>
          </div>
        </div>
      </div>
    `
  },
  
  {
    id: 'service-business',
    name: 'Service Business',
    industry: 'Service Business',
    description: 'Versatile template suitable for various service-based businesses',
    previewImage: '/templates/service-business-preview.png',
    fields: ['companyLogo', 'businessInfo', 'clientInfo', 'invoiceNumber', 'date', 'dueDate', 'lineItems', 'subtotal', 'tax', 'total', 'notes', 'paymentTerms'],
    styling: {
      primaryColor: '#7c2d12',
      fontFamily: 'Inter, sans-serif',
      layout: 'service-focused'
    },
    htmlTemplate: `
      <div style="max-width: 800px; margin: 0 auto; padding: 40px; font-family: Inter, sans-serif; background: #fafaf9;">
        <div style="background: white; border-radius: 16px; overflow: hidden; box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);">
          <div style="background: linear-gradient(135deg, #7c2d12 0%, #a16207 50%, #ea580c 100%); padding: 40px; color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div>
                <h1 style="font-size: 32px; font-weight: 800; margin: 0; letter-spacing: 1px;">SERVICE INVOICE</h1>
                <p style="margin: 12px 0 0 0; font-size: 16px; opacity: 0.9;">Professional Services Billing</p>
              </div>
              <div style="text-align: right;">
                <div style="background: rgba(255, 255, 255, 0.2); padding: 16px 24px; border-radius: 12px; backdrop-filter: blur(10px);">
                  <p style="margin: 0; font-size: 14px; opacity: 0.8;">Invoice #</p>
                  <p style="margin: 4px 0 0 0; font-size: 24px; font-weight: 700;">{{invoiceNumber}}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div style="padding: 40px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px;">
              <div>
                <div style="background: #fef7ed; padding: 24px; border-radius: 12px; border: 1px solid #fed7aa; margin-bottom: 24px;">
                  <h3 style="color: #9a3412; font-size: 14px; font-weight: 700; text-transform: uppercase; margin: 0 0 16px 0; letter-spacing: 1px;">Service Provider</h3>
                  <div>
                    <p style="font-weight: 700; margin: 0 0 12px 0; font-size: 20px; color: #1f2937;">{{businessName}}</p>
                    <p style="margin: 4px 0; color: #6b7280; font-size: 15px;">{{businessAddress}}</p>
                    <p style="margin: 4px 0; color: #6b7280; font-size: 15px;">{{businessPhone}}</p>
                    <p style="margin: 4px 0; color: #ea580c; font-weight: 600; font-size: 15px;">{{businessEmail}}</p>
                  </div>
                </div>
                
                <div style="background: #f0fdfa; padding: 24px; border-radius: 12px; border: 1px solid #5eead4;">
                  <h3 style="color: #134e4a; font-size: 14px; font-weight: 700; text-transform: uppercase; margin: 0 0 16px 0; letter-spacing: 1px;">Bill To</h3>
                  <div>
                    <p style="font-weight: 700; margin: 0 0 12px 0; font-size: 20px; color: #1f2937;">{{clientName}}</p>
                    <p style="margin: 4px 0; color: #6b7280; font-size: 15px;">{{clientAddress}}</p>
                    <p style="margin: 4px 0; color: #6b7280; font-size: 15px;">{{clientPhone}}</p>
                    <p style="margin: 4px 0; color: #0d9488; font-weight: 600; font-size: 15px;">{{clientEmail}}</p>
                  </div>
                </div>
              </div>
              
              <div>
                <div style="background: linear-gradient(135deg, #f3f4f6, #e5e7eb); padding: 30px; border-radius: 12px; text-align: center;">
                  <div style="margin-bottom: 24px;">
                    <p style="margin: 0 0 8px 0; color: #6b7280; font-size: 14px; font-weight: 600;">Invoice Date</p>
                    <p style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 700;">{{date}}</p>
                  </div>
                  
                  <div style="background: #fef2f2; padding: 20px; border-radius: 8px; border: 2px solid #fca5a5;">
                    <p style="margin: 0 0 8px 0; color: #991b1b; font-size: 14px; font-weight: 700;">Payment Due</p>
                    <p style="margin: 0; color: #dc2626; font-size: 20px; font-weight: 800;">{{dueDate}}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div style="background: #fafaf9; padding: 30px; border-radius: 12px; margin-bottom: 30px; border: 1px solid #e7e5e4;">
              <h3 style="color: #7c2d12; font-size: 20px; font-weight: 700; margin: 0 0 24px 0; text-align: center;">Services Rendered</h3>
              
              <table style="width: 100%; border-collapse: collapse;">
                <thead>
                  <tr style="background: linear-gradient(135deg, #7c2d12, #a16207); color: white;">
                    <th style="text-align: left; padding: 20px; font-weight: 700; border-radius: 8px 0 0 0;">Service Details</th>
                    <th style="text-align: center; padding: 20px; font-weight: 700; width: 100px;">Qty</th>
                    <th style="text-align: right; padding: 20px; font-weight: 700; width: 120px;">Unit Price</th>
                    <th style="text-align: right; padding: 20px; font-weight: 700; width: 120px; border-radius: 0 8px 0 0;">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {{#lineItems}}
                  <tr style="background: white; border-bottom: 2px solid #f5f5f4;">
                    <td style="padding: 20px; color: #1f2937; font-weight: 500; font-size: 15px;">{{description}}</td>
                    <td style="padding: 20px; text-align: center; color: #6b7280; font-weight: 600; font-size: 16px;">{{quantity}}</td>
                    <td style="padding: 20px; text-align: right; color: #6b7280; font-weight: 600; font-size: 16px;">\${{rate}}</td>
                    <td style="padding: 20px; text-align: right; font-weight: 700; color: #1f2937; font-size: 16px;">\${{amount}}</td>
                  </tr>
                  {{/lineItems}}
                </tbody>
              </table>
            </div>
            
            <div style="display: flex; justify-content: flex-end; margin-bottom: 30px;">
              <div style="width: 400px;">
                <div style="background: white; border: 2px solid #e7e5e4; border-radius: 12px; overflow: hidden;">
                  <div style="padding: 24px; background: #fafaf9;">
                    <div style="display: flex; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #d6d3d1;">
                      <span style="color: #78716c; font-weight: 600; font-size: 16px;">Subtotal</span>
                      <span style="color: #1f2937; font-weight: 700; font-size: 16px;">\${{subtotal}}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #d6d3d1;">
                      <span style="color: #78716c; font-weight: 600; font-size: 16px;">Tax</span>
                      <span style="color: #1f2937; font-weight: 700; font-size: 16px;">\${{tax}}</span>
                    </div>
                  </div>
                  
                  <div style="background: linear-gradient(135deg, #7c2d12, #a16207); color: white; padding: 24px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                      <span style="font-weight: 800; font-size: 20px; letter-spacing: 1px;">TOTAL DUE</span>
                      <span style="font-weight: 800; font-size: 28px;">\${{total}}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
              <div style="background: #ecfdf5; padding: 20px; border-radius: 8px; border-left: 4px solid #10b981;">
                <h4 style="color: #047857; margin: 0 0 8px 0; font-size: 14px; font-weight: 700;">Payment Terms</h4>
                <p style="color: #065f46; margin: 0; font-size: 14px; font-weight: 500;">Payment due within 30 days of invoice date</p>
              </div>
              
              <div style="background: #eff6ff; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6;">
                <h4 style="color: #1d4ed8; margin: 0 0 8px 0; font-size: 14px; font-weight: 700;">Service Guarantee</h4>
                <p style="color: #1e3a8a; margin: 0; font-size: 14px; font-weight: 500;">100% satisfaction guaranteed on all services</p>
              </div>
            </div>
            
            {{#notes}}
            <div style="background: #fef7ed; padding: 24px; border-radius: 12px; border: 1px solid #fed7aa;">
              <h4 style="color: #9a3412; margin: 0 0 12px 0; font-size: 16px; font-weight: 700;">Additional Information</h4>
              <p style="color: #7c2d12; margin: 0; line-height: 1.7; font-size: 15px;">{{notes}}</p>
            </div>
            {{/notes}}
          </div>
          
          <div style="background: linear-gradient(135deg, #1f2937, #374151); color: white; padding: 30px; text-align: center;">
            <p style="margin: 0 0 8px 0; font-size: 18px; font-weight: 600;">Thank you for your business!</p>
            <p style="margin: 0; font-size: 14px; opacity: 0.8;">We appreciate the opportunity to serve you</p>
          </div>
        </div>
      </div>
    `
  }
];

export const getTemplateById = (id: string): InvoiceTemplate | undefined => {
  return invoiceTemplates.find(template => template.id === id);
};

export const getTemplatesByIndustry = (industry: string): InvoiceTemplate[] => {
  return invoiceTemplates.filter(template => 
    template.industry.toLowerCase().includes(industry.toLowerCase())
  );
};

export const searchTemplates = (query: string): InvoiceTemplate[] => {
  const lowercaseQuery = query.toLowerCase();
  return invoiceTemplates.filter(template => 
    template.name.toLowerCase().includes(lowercaseQuery) ||
    template.description.toLowerCase().includes(lowercaseQuery) ||
    template.industry.toLowerCase().includes(lowercaseQuery)
  );
};