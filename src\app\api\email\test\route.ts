import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if email service is configured
    const hasEmailConfig = !!(
      process.env.SMTP_HOST &&
      process.env.SMTP_USER &&
      process.env.SMTP_PASS
    );
    
    if (!hasEmailConfig) {
      return NextResponse.json(
        { configured: false, message: 'Email service not configured' },
        { status: 503 }
      );
    }

    // In production, you might want to verify SMTP connection
    return NextResponse.json({
      configured: true,
      message: 'Email service is configured',
      provider: process.env.SMTP_HOST || 'Unknown'
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Email test failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}