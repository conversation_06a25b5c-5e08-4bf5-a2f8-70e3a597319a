'use client';

import { useEffect } from 'react';
import { onCLS, onFCP, onINP, onLCP, onTTFB } from 'web-vitals';
import { useReportWebVitals } from 'next/web-vitals';

type MetricType = 'CLS' | 'FCP' | 'INP' | 'LCP' | 'TTFB';

interface Metric {
  name: MetricType;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
  navigationType: 'navigate' | 'reload' | 'back_forward' | 'prerender';
}

function sendToAnalytics(metric: Metric) {
  // Send to Google Analytics if available
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'web_vitals', {
      event_category: 'Web Vitals',
      event_label: metric.name,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
      metric_id: metric.id,
      metric_value: metric.value,
      metric_delta: metric.delta,
      metric_rating: metric.rating,
    });
  }

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Web Vitals] ${metric.name}:`, {
      value: metric.value,
      rating: metric.rating,
      delta: metric.delta,
    });
  }

  // You can also send to a custom analytics endpoint
  // fetch('/api/analytics/vitals', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(metric),
  // });
}

export default function WebVitals() {
  // Use Next.js built-in web vitals reporting
  useReportWebVitals((metric) => {
    // This will automatically send to Vercel Analytics
    sendToAnalytics(metric as Metric);
  });

  useEffect(() => {
    // Core Web Vitals
    onCLS((metric) => sendToAnalytics(metric as Metric));
    onLCP((metric) => sendToAnalytics(metric as Metric));
    onINP((metric) => sendToAnalytics(metric as Metric)); // INP replaces FID

    // Additional metrics
    onFCP((metric) => sendToAnalytics(metric as Metric));
    onTTFB((metric) => sendToAnalytics(metric as Metric));
  }, []);

  return null;
}

// Helper function to check if metrics are good
export function isGoodWebVitals(metric: Metric): boolean {
  const thresholds = {
    CLS: 0.1,
    FCP: 1800,
    LCP: 2500,
    INP: 200,
    TTFB: 800,
  };

  return metric.value <= thresholds[metric.name];
}

// Declare gtag on window for TypeScript
declare global {
  interface Window {
    gtag?: (
      command: string,
      action: string,
      parameters: {
        event_category?: string;
        event_label?: string;
        value?: number;
        non_interaction?: boolean;
        [key: string]: any;
      }
    ) => void;
  }
}