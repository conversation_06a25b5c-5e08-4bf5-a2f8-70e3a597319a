
# =================================================================
# DATABASE CONFIGURATION
# =================================================================
# MongoDB Atlas connection string
# Get from: MongoDB Atlas Dashboard → Connect → Drivers
MONGODB_URI=MONGODB_URI=mongodb+srv://sbdinkins:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0


# =================================================================
# NEXTAUTH CONFIGURATION
# =================================================================
# Generate with: openssl rand -base64 32
# Or use: https://generate-secret.vercel.app/32
NEXTAUTH_SECRET=eb995785a6b5e9f1aeb880a474770f06

# Your app's URL (different for each environment)
# Local: http://localhost:3000
# Production: https://templateinvoices.com
NEXTAUTH_URL=https://templateinvoices.com

# =================================================================
# GOOGLE OAUTH CONFIGURATION
# =================================================================
# Get from: Google Cloud Console → APIs & Services → Credentials
# Create OAuth 2.0 Client ID for web application
GOOGLE_CLIENT_ID=763930941779-a3d9tc1q9vo24p7m7mbqbqaf52qgr0fh.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
# Google Analytics 4


# =================================================================
# STRIPE CONFIGURATION (PAYMENTS)
# =================================================================
# Get from: Stripe Dashboard → Developers → API Keys
# Use test keys for development, live keys for production
# STRIPE_SECRET_KEY=***********************************************************************************************************
# STRIPE_PUBLISHABLE_KEY=pk_live_51RD3dtFo3OyCE7jhxQKRZsiKnWpFHjSoYyaF4XLIkCjFUgHW62cLeNVT7It5DsWnSpiyhrWGjHUwCt3ZalTKI15N00J4ybtdIy

# Webhook secret from: Stripe Dashboard → Developers → Webhooks
STRIPE_WEBHOOK_SECRET=whsec_NgSo8cJdQYeYk1ZY5qS7VefUOPXHlGAl

# Pro plan price ID from: Stripe Dashboard → Products
# Create a $9.99/month recurring product and copy its price ID
STRIPE_PRO_PRICE_ID=price_1RUyKYFo3OyCE7jhgrfeN06C

# =================================================================
# EMAIL CONFIGURATION (INVOICE SENDING)
# =================================================================
# SMTP settings for sending invoice emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
# For Gmail: Generate App Password in Google Account settings
SMTP_PASS=nahn atso jmrj yqhx

# =================================================================
# AI CONFIGURATION
# =================================================================
# Get from: https://console.anthropic.com/
ANTHROPIC_API_KEY=AIzaSyBeQ_EkgYUv0lt0Mdbmwwljb0LPmYB4T6w

# Alternative: OpenAI API (if using ChatGPT instead)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# =================================================================
# APPLICATION SETTINGS
# =================================================================
# Environment: development, production, test
NODE_ENV=production

# Your app's base URL (accessible in client-side code)
# Must match NEXTAUTH_URL value
NEXT_PUBLIC_BASE_URL=https://templateinvoices.com

# =================================================================
# PUPPETEER CONFIGURATION (PDF GENERATION)
# =================================================================
# Set to true in production (Vercel), false for local development
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=false

# PDF generation timeout (milliseconds)
PDF_GENERATION_TIMEOUT=30000

# =================================================================
# SECURITY SETTINGS
# =================================================================
# Enable rate limiting for API endpoints
RATE_LIMIT_ENABLED=true

# Maximum file upload size (in MB)
MAX_FILE_SIZE_MB=5

# Session configuration
SESSION_MAX_AGE=86400

# File upload settings
UPLOAD_MAX_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# =================================================================
# FEATURE FLAGS
# =================================================================
# Enable/disable major features
ENABLE_AI_FEATURES=true
ENABLE_PDF_GENERATION=true
ENABLE_EMAIL_SENDING=true
ENABLE_STRIPE_PAYMENTS=true

# Public feature flags (accessible in client code)
NEXT_PUBLIC_AI_CONFIGURED=true
NEXT_PUBLIC_EMAIL_CONFIGURED=true
NEXT_PUBLIC_STRIPE_CONFIGURED=true

# Allow mock data for development/testing
NEXT_PUBLIC_ALLOW_MOCK_DATA=true

# =================================================================
# LOGGING AND MONITORING
# =================================================================
# Log level: error, warn, info, debug
LOG_LEVEL=info

# Enable detailed debug logging
ENABLE_DEBUG_LOGGING=false

# =================================================================
# PERFORMANCE SETTINGS
# =================================================================
# Email sending timeout (milliseconds)
EMAIL_SEND_TIMEOUT=15000

# API request timeouts
API_TIMEOUT=10000

# =================================================================
# DEVELOPMENT SETTINGS
# =================================================================
# Disable SSL verification (development only)
DISABLE_SSL_VERIFY=true

# Mock external services for testing
MOCK_EMAIL_SENDING=true
MOCK_PDF_GENERATION=true
MOCK_STRIPE_PAYMENTS=true

# =================================================================
# ENVIRONMENT-SPECIFIC EXAMPLES
# =================================================================

# LOCAL DEVELOPMENT (.env.local):
# NEXTAUTH_URL=http://localhost:3000
# NEXT_PUBLIC_BASE_URL=http://localhost:3000
STRIPE_SECRET_KEY=sk_test_51RD3eJCBsplbpPtg6WhHE5yjy4Bz46TdHRdCDJpxiYLuvoy9DPW1AIJ8ncC9CRROoRmESqCARV40iUaEwT5flB7G008SqukycX
STRIPE_PUBLISHABLE_KEY=pk_test_51RD3eJCBsplbpPtglBrw1yPrHet6j4sXYcYWl4VNQT8lMJL7nZgbW8Xy6SEBrCjpQ898hAt72lG9gmjpv3LlQSYs00tSnytfc2
NODE_ENV=test

# VERCEL PRODUCTION (Environment Variables):
NEXTAUTH_URL=https://templateinvoices.com
NEXT_PUBLIC_BASE_URL=https://templateinvoices.com
# STRIPE_SECRET_KEY=***********************************************************************************************************
# STRIPE_PUBLISHABLE_KEY=pk_live_51RD3dtFo3OyCE7jhxQKRZsiKnWpFHjSoYyaF4XLIkCjFUgHW62cLeNVT7It5DsWnSpiyhrWGjHUwCt3ZalTKI15N00J4ybtdIy
# STRIPE_WEBHOOK_SECRET=whsec_OsQLQM8kKMK43uzMDvDc0JiPFACVe09K
NODE_ENV=production
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# =================================================================
# SETUP CHECKLIST
# =================================================================
# □ MongoDB Atlas cluster created and connection string added
# □ Google OAuth client created and credentials added
# □ Stripe account setup with API keys and webhook configured
# □ Email SMTP settings configured (Gmail app password)
# □ Anthropic API key obtained
# □ All URLs updated for your domain
# □ Environment variables added to Vercel
# □ .env.local added to .gitignore
# □ Test all integrations work

# =================================================================
# IMPORTANT SECURITY NOTES
# =================================================================
# 1. Never commit this file with real values to Git
# 2. Use different databases for development/production
# 3. Use Stripe test keys in development
# 4. Regenerate all secrets if accidentally exposed
# 5. Enable 2FA on all service accounts
# 6. Regularly rotate API keys and passwords
# 7. Monitor usage and billing on all services