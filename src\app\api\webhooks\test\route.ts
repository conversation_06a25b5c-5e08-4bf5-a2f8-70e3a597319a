import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  console.log('🧪 Test webhook GET received');
  return NextResponse.json({ 
    status: 'Test webhook GET works',
    timestamp: new Date().toISOString(),
    path: '/api/webhooks/test'
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    console.log('🧪 Test webhook POST received:', body.substring(0, 100));
    console.log('🧪 Headers:', Object.fromEntries(request.headers.entries()));
    
    return NextResponse.json({ 
      received: true,
      bodyLength: body.length,
      timestamp: new Date().toISOString(),
      path: '/api/webhooks/test'
    });
  } catch (error) {
    console.error('🧪 Test webhook error:', error);
    return NextResponse.json({ 
      error: 'Test webhook failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}