import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/mongodb';
import { COLLECTIONS } from '@/lib/models';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const client = await clientPromise;
    const db = client.db();
    const usersCollection = db.collection(COLLECTIONS.USERS);
    const invoicesCollection = db.collection(COLLECTIONS.INVOICES);

    const userObjectId = typeof session.user.id === 'string' ? new ObjectId(session.user.id) : session.user.id;

    // Get user onboarding status
    const user = await usersCollection.findOne({ _id: userObjectId });
    
    // Check if user has created any invoices
    const invoiceCount = await invoicesCollection.countDocuments({ userId: userObjectId });

    return NextResponse.json({
      hasCompletedOnboarding: (user as any)?.onboarding?.completed || false,
      hasCreatedFirstInvoice: invoiceCount > 0,
      onboardingStep: (user as any)?.onboarding?.currentStep || null,
      lastOnboardingDate: (user as any)?.onboarding?.completedAt || null,
      invoiceCount
    });

  } catch (error) {
    console.error('Error fetching onboarding status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch onboarding status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const updates = await request.json();
    const client = await clientPromise;
    const db = client.db();
    const usersCollection = db.collection(COLLECTIONS.USERS);

    const userObjectId = typeof session.user.id === 'string' ? new ObjectId(session.user.id) : session.user.id;

    // Build update object
    const updateData: any = {
      updatedAt: new Date()
    };

    if (updates.hasCompletedOnboarding !== undefined) {
      updateData['onboarding.completed'] = updates.hasCompletedOnboarding;
      if (updates.hasCompletedOnboarding) {
        updateData['onboarding.completedAt'] = new Date();
      }
    }

    if (updates.onboardingStep !== undefined) {
      updateData['onboarding.currentStep'] = updates.onboardingStep;
    }

    if (updates.lastOnboardingDate !== undefined) {
      updateData['onboarding.lastUpdated'] = new Date(updates.lastOnboardingDate);
    }

    // Update user
    await usersCollection.updateOne(
      { _id: userObjectId },
      { $set: updateData }
    );

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error updating onboarding status:', error);
    return NextResponse.json(
      { error: 'Failed to update onboarding status' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const client = await clientPromise;
    const db = client.db();
    const usersCollection = db.collection(COLLECTIONS.USERS);

    const userObjectId = typeof session.user.id === 'string' ? new ObjectId(session.user.id) : session.user.id;

    // Reset onboarding data
    await usersCollection.updateOne(
      { _id: userObjectId },
      { 
        $unset: { onboarding: "" },
        $set: { updatedAt: new Date() }
      }
    );

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error resetting onboarding status:', error);
    return NextResponse.json(
      { error: 'Failed to reset onboarding status' },
      { status: 500 }
    );
  }
}