import React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { 
  Building2, 
  ShoppingCart, 
  Heart, 
  GraduationCap, 
  Plane, 
  Home,
  Briefcase,
  Zap
} from 'lucide-react';

export interface Industry {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  stats?: {
    label: string;
    value: string;
  };
}

export interface IndustryGridProps {
  industries?: Industry[];
  title?: string;
  subtitle?: string;
  columns?: 2 | 3 | 4;
  className?: string;
}

const defaultIndustries: Industry[] = [
  {
    id: 'retail',
    title: 'Retail & E-commerce',
    description: 'Streamline inventory management and boost online sales with integrated solutions.',
    icon: <ShoppingCart className="w-8 h-8" />,
    color: 'bg-blue-500',
    stats: { label: 'Active Stores', value: '10K+' },
  },
  {
    id: 'healthcare',
    title: 'Healthcare',
    description: 'Secure patient data management and efficient billing for medical practices.',
    icon: <Heart className="w-8 h-8" />,
    color: 'bg-red-500',
    stats: { label: 'Patients Served', value: '1M+' },
  },
  {
    id: 'education',
    title: 'Education',
    description: 'Comprehensive management systems for schools and educational institutions.',
    icon: <GraduationCap className="w-8 h-8" />,
    color: 'bg-green-500',
    stats: { label: 'Institutions', value: '5K+' },
  },
  {
    id: 'hospitality',
    title: 'Hospitality & Travel',
    description: 'Booking management and customer service solutions for hotels and travel agencies.',
    icon: <Plane className="w-8 h-8" />,
    color: 'bg-purple-500',
    stats: { label: 'Bookings Daily', value: '50K+' },
  },
  {
    id: 'realestate',
    title: 'Real Estate',
    description: 'Property management and client relationship tools for real estate professionals.',
    icon: <Home className="w-8 h-8" />,
    color: 'bg-indigo-500',
    stats: { label: 'Properties Listed', value: '100K+' },
  },
  {
    id: 'professional',
    title: 'Professional Services',
    description: 'Time tracking, invoicing, and project management for consultants and agencies.',
    icon: <Briefcase className="w-8 h-8" />,
    color: 'bg-yellow-500',
    stats: { label: 'Professionals', value: '25K+' },
  },
];

const IndustryGrid: React.FC<IndustryGridProps> = ({
  industries = defaultIndustries,
  title = 'Industries We Serve',
  subtitle = 'Tailored solutions for every business sector',
  columns = 3,
  className,
}) => {
  const gridCols = {
    2: 'md:grid-cols-2',
    3: 'md:grid-cols-2 lg:grid-cols-3',
    4: 'md:grid-cols-2 lg:grid-cols-4',
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section className={cn('py-16 lg:py-24', className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && (
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {subtitle}
              </p>
            )}
          </div>
        )}

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: '-100px' }}
          className={cn('grid grid-cols-1 gap-6', gridCols[columns])}
        >
          {industries.map((industry) => (
            <motion.div
              key={industry.id}
              variants={itemVariants}
              whileHover={{ y: -5 }}
              className="group relative bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
            >
              <div className="p-6">
                <div
                  className={cn(
                    'w-16 h-16 rounded-lg flex items-center justify-center text-white mb-4',
                    industry.color
                  )}
                >
                  {industry.icon}
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {industry.title}
                </h3>
                
                <p className="text-gray-600 mb-4">
                  {industry.description}
                </p>

                {industry.stats && (
                  <div className="pt-4 border-t border-gray-100">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        {industry.stats.label}
                      </span>
                      <span className="text-lg font-bold text-gray-900">
                        {industry.stats.value}
                      </span>
                    </div>
                  </div>
                )}

                <div className="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-transparent via-current to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{ color: industry.color.replace('bg-', '') }}
                />
              </div>

              <motion.div
                className="absolute -right-8 -bottom-8 w-24 h-24 opacity-5"
                whileHover={{ scale: 1.1, rotate: 10 }}
                transition={{ duration: 0.3 }}
              >
                <div className={cn('w-full h-full rounded-full', industry.color)} />
              </motion.div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="text-center mt-12"
        >
          <p className="text-gray-600 mb-6">
            Don't see your industry? We can customize our solution for your specific needs.
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary/90 transition-colors"
          >
            <Zap className="w-5 h-5 mr-2" />
            Get Custom Solution
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export { IndustryGrid };