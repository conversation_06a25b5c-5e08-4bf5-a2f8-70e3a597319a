import type { Metadata } from 'next'
import Link from 'next/link'
import { ArrowR<PERSON>, Palette, Upload, Type, Eye, Wand2, Setting<PERSON>, Brush } from 'lucide-react'

export const metadata: Metadata = {
  title: 'How to Customize Invoice Templates - Design Guide',
  description: 'Personalize invoice templates to match your brand. Learn colors, fonts, logos, and layout customization options.',
  keywords: ['customize invoice template', 'brand invoice', 'invoice design', 'invoice branding', 'template customization', 'invoice layout'],
  openGraph: {
    title: 'How to Customize Invoice Templates - Design Guide',
    description: 'Personalize invoice templates to match your brand. Learn colors, fonts, logos, and layout customization options.',
    type: 'article',
  },
}

export default function CustomizingTemplatesGuide() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Customize Invoice Templates
            </h1>
            <p className="text-xl text-purple-100 max-w-3xl mx-auto">
              Transform templates to match your brand and create stunning, professional invoices
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="max-w-4xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
        <nav className="flex items-center space-x-2 text-sm text-gray-600">
          <Link href="/" className="hover:text-gray-900">Home</Link>
          <span>/</span>
          <Link href="/guides" className="hover:text-gray-900">Guides</Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">Customizing Templates</span>
        </nav>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Table of Contents */}
        <div className="bg-purple-50 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Table of Contents</h2>
          <ol className="space-y-2 text-purple-700">
            <li><a href="#introduction" className="hover:text-purple-900">1. Introduction to Template Customization</a></li>
            <li><a href="#brand-setup" className="hover:text-purple-900">2. Setting Up Your Brand Identity</a></li>
            <li><a href="#logo-upload" className="hover:text-purple-900">3. Adding Your Logo</a></li>
            <li><a href="#color-schemes" className="hover:text-purple-900">4. Color Schemes and Branding</a></li>
            <li><a href="#typography" className="hover:text-purple-900">5. Typography and Fonts</a></li>
            <li><a href="#layout-options" className="hover:text-purple-900">6. Layout Customization</a></li>
            <li><a href="#advanced-features" className="hover:text-purple-900">7. Advanced Customization Features</a></li>
            <li><a href="#preview-test" className="hover:text-purple-900">8. Preview and Testing</a></li>
            <li><a href="#best-practices" className="hover:text-purple-900">9. Design Best Practices</a></li>
          </ol>
        </div>

        {/* Introduction */}
        <section id="introduction" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Introduction to Template Customization</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Your invoice is often the first formal document your clients receive from your business, 
            making it a crucial part of your brand identity. Template Invoice's customization tools 
            allow you to create invoices that reflect your unique brand while maintaining professionalism 
            and readability.
          </p>
          
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 mb-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center">
              <Wand2 className="w-6 h-6 mr-2 text-purple-600" />
              What You Can Customize
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              <ul className="space-y-2 text-gray-700">
                <li>• Company logo and branding</li>
                <li>• Color schemes and themes</li>
                <li>• Typography and font choices</li>
                <li>• Layout and section arrangement</li>
              </ul>
              <ul className="space-y-2 text-gray-700">
                <li>• Header and footer content</li>
                <li>• Payment terms and notes</li>
                <li>• Field labels and descriptions</li>
              </ul>
            </div>
          </div>

          <div className="bg-yellow-50 rounded-lg p-6">
            <h3 className="font-semibold text-yellow-800 mb-2">Before You Start</h3>
            <p className="text-yellow-700">
              Have your brand assets ready: logo files (PNG, SVG preferred), brand colors (hex codes), 
              and any specific fonts you want to use. This will streamline the customization process.
            </p>
          </div>
        </section>

        {/* Brand Setup */}
        <section id="brand-setup" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Setting Up Your Brand Identity</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Before diving into specific customizations, it's important to establish your brand guidelines 
            for consistency across all your invoices and business documents.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Brush className="w-6 h-6 mr-2 text-purple-600" />
                Brand Guidelines Checklist
              </h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Visual Elements</h4>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <input type="checkbox" className="mt-1 mr-2" />
                      <div>
                        <strong className="text-sm">Primary Logo</strong>
                        <p className="text-xs text-gray-600">High-resolution version for headers</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <input type="checkbox" className="mt-1 mr-2" />
                      <div>
                        <strong className="text-sm">Brand Colors</strong>
                        <p className="text-xs text-gray-600">Primary, secondary, and accent colors</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <input type="checkbox" className="mt-1 mr-2" />
                      <div>
                        <strong className="text-sm">Typography</strong>
                        <p className="text-xs text-gray-600">Primary and secondary font choices</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Content Elements</h4>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <input type="checkbox" className="mt-1 mr-2" />
                      <div>
                        <strong className="text-sm">Business Information</strong>
                        <p className="text-xs text-gray-600">Address, contact details, tax ID</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <input type="checkbox" className="mt-1 mr-2" />
                      <div>
                        <strong className="text-sm">Payment Terms</strong>
                        <p className="text-xs text-gray-600">Standard terms and conditions</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <input type="checkbox" className="mt-1 mr-2" />
                      <div>
                        <strong className="text-sm">Brand Voice</strong>
                        <p className="text-xs text-gray-600">Tone for notes and communications</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Logo Upload */}
        <section id="logo-upload" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Adding Your Logo</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Your logo is the cornerstone of your brand identity on invoices. Here's how to add and 
            optimize your logo for the best appearance:
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start">
                <div className="bg-purple-100 rounded-full p-2 mr-4">
                  <Upload className="w-6 h-6 text-purple-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold mb-2">Step 1: Upload Your Logo</h3>
                  <p className="text-gray-700 mb-4">
                    Navigate to the template customization section and click "Upload Logo". 
                    We support PNG, JPG, and SVG formats for maximum quality.
                  </p>
                  
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-800 mb-2">Logo Requirements</h4>
                    <ul className="text-blue-700 text-sm space-y-1">
                      <li>• <strong>Format:</strong> PNG (recommended), JPG, or SVG</li>
                      <li>• <strong>Resolution:</strong> Minimum 300 DPI for crisp printing</li>
                      <li>• <strong>Size:</strong> Recommended width: 200-400 pixels</li>
                      <li>• <strong>Background:</strong> Transparent background preferred</li>
                      <li>• <strong>File size:</strong> Maximum 5MB</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Logo Positioning Options</h3>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="border border-gray-200 rounded-lg p-4 text-center">
                  <div className="h-16 bg-gray-100 rounded mb-2 flex items-center justify-center">
                    <span className="text-xs text-gray-500">Logo</span>
                  </div>
                  <h4 className="font-semibold text-sm">Top Left</h4>
                  <p className="text-xs text-gray-600">Traditional business placement</p>
                </div>
                <div className="border border-gray-200 rounded-lg p-4 text-center">
                  <div className="h-16 bg-gray-100 rounded mb-2 flex items-center justify-center">
                    <span className="text-xs text-gray-500">Logo</span>
                  </div>
                  <h4 className="font-semibold text-sm">Top Center</h4>
                  <p className="text-xs text-gray-600">Centered, prominent display</p>
                </div>
                <div className="border border-gray-200 rounded-lg p-4 text-center">
                  <div className="h-16 bg-gray-100 rounded mb-2 flex items-center justify-center">
                    <span className="text-xs text-gray-500">Logo</span>
                  </div>
                  <h4 className="font-semibold text-sm">Top Right</h4>
                  <p className="text-xs text-gray-600">Modern, clean alignment</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Color Schemes */}
        <section id="color-schemes" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Color Schemes and Branding</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Colors play a crucial role in brand recognition and invoice readability. Learn how to 
            implement your brand colors effectively while maintaining professionalism.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Palette className="w-6 h-6 mr-2 text-purple-600" />
                Color Customization Options
              </h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Primary Elements</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <span className="text-sm">Header Background</span>
                      <div className="w-8 h-8 bg-blue-600 rounded border"></div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <span className="text-sm">Primary Text</span>
                      <div className="w-8 h-8 bg-gray-900 rounded border"></div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <span className="text-sm">Accent Color</span>
                      <div className="w-8 h-8 bg-purple-600 rounded border"></div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Secondary Elements</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <span className="text-sm">Table Headers</span>
                      <div className="w-8 h-8 bg-gray-100 rounded border"></div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <span className="text-sm">Borders</span>
                      <div className="w-8 h-8 bg-gray-300 rounded border"></div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <span className="text-sm">Background</span>
                      <div className="w-8 h-8 bg-white rounded border"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Pre-designed Color Schemes</h3>
            <p className="text-gray-700 mb-4">
              Choose from our professionally designed color schemes or create your own:
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="flex h-8 rounded mb-2">
                  <div className="w-1/3 bg-blue-600 rounded-l"></div>
                  <div className="w-1/3 bg-blue-400"></div>
                  <div className="w-1/3 bg-blue-200 rounded-r"></div>
                </div>
                <span className="text-xs text-gray-600">Professional Blue</span>
              </div>
              <div className="text-center">
                <div className="flex h-8 rounded mb-2">
                  <div className="w-1/3 bg-green-600 rounded-l"></div>
                  <div className="w-1/3 bg-green-400"></div>
                  <div className="w-1/3 bg-green-200 rounded-r"></div>
                </div>
                <span className="text-xs text-gray-600">Corporate Green</span>
              </div>
              <div className="text-center">
                <div className="flex h-8 rounded mb-2">
                  <div className="w-1/3 bg-purple-600 rounded-l"></div>
                  <div className="w-1/3 bg-purple-400"></div>
                  <div className="w-1/3 bg-purple-200 rounded-r"></div>
                </div>
                <span className="text-xs text-gray-600">Creative Purple</span>
              </div>
              <div className="text-center">
                <div className="flex h-8 rounded mb-2">
                  <div className="w-1/3 bg-gray-800 rounded-l"></div>
                  <div className="w-1/3 bg-gray-600"></div>
                  <div className="w-1/3 bg-gray-300 rounded-r"></div>
                </div>
                <span className="text-xs text-gray-600">Modern Gray</span>
              </div>
            </div>
          </div>
        </section>

        {/* Typography */}
        <section id="typography" className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Typography and Fonts</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Typography affects both readability and brand perception. Choose fonts that align with 
            your brand while ensuring your invoices remain professional and easy to read.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Type className="w-6 h-6 mr-2 text-purple-600" />
                Font Categories and Usage
              </h3>
              
              <div className="grid md:grid-cols-3 gap-6">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold mb-2" style={{fontFamily: 'serif'}}>Serif Fonts</h4>
                  <p className="text-sm text-gray-600 mb-3">Traditional, trustworthy</p>
                  <ul className="text-sm space-y-1">
                    <li style={{fontFamily: 'serif'}}>Times New Roman</li>
                    <li style={{fontFamily: 'serif'}}>Georgia</li>
                    <li style={{fontFamily: 'serif'}}>Playfair Display</li>
                  </ul>
                  <p className="text-xs text-gray-500 mt-2">Best for: Law firms, traditional businesses</p>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold mb-2">Sans-Serif Fonts</h4>
                  <p className="text-sm text-gray-600 mb-3">Modern, clean, readable</p>
                  <ul className="text-sm space-y-1">
                    <li>Arial</li>
                    <li>Helvetica</li>
                    <li>Inter</li>
                  </ul>
                  <p className="text-xs text-gray-500 mt-2">Best for: Tech, modern businesses</p>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold mb-2" style={{fontFamily: 'monospace'}}>Monospace Fonts</h4>
                  <p className="text-sm text-gray-600 mb-3">Technical, precise</p>
                  <ul className="text-sm space-y-1" style={{fontFamily: 'monospace'}}>
                    <li>Courier New</li>
                    <li>Monaco</li>
                  </ul>
                  <p className="text-xs text-gray-500 mt-2">Best for: Technical invoices</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <div className="mt-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-8 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">Ready to Customize Your Templates?</h2>
          <p className="text-xl mb-8 text-purple-100">
            Start creating beautiful, branded invoices that impress your clients.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/templates"
              className="inline-flex items-center px-8 py-4 bg-white text-purple-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Browse Templates
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
            <Link
              href="/create-invoice"
              className="inline-flex items-center px-8 py-4 bg-purple-700 text-white rounded-lg font-semibold hover:bg-purple-800 transition-colors"
            >
              Create Invoice
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
          </div>
        </div>
      </div>
    </main>
  )
}