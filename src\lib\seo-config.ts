// Comprehensive SEO configuration for the invoice generator

export const siteConfig = {
  name: 'Invoice Generator',
  title: 'Free Invoice Generator | Create Professional Invoices Online',
  description: 'Create professional invoices instantly with our AI-powered invoice generator. Free templates for freelancers, consultants, and small businesses. Generate and send invoices in minutes.',
  url: process.env.NEXT_PUBLIC_BASE_URL || 'https://templateinvoices.com',
  locale: 'en-US',
  type: 'website',
}

// Target keywords for SEO
export const targetKeywords = {
  primary: ['invoice generator', 'invoice template', 'create invoice', 'invoice maker'],
  secondary: ['AI invoice generator', 'professional invoice maker', 'small business invoicing', 'free invoice creator'],
  longTail: [
    'invoice generator for freelancers',
    'photography invoice template',
    'consulting invoice creator',
    'web development invoice template',
    'graphic design invoice maker',
    'contractor invoice generator',
    'small business invoice template free',
    'professional invoice template download'
  ]
}

// Page-specific SEO metadata
export const pageSEO = {
  home: {
    title: 'Free Invoice Generator | Create Professional Invoices Online',
    description: 'Create professional invoices instantly with our AI-powered invoice generator. Free templates for freelancers, consultants, and small businesses. Generate and send invoices in minutes.',
    keywords: targetKeywords.primary.concat(targetKeywords.secondary),
    canonical: '/'
  },
  templates: {
    title: 'Professional Invoice Templates | Industry-Specific Designs',
    description: 'Choose from our collection of professional invoice templates designed for various industries. Customizable templates for freelancers, photographers, consultants, and more.',
    keywords: ['invoice templates', 'professional invoice designs', 'industry-specific invoices', 'customizable invoice templates'],
    canonical: '/templates'
  },
  create: {
    title: 'Create Invoice | AI-Powered Invoice Generator',
    description: 'Generate professional invoices in seconds with our AI-powered invoice creator. Add your business details, items, and send directly to clients. Free to use.',
    keywords: ['create invoice', 'invoice generator', 'AI invoice maker', 'online invoice creator'],
    canonical: '/create-invoice'
  },
  pricing: {
    title: 'Pricing | Affordable Invoice Solutions for Every Business',
    description: 'Choose the perfect plan for your invoicing needs. Free forever plan available. Upgrade for unlimited invoices, premium templates, and advanced features.',
    keywords: ['invoice generator pricing', 'invoicing software cost', 'free invoice maker', 'invoice plan pricing'],
    canonical: '/pricing'
  },
  dashboard: {
    title: 'Dashboard | Manage Your Invoices & Business Insights',
    description: 'Access your invoice dashboard to manage invoices, track payments, view analytics, and monitor your business performance. Complete business overview.',
    keywords: ['invoice dashboard', 'business analytics', 'invoice management', 'payment tracking', 'business insights'],
    canonical: '/dashboard'
  }
}

// Industry-specific SEO metadata
export const industrySEO = {
  freelancer: {
    title: 'Freelancer Invoice Template | Professional & Free',
    description: 'Create professional freelance invoices in minutes. Perfect for consultants, writers, designers, and independent contractors. Track projects and get paid faster.',
    keywords: ['freelancer invoice template', 'freelance invoice generator', 'contractor invoice maker', 'independent contractor invoicing'],
    canonical: '/invoice-template/freelancer'
  },
  photography: {
    title: 'Photography Invoice Template | Professional & Free',
    description: 'Professional invoice templates designed for photographers. Include session details, image licensing, and package pricing. Create stunning invoices that match your brand.',
    keywords: ['photography invoice template', 'photographer invoice generator', 'photo shoot invoice', 'wedding photography invoice'],
    canonical: '/invoice-template/photography'
  },
  consulting: {
    title: 'Consulting Invoice Template | Professional & Free',
    description: 'Create professional consulting invoices with detailed project breakdowns. Perfect for management consultants, business advisors, and professional services.',
    keywords: ['consulting invoice template', 'consultant invoice generator', 'professional services invoice', 'advisory invoice template'],
    canonical: '/invoice-template/consulting'
  },
  webDevelopment: {
    title: 'Web Development Invoice Template | Professional & Free',
    description: 'Invoice templates designed for web developers and agencies. Include project milestones, hourly rates, and technical specifications. Get paid for your code.',
    keywords: ['web development invoice template', 'developer invoice generator', 'programming invoice', 'software development invoice'],
    canonical: '/invoice-template/web-development'
  },
  graphicDesign: {
    title: 'Graphic Design Invoice Template | Professional & Free',
    description: 'Beautiful invoice templates for graphic designers. Showcase your creative work while maintaining professional billing. Include revisions and usage rights.',
    keywords: ['graphic design invoice template', 'designer invoice generator', 'creative invoice template', 'design agency invoice'],
    canonical: '/invoice-template/graphic-design'
  }
}

// Open Graph and Twitter Card configuration
export const socialMediaConfig = {
  openGraph: {
    type: 'website',
    siteName: 'Invoice Generator',
    images: [
      {
        url: '/images/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Invoice Generator - Create Professional Invoices Online'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    site: '@invoicegenerator',
    creator: '@invoicegenerator'
  }
}

// Structured data schemas
export const structuredData = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Invoice Generator',
    url: siteConfig.url,
    logo: `${siteConfig.url}/images/logo/logo.svg`,
    sameAs: [
      'https://twitter.com/invoicegenerator',
      'https://facebook.com/invoicegenerator',
      'https://linkedin.com/company/invoicegenerator'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      email: '<EMAIL>',
      contactType: 'customer support',
      availableLanguage: ['English']
    }
  },
  webApplication: {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: 'Invoice Generator',
    description: siteConfig.description,
    url: siteConfig.url,
    applicationCategory: 'BusinessApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      description: 'Free plan available with premium upgrades'
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '1250'
    }
  },
  faq: {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: [
      {
        '@type': 'Question',
        name: 'Is the invoice generator really free?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes! You can create up to 5 invoices per month absolutely free. No credit card required. Upgrade to premium for unlimited invoices and advanced features.'
        }
      },
      {
        '@type': 'Question',
        name: 'Can I customize the invoice templates?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Absolutely! All our templates are fully customizable. You can add your logo, change colors, modify fields, and adjust the layout to match your brand.'
        }
      },
      {
        '@type': 'Question',
        name: 'How do I send invoices to clients?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'You can send invoices directly from our platform via email, download as PDF, or share a secure link. Your clients can view and pay invoices online.'
        }
      },
      {
        '@type': 'Question',
        name: 'What industries are the templates designed for?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'We have specialized templates for freelancers, photographers, consultants, web developers, graphic designers, contractors, and many other industries.'
        }
      },
      {
        '@type': 'Question',
        name: 'Is my data secure?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes, we use industry-standard encryption and security measures to protect your data. All invoices are stored securely and only accessible by you.'
        }
      }
    ]
  }
}

// Generate meta tags for a page
export function generateMetaTags(page: string, customData?: any) {
  const pageData = pageSEO[page as keyof typeof pageSEO] || pageSEO.home
  const data = { ...pageData, ...customData }
  
  return {
    title: data.title,
    description: data.description,
    keywords: data.keywords?.join(', '),
    openGraph: {
      title: data.title,
      description: data.description,
      url: `${siteConfig.url}${data.canonical}`,
      ...socialMediaConfig.openGraph
    },
    twitter: {
      title: data.title,
      description: data.description,
      ...socialMediaConfig.twitter
    },
    alternates: {
      canonical: `${siteConfig.url}${data.canonical}`
    }
  }
}

// Generate structured data for a specific type
export function generateStructuredData(type: keyof typeof structuredData, additionalData?: any) {
  const baseData = structuredData[type]
  return {
    ...baseData,
    ...additionalData
  }
}

// SEO-friendly URL generation
export function generateSEOUrl(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

// Generate breadcrumb schema
export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${siteConfig.url}${item.url}`
    }))
  }
}