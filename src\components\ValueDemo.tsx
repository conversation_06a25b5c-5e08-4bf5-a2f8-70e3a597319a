'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  Zap,
  TrendingUp,
  ArrowRight,
  DollarSign,
  Sparkles,
  AlertCircle,
  Send,
  Calendar
} from 'lucide-react'
import { Button } from '@/components/ui/Button'

const businessTypes = {
  freelancer: {
    label: 'Freelancer',
    fields: ['Project Name', 'Hourly Rate', 'Hours Worked', 'Deliverables'],
    example: 'Website Redesign - $3,500'
  },
  consultant: {
    label: 'Consultant',
    fields: ['Consultation Type', 'Billable Hours', 'Expenses', 'Recommendations'],
    example: 'Marketing Strategy - $5,200'
  },
  photographer: {
    label: 'Photographer',
    fields: ['Event Type', 'Package Details', 'Usage Rights', 'Delivery Timeline'],
    example: 'Wedding Photography - $4,800'
  },
  developer: {
    label: 'Developer',
    fields: ['Project Scope', 'Technologies', 'Milestones', 'Support Period'],
    example: 'Mobile App Development - $12,000'
  }
}

export default function ValueDemo() {
  const [selectedBusiness, setSelectedBusiness] = useState<keyof typeof businessTypes>('freelancer')
  const [demoStep, setDemoStep] = useState(0)
  const [inputValue, setInputValue] = useState('')
  const [showResults, setShowResults] = useState(false)

  const handleTryDemo = () => {
    if (inputValue.trim()) {
      setShowResults(true)
      // Simulate AI processing
      setTimeout(() => {
        setDemoStep(1)
      }, 1000)
    }
  }

  return (
    <section className="py-20 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            See the difference in{' '}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              60 seconds
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Stop wasting hours on invoices that get paid late. Let AI create professional invoices that get you paid 3x faster.
          </p>
        </motion.div>

        {/* Problem/Solution Split Screen */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {/* Old Way */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="bg-red-50 rounded-xl p-8 border border-red-100"
          >
            <div className="flex items-center gap-2 mb-6">
              <XCircle className="w-6 h-6 text-red-600" />
              <h3 className="text-2xl font-bold text-gray-900">The Old Way</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <p className="font-medium text-gray-900">Manual invoice creation</p>
                  <p className="text-sm text-gray-600">30+ minutes per invoice, prone to errors</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <p className="font-medium text-gray-900">Generic templates</p>
                  <p className="text-sm text-gray-600">Look unprofessional, don't match your brand</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <p className="font-medium text-gray-900">Awkward payment follow-ups</p>
                  <p className="text-sm text-gray-600">Damage client relationships, still get paid late</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <p className="font-medium text-gray-900">45+ day payment cycles</p>
                  <p className="text-sm text-gray-600">Cash flow problems, can't grow your business</p>
                </div>
              </div>
            </div>

            {/* Visual representation */}
            <div className="mt-6 bg-white/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Average payment time</span>
                <span className="text-red-600 font-bold">45 days</span>
              </div>
              <div className="h-2 bg-red-200 rounded-full">
                <div className="h-full w-full bg-red-400 rounded-full" />
              </div>
            </div>
          </motion.div>

          {/* Our Way */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="bg-green-50 rounded-xl p-8 border border-green-100"
          >
            <div className="flex items-center gap-2 mb-6">
              <CheckCircle className="w-6 h-6 text-green-600" />
              <h3 className="text-2xl font-bold text-gray-900">Our Way</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <p className="font-medium text-gray-900">AI-powered generation</p>
                  <p className="text-sm text-gray-600">Professional invoices in under 60 seconds</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <p className="font-medium text-gray-900">Industry-specific templates</p>
                  <p className="text-sm text-gray-600">Tailored to your business, builds trust instantly</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <p className="font-medium text-gray-900">Automated reminders</p>
                  <p className="text-sm text-gray-600">Professional follow-ups that preserve relationships</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <p className="font-medium text-gray-900">14-day payment average</p>
                  <p className="text-sm text-gray-600">Consistent cash flow, grow your business faster</p>
                </div>
              </div>
            </div>

            {/* Visual representation */}
            <div className="mt-6 bg-white/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Average payment time</span>
                <span className="text-green-600 font-bold">14 days</span>
              </div>
              <div className="h-2 bg-green-200 rounded-full">
                <div className="h-full w-1/3 bg-green-400 rounded-full" />
              </div>
            </div>
          </motion.div>
        </div>

        {/* Interactive Demo */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="bg-white rounded-2xl shadow-xl p-8 md:p-12 max-w-4xl mx-auto"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Try it yourself</h3>
            <p className="text-gray-600">See how AI customizes invoices for your business</p>
          </div>

          {/* Business Type Selector */}
          <div className="mb-8">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              I'm a...
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {Object.entries(businessTypes).map(([key, type]) => (
                <button
                  key={key}
                  onClick={() => {
                    setSelectedBusiness(key as keyof typeof businessTypes)
                    setShowResults(false)
                    setDemoStep(0)
                  }}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    selectedBusiness === key
                      ? 'border-blue-600 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {type.label}
                </button>
              ))}
            </div>
          </div>

          {/* Project Input */}
          <div className="mb-8">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Describe your project
            </label>
            <div className="flex gap-3">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleTryDemo()}
                placeholder={`e.g., ${businessTypes[selectedBusiness].example}`}
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <Button
                onClick={handleTryDemo}
                disabled={!inputValue.trim()}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Generate Invoice
              </Button>
            </div>
          </div>

          {/* Results */}
          <AnimatePresence>
            {showResults && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-6"
              >
                {/* AI Processing Animation */}
                {demoStep === 0 && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-8"
                  >
                    <div className="inline-flex items-center gap-3 text-blue-600">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Sparkles className="w-6 h-6" />
                      </motion.div>
                      <span className="text-lg font-medium">AI is customizing your invoice...</span>
                    </div>
                  </motion.div>
                )}

                {/* Generated Fields */}
                {demoStep === 1 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-4"
                  >
                    <div className="flex items-center gap-2 mb-4">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      <span className="font-medium text-gray-900">
                        AI detected: {businessTypes[selectedBusiness].label} project
                      </span>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      {businessTypes[selectedBusiness].fields.map((field, index) => (
                        <motion.div
                          key={field}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="bg-gray-50 rounded-lg p-4"
                        >
                          <p className="text-sm text-gray-600 mb-1">{field}</p>
                          <p className="font-medium text-gray-900">
                            AI-generated content for {field.toLowerCase()}
                          </p>
                        </motion.div>
                      ))}
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div>
                        <p className="text-sm text-gray-600">Time saved</p>
                        <p className="text-2xl font-bold text-green-600">28 minutes</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Professional score</p>
                        <p className="text-2xl font-bold text-blue-600">98%</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Payment speed</p>
                        <p className="text-2xl font-bold text-purple-600">3x faster</p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Benefit Cards */}
        <div className="grid md:grid-cols-4 gap-6 mt-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-center"
          >
            <motion.div
              whileHover={{ scale: 1.1 }}
              className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <Zap className="w-8 h-8 text-blue-600" />
            </motion.div>
            <h4 className="text-xl font-bold text-gray-900 mb-2">Get paid 3x faster</h4>
            <p className="text-gray-600">Average payment in 14 days vs 45 days</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-center"
          >
            <motion.div
              whileHover={{ scale: 1.1 }}
              className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <Clock className="w-8 h-8 text-green-600" />
            </motion.div>
            <h4 className="text-xl font-bold text-gray-900 mb-2">Save 4 hours/week</h4>
            <p className="text-gray-600">Automated invoicing and follow-ups</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
            className="text-center"
          >
            <motion.div
              whileHover={{ scale: 1.1 }}
              className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </motion.div>
            <h4 className="text-xl font-bold text-gray-900 mb-2">Look professional</h4>
            <p className="text-gray-600">Industry-specific templates that impress</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
            className="text-center"
          >
            <motion.div
              whileHover={{ scale: 1.1 }}
              className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <DollarSign className="w-8 h-8 text-pink-600" />
            </motion.div>
            <h4 className="text-xl font-bold text-gray-900 mb-2">Never chase again</h4>
            <p className="text-gray-600">Automated reminders maintain relationships</p>
          </motion.div>
        </div>
      </div>
    </section>
  )
}