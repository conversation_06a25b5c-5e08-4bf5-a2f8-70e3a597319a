'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Check, X, Zap, TrendingUp, Clock, Shield } from 'lucide-react';
// Removed direct import of invoice-limits to avoid MongoDB client-side imports

export default function UpgradePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [usage, setUsage] = useState<{
    used: number;
    limit: number;
    remaining: number;
    percentage: number;
    daysUntilReset: number;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  // Load current usage
  useEffect(() => {
    if (session?.user?.id) {
      loadUsageData();
    }
  }, [session?.user?.id]);

  const loadUsageData = async () => {
    try {
      const response = await fetch('/api/invoices/usage');
      if (!response.ok) {
        throw new Error('Failed to fetch usage data');
      }
      const stats = await response.json();
      const { used, limit, percentage, daysUntilReset } = stats.currentMonth;
      setUsage({
        used,
        limit,
        remaining: Math.max(0, limit - used),
        percentage,
        daysUntilReset
      });
    } catch (error) {
      console.error('Error loading usage data:', error);
      // Use fallback data if API fails
      setUsage({
        used: 2,
        limit: 3,
        remaining: 1,
        percentage: 67,
        daysUntilReset: 15
      });
    }
  };

  const handleUpgrade = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Track upgrade click
      await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'upgrade_clicked',
          properties: {
            source: 'upgrade_page',
            currentUsage: usage?.used,
            limit: usage?.limit
          }
        })
      });

      // Create Stripe checkout session
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          priceId: process.env.NEXT_PUBLIC_STRIPE_PRO_PRICE_ID,
          source: 'upgrade_page',
          metadata: {
            userId: session?.user.id,
            currentPlan: 'free',
            invoicesUsed: usage?.used
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }

      const { url } = await response.json();
      
      // Redirect to Stripe Checkout
      window.location.href = url;
    } catch (error) {
      console.error('Upgrade error:', error);
      setError('Something went wrong. Please try again.');
      setIsLoading(false);
    }
  };

  if (status === 'loading' || !usage) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If already Pro, redirect to dashboard
  if (session?.user?.subscription?.plan === 'pro') {
    router.push('/dashboard');
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-xl font-semibold text-gray-900">Upgrade to Pro</h1>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-gray-500 hover:text-gray-700"
            >
              ← Back to Dashboard
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Current Usage Card */}
        <div className="mb-12">
          <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Current Usage</h2>
            
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Monthly Invoices</span>
                <span className="text-lg font-semibold text-gray-900">
                  {usage.used} / {usage.limit}
                </span>
              </div>
              
              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className={`h-3 rounded-full transition-all duration-500 ${
                    usage.percentage >= 100 ? 'bg-red-500' : 
                    usage.percentage >= 67 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(usage.percentage, 100)}%` }}
                />
              </div>
              
              <div className="flex justify-between items-center mt-2">
                <span className="text-sm text-gray-500">
                  {usage.remaining > 0 
                    ? `${usage.remaining} invoice${usage.remaining === 1 ? '' : 's'} remaining`
                    : 'Monthly limit reached'
                  }
                </span>
                <span className="text-sm text-gray-500">
                  Resets in {usage.daysUntilReset} days
                </span>
              </div>
            </div>

            {usage.percentage >= 67 && (
              <div className={`p-4 rounded-lg ${
                usage.percentage >= 100 ? 'bg-red-50 border border-red-200' : 'bg-yellow-50 border border-yellow-200'
              }`}>
                <p className={`text-sm ${
                  usage.percentage >= 100 ? 'text-red-700' : 'text-yellow-700'
                }`}>
                  {usage.percentage >= 100 
                    ? "You've reached your monthly limit. Upgrade to Pro to continue creating invoices."
                    : `You're approaching your monthly limit. Upgrade now to ensure uninterrupted access.`
                  }
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Pricing Section */}
        <div className="mb-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Simple, Transparent Pricing</h2>
            <p className="text-lg text-gray-600">Unlock unlimited invoices and premium features</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Plan */}
            <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-200 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-gray-100 text-gray-700 px-4 py-1 rounded-full text-sm font-medium">
                  Current Plan
                </span>
              </div>
              
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Free</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold">$0</span>
                <span className="text-gray-600">/month</span>
              </div>
              
              <ul className="space-y-4 mb-8">
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">3 invoices per month</span>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Basic invoice templates</span>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">PDF export</span>
                </li>
                <li className="flex items-start">
                  <X className="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-400">Client management</span>
                </li>
                <li className="flex items-start">
                  <X className="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-400">Invoice analytics</span>
                </li>
                <li className="flex items-start">
                  <X className="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-400">Priority support</span>
                </li>
              </ul>
              
              <button 
                disabled
                className="w-full py-3 px-6 bg-gray-100 text-gray-400 rounded-lg font-medium cursor-not-allowed"
              >
                Your Current Plan
              </button>
            </div>

            {/* Pro Plan */}
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl shadow-xl p-8 text-white relative transform hover:scale-105 transition-transform">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-yellow-400 text-gray-900 px-4 py-1 rounded-full text-sm font-bold">
                  RECOMMENDED
                </span>
              </div>
              
              <h3 className="text-2xl font-bold mb-2">Pro</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold">$9.99</span>
                <span className="text-blue-100">/month</span>
              </div>
              
              <ul className="space-y-4 mb-8">
                <li className="flex items-start">
                  <Zap className="w-5 h-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="font-medium">Unlimited invoices</span>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-white mr-3 mt-0.5 flex-shrink-0" />
                  <span>All premium templates</span>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-white mr-3 mt-0.5 flex-shrink-0" />
                  <span>Advanced PDF customization</span>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-white mr-3 mt-0.5 flex-shrink-0" />
                  <span>Save & manage clients</span>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-white mr-3 mt-0.5 flex-shrink-0" />
                  <span>Invoice analytics & insights</span>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-white mr-3 mt-0.5 flex-shrink-0" />
                  <span>Priority email support</span>
                </li>
              </ul>
              
              <button 
                onClick={handleUpgrade}
                disabled={isLoading}
                className="w-full py-3 px-6 bg-white text-blue-600 rounded-lg font-bold hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Processing...' : 'Upgrade to Pro →'}
              </button>
            </div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Why Upgrade to Pro?
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <Zap className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Never Hit Limits
              </h3>
              <p className="text-gray-600">
                Create as many invoices as your business needs. No monthly caps, no restrictions.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Grow Your Business
              </h3>
              <p className="text-gray-600">
                Track payments, analyze revenue trends, and make data-driven decisions.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <Clock className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Save Time
              </h3>
              <p className="text-gray-600">
                Reuse client info, duplicate invoices, and streamline your billing workflow.
              </p>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="text-center">
          <div className="inline-flex items-center space-x-6 text-sm text-gray-600">
            <div className="flex items-center">
              <Shield className="w-4 h-4 mr-2 text-green-500" />
              <span>Secure payments via Stripe</span>
            </div>
            <div className="flex items-center">
              <Check className="w-4 h-4 mr-2 text-green-500" />
              <span>Cancel anytime</span>
            </div>
            <div className="flex items-center">
              <Zap className="w-4 h-4 mr-2 text-green-500" />
              <span>Instant activation</span>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-center">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
}