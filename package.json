{"name": "template-invoice", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "test:stripe": "npx ts-node scripts/test-stripe-mode.ts"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@next-auth/mongodb-adapter": "^1.1.3", "@types/nodemailer": "^6.4.17", "@types/puppeteer": "^5.4.7", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "buffer": "^6.0.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "critters": "^0.0.23", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "dns": "^0.1.2", "framer-motion": "^10.16.0", "html-pdf-node": "^1.0.7", "lucide-react": "^0.294.0", "mongodb": "^5.9.2", "next": "^15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "puppeteer": "^24.9.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "stream-browserify": "^3.0.0", "stripe": "^14.0.0", "swiper": "^11.2.8", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.0.2", "zod": "^3.25.42"}, "devDependencies": {"@types/date-fns": "^2.5.3", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.5.4", "styled-jsx": "^5.1.7", "tailwindcss": "^3.4.17", "typescript": "^5.0.0"}}