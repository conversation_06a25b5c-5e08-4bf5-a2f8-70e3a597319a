// Client-safe analytics functions that call API endpoints instead of directly accessing MongoDB

// Enhanced event categories for comprehensive tracking
export type EventCategory = 
  | 'user'
  | 'invoice'
  | 'payment'
  | 'ai_template'
  | 'subscription'
  | 'system'
  | 'error'
  | 'performance'
  | 'onboarding'
  | 'email'
  | 'pdf';

export type EventAction = 
  | 'signup'
  | 'login'
  | 'logout'
  | 'create'
  | 'update'
  | 'delete'
  | 'view'
  | 'download'
  | 'send'
  | 'generate'
  | 'complete'
  | 'fail'
  | 'upgrade'
  | 'downgrade'
  | 'click'
  | 'error'
  | 'started'
  | 'abandoned'
  | 'retry'
  | 'timeout';

// Client-safe tracking function that sends events to API
export async function trackEvent(
  category: EventCategory,
  action: EventAction,
  data?: {
    userId?: string;
    label?: string;
    value?: number;
    metadata?: Record<string, any>;
    performance?: {
      duration?: number;
      status?: 'success' | 'error' | 'timeout';
      errorMessage?: string;
    };
  }
): Promise<void> {
  try {
    // Send to API endpoint instead of directly to MongoDB
    await fetch('/api/analytics/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        category,
        action,
        ...data,
        timestamp: new Date().toISOString()
      }),
    });
  } catch (error) {
    console.error('Error tracking analytics event:', error);
  }
}

// Other client-safe tracking functions
export async function trackUserJourney(
  userId: string,
  event: 'signup' | 'first_invoice' | 'first_payment' | 'upgrade',
  metadata?: Record<string, any>
): Promise<void> {
  const categoryMap = {
    signup: 'user',
    first_invoice: 'invoice',
    first_payment: 'payment',
    upgrade: 'subscription'
  };

  await trackEvent(categoryMap[event] as EventCategory, 'complete', {
    userId,
    label: event,
    metadata: {
      ...metadata,
      journey_event: event,
      timestamp: new Date()
    }
  });
}