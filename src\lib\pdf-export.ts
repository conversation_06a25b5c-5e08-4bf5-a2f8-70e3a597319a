// PDF Export Service for Template Invoice System
import puppeteer from 'puppeteer';
import { InvoiceDocument } from './models';

// PDF generation configuration
const PDF_CONFIG = {
  format: 'A4' as const,
  margin: {
    top: '20mm',
    right: '20mm',
    bottom: '20mm',
    left: '20mm'
  },
  printBackground: true,
  displayHeaderFooter: false,
  preferCSSPageSize: true
};

// Logo configuration
const LOGO_CONFIG = {
  maxWidth: '200px',
  maxHeight: '80px',
  fallbackText: 'Company Logo'
};

// Generate professional invoice HTML
export function generateInvoiceHTML(invoice: InvoiceDocument, logoUrl?: string): string {
  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Invoice ${invoice.invoiceNumber}</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          line-height: 1.6;
          color: #111827;
          background: white;
          font-size: 14px;
        }
        
        .invoice-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 40px;
          background: white;
        }
        
        .header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 60px;
          border-bottom: 2px solid #e5e7eb;
          padding-bottom: 30px;
        }
        
        .logo-section {
          flex: 1;
        }
        
        .logo {
          max-width: ${LOGO_CONFIG.maxWidth};
          max-height: ${LOGO_CONFIG.maxHeight};
          object-fit: contain;
        }
        
        .logo-fallback {
          font-size: 24px;
          font-weight: 700;
          color: #1f2937;
        }
        
        .invoice-title {
          text-align: right;
          flex: 1;
        }
        
        .invoice-title h1 {
          font-size: 48px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 10px;
        }
        
        .invoice-number {
          font-size: 18px;
          color: #6b7280;
          font-weight: 500;
        }
        
        .parties {
          display: flex;
          justify-content: space-between;
          margin-bottom: 50px;
        }
        
        .party {
          flex: 1;
        }
        
        .party:last-child {
          text-align: right;
        }
        
        .party-title {
          font-size: 12px;
          text-transform: uppercase;
          font-weight: 600;
          color: #6b7280;
          margin-bottom: 15px;
          letter-spacing: 0.5px;
        }
        
        .party-info {
          font-size: 14px;
          line-height: 1.8;
        }
        
        .party-name {
          font-weight: 600;
          font-size: 16px;
          color: #1f2937;
          margin-bottom: 5px;
        }
        
        .invoice-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 50px;
          background: #f9fafb;
          padding: 25px;
          border-radius: 8px;
        }
        
        .detail-group {
          text-align: center;
        }
        
        .detail-label {
          font-size: 12px;
          text-transform: uppercase;
          font-weight: 600;
          color: #6b7280;
          margin-bottom: 8px;
          letter-spacing: 0.5px;
        }
        
        .detail-value {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
        }
        
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 40px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          overflow: hidden;
        }
        
        .items-table th {
          background: #f3f4f6;
          padding: 20px 15px;
          text-align: left;
          font-weight: 600;
          font-size: 12px;
          text-transform: uppercase;
          color: #374151;
          letter-spacing: 0.5px;
          border-bottom: 1px solid #e5e7eb;
        }
        
        .items-table th:last-child {
          text-align: right;
        }
        
        .items-table td {
          padding: 20px 15px;
          border-bottom: 1px solid #f3f4f6;
          vertical-align: top;
        }
        
        .items-table td:last-child {
          text-align: right;
          font-weight: 500;
        }
        
        .items-table tbody tr:last-child td {
          border-bottom: none;
        }
        
        .item-description {
          font-weight: 500;
          color: #1f2937;
          margin-bottom: 5px;
        }
        
        .item-details {
          color: #6b7280;
          font-size: 12px;
        }
        
        .totals {
          margin-left: auto;
          width: 300px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          overflow: hidden;
        }
        
        .total-row {
          display: flex;
          justify-content: space-between;
          padding: 15px 20px;
          border-bottom: 1px solid #f3f4f6;
        }
        
        .total-row:last-child {
          border-bottom: none;
          background: #1f2937;
          color: white;
          font-weight: 600;
          font-size: 18px;
        }
        
        .total-row.subtotal {
          background: #f9fafb;
        }
        
        .total-label {
          font-weight: 500;
        }
        
        .total-value {
          font-weight: 600;
        }
        
        .notes {
          margin-top: 50px;
          padding-top: 30px;
          border-top: 1px solid #e5e7eb;
        }
        
        .notes-title {
          font-weight: 600;
          margin-bottom: 15px;
          color: #1f2937;
        }
        
        .notes-content {
          color: #6b7280;
          line-height: 1.8;
        }
        
        .footer {
          margin-top: 60px;
          padding-top: 30px;
          border-top: 2px solid #e5e7eb;
          text-align: center;
          color: #6b7280;
          font-size: 12px;
        }
        
        @media print {
          .invoice-container {
            padding: 0;
            margin: 0;
            max-width: none;
          }
          
          body {
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
          }
        }
        
        @page {
          margin: 20mm;
          size: A4;
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Header -->
        <div class="header">
          <div class="logo-section">
            ${logoUrl ? 
              `<img src="${logoUrl}" alt="Company Logo" class="logo" />` : 
              `<div class="logo-fallback">${invoice.businessInfo.name}</div>`
            }
          </div>
          <div class="invoice-title">
            <h1>INVOICE</h1>
            <div class="invoice-number">${invoice.invoiceNumber}</div>
          </div>
        </div>

        <!-- Parties -->
        <div class="parties">
          <div class="party">
            <div class="party-title">From</div>
            <div class="party-info">
              <div class="party-name">${invoice.businessInfo.name}</div>
              ${invoice.businessInfo.email ? `<div>${invoice.businessInfo.email}</div>` : ''}
              ${invoice.businessInfo.phone ? `<div>${invoice.businessInfo.phone}</div>` : ''}
              ${invoice.businessInfo.address ? `<div>${invoice.businessInfo.address}</div>` : ''}
              ${invoice.businessInfo.city ? `<div>${invoice.businessInfo.city}</div>` : ''}
            </div>
          </div>
          
          <div class="party">
            <div class="party-title">Bill To</div>
            <div class="party-info">
              <div class="party-name">${invoice.clientInfo.name}</div>
              ${invoice.clientInfo.email ? `<div>${invoice.clientInfo.email}</div>` : ''}
              ${invoice.clientInfo.address ? `<div>${invoice.clientInfo.address}</div>` : ''}
              ${invoice.clientInfo.city ? `<div>${invoice.clientInfo.city}</div>` : ''}
              ${invoice.clientInfo.contactPerson ? `<div>Attn: ${invoice.clientInfo.contactPerson}</div>` : ''}
            </div>
          </div>
        </div>

        <!-- Invoice Details -->
        <div class="invoice-details">
          <div class="detail-group">
            <div class="detail-label">Issue Date</div>
            <div class="detail-value">${formatDate(invoice.createdAt)}</div>
          </div>
          <div class="detail-group">
            <div class="detail-label">Due Date</div>
            <div class="detail-value">${formatDate(invoice.dueDate)}</div>
          </div>
          <div class="detail-group">
            <div class="detail-label">Status</div>
            <div class="detail-value" style="text-transform: capitalize;">${invoice.status}</div>
          </div>
        </div>

        <!-- Line Items -->
        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 50%;">Description</th>
              <th style="width: 15%;">Quantity</th>
              <th style="width: 15%;">Rate</th>
              <th style="width: 20%;">Amount</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.lineItems.map(item => `
              <tr>
                <td>
                  <div class="item-description">${item.description}</div>
                  ${item.details ? `<div class="item-details">${item.details}</div>` : ''}
                </td>
                <td>${item.quantity}</td>
                <td>${formatCurrency(item.rate, invoice.currency)}</td>
                <td>${formatCurrency(item.amount, invoice.currency)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <!-- Totals -->
        <div class="totals">
          <div class="total-row subtotal">
            <span class="total-label">Subtotal</span>
            <span class="total-value">${formatCurrency(invoice.totals.subtotal, invoice.currency)}</span>
          </div>
          ${invoice.totals.tax > 0 ? `
            <div class="total-row">
              <span class="total-label">Tax</span>
              <span class="total-value">${formatCurrency(invoice.totals.tax, invoice.currency)}</span>
            </div>
          ` : ''}
          ${invoice.totals.discount > 0 ? `
            <div class="total-row">
              <span class="total-label">Discount</span>
              <span class="total-value">-${formatCurrency(invoice.totals.discount, invoice.currency)}</span>
            </div>
          ` : ''}
          <div class="total-row">
            <span class="total-label">Total</span>
            <span class="total-value">${formatCurrency(invoice.totals.total, invoice.currency)}</span>
          </div>
        </div>

        <!-- Notes -->
        ${invoice.notes ? `
          <div class="notes">
            <div class="notes-title">Notes</div>
            <div class="notes-content">${invoice.notes.replace(/\n/g, '<br>')}</div>
          </div>
        ` : ''}

        <!-- Footer -->
        <div class="footer">
          <div>Thank you for your business!</div>
          <div style="margin-top: 10px;">
            This invoice was generated on ${formatDate(new Date())}
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Generate PDF from invoice data
export async function generateInvoicePDF(
  invoice: InvoiceDocument, 
  options: {
    logoUrl?: string;
    filename?: string;
    quality?: 'draft' | 'standard' | 'high';
  } = {}
): Promise<Buffer> {
  const { logoUrl, quality = 'standard' } = options;

  let browser;
  try {
    // Launch browser with optimized settings
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();
    
    // Set viewport for consistent rendering
    await page.setViewport({
      width: 1200,
      height: 1600,
      deviceScaleFactor: quality === 'high' ? 2 : 1
    });

    // Generate HTML content
    const html = generateInvoiceHTML(invoice, logoUrl);
    
    // Set content and wait for fonts to load
    await page.setContent(html, { 
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000 
    });

    // Wait for any images to load
    await page.waitForFunction(() => document.readyState === 'complete');

    // Generate PDF with professional settings
    const pdfBuffer = await page.pdf(PDF_CONFIG);

    return Buffer.from(pdfBuffer);

  } catch (error) {
    console.error('PDF generation error:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Generate PDF with error handling and retries
export async function generateInvoicePDFWithRetry(
  invoice: InvoiceDocument,
  options: {
    logoUrl?: string;
    filename?: string;
    quality?: 'draft' | 'standard' | 'high';
    maxRetries?: number;
  } = {}
): Promise<Buffer> {
  const { maxRetries = 3 } = options;
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await generateInvoicePDF(invoice, options);
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, attempt * 1000));
    }
  }

  throw lastError!;
}

// Preview HTML for testing (returns HTML string)
export function previewInvoiceHTML(invoice: InvoiceDocument, logoUrl?: string): string {
  return generateInvoiceHTML(invoice, logoUrl);
}

// Get estimated PDF size before generation
export function estimatePDFSize(invoice: InvoiceDocument): number {
  // Base size estimation in KB
  const baseSize = 50;
  const itemsSize = invoice.lineItems.length * 2;
  const notesSize = invoice.notes ? Math.ceil(invoice.notes.length / 100) : 0;
  
  return baseSize + itemsSize + notesSize;
}

// Validate invoice data before PDF generation
export function validateInvoiceForPDF(invoice: InvoiceDocument): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Required fields validation
  if (!invoice.invoiceNumber) errors.push('Invoice number is required');
  if (!invoice.businessInfo.name) errors.push('Business name is required');
  if (!invoice.clientInfo.name) errors.push('Client name is required');
  if (!invoice.lineItems || invoice.lineItems.length === 0) errors.push('At least one line item is required');
  if (!invoice.totals || invoice.totals.total <= 0) errors.push('Invoice total must be greater than 0');

  // Date validation
  if (!invoice.createdAt || !invoice.dueDate) errors.push('Invoice dates are required');
  if (new Date(invoice.dueDate) < new Date(invoice.createdAt)) {
    errors.push('Due date cannot be before issue date');
  }

  // Line items validation
  invoice.lineItems?.forEach((item, index) => {
    if (!item.description) errors.push(`Line item ${index + 1}: Description is required`);
    if (item.quantity <= 0) errors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
    if (item.rate < 0) errors.push(`Line item ${index + 1}: Rate cannot be negative`);
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Generate filename for PDF
export function generatePDFFilename(invoice: InvoiceDocument): string {
  const safeBusinessName = invoice.businessInfo.name.replace(/[^a-zA-Z0-9]/g, '_');
  const safeClientName = invoice.clientInfo.name.replace(/[^a-zA-Z0-9]/g, '_');
  const date = new Date(invoice.createdAt).toISOString().split('T')[0];
  
  return `${invoice.invoiceNumber}_${safeBusinessName}_${safeClientName}_${date}.pdf`;
}