'use client'

import { mockDashboardData } from '../../mock-data/dashboard-data'
import { 
  Loader2, RefreshCw, AlertCircle, DollarSign, FileText, 
  BarChart3, Timer, Search, Brain, Eye, Edit, Copy, 
  MoreHorizontal, ChevronRight, Users, TrendingUp, 
  ArrowUpRight, ArrowDownRight, Lightbulb
} from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'

export default function DevDashboard() {
  const [isLoading, setIsLoading] = useState(true)
  const [dashboardData, setDashboardData] = useState(mockDashboardData)
  const [invoiceFilter, setInvoiceFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1500)
    
    return () => clearTimeout(timer)
  }, [])

  // Calculate percentage changes
  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  }

  // Format currency
  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'text-green-600 bg-green-50'
      case 'sent': return 'text-blue-600 bg-blue-50'
      case 'draft': return 'text-gray-600 bg-gray-50'
      case 'overdue': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  // Get priority color for AI insights
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50'
      case 'medium': return 'border-yellow-200 bg-yellow-50'
      case 'low': return 'border-blue-200 bg-blue-50'
      default: return 'border-gray-200 bg-gray-50'
    }
  }

  // Filter recent invoices
  const filteredInvoices = dashboardData?.recentInvoices.filter((invoice) => {        
    const matchesFilter = invoice.clientName.toLowerCase().includes(invoiceFilter.toLowerCase()) ||
                           invoice.invoiceNumber.toLowerCase().includes(invoiceFilter.toLowerCase())
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter
    return matchesFilter && matchesStatus
  }) || []

  const revenueChange = calculateChange(dashboardData.overview.totalRevenue.thisMonth, dashboardData.overview.totalRevenue.lastMonth)
  const invoiceChange = calculateChange(dashboardData.overview.totalInvoices.thisMonth, dashboardData.overview.totalInvoices.lastMonth)

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 animate-spin rounded-full border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* DEV MODE Notice */}
      <div className="mb-4 p-3 bg-yellow-100 border border-yellow-300 rounded-md">
        <p className="text-yellow-800 font-medium">⚠️ Development Mode - Using Mock Data</p>
        <p className="text-yellow-700 text-sm">This page uses mock data for development and testing purposes.</p>
      </div>
    
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Welcome back, Test User! Here's your business overview.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="month">This Month</option>
              <option value="year">This Year</option>
              <option value="all">All Time</option>
            </select>
            <button 
              className="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
            <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Free
            </span>
          </div>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <h3 className="text-sm font-medium text-gray-600">
                Total Revenue
              </h3>
              <DollarSign className="h-4 w-4 text-gray-400" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(dashboardData.overview.totalRevenue.thisMonth)}
              </div>
              <div className="flex items-center text-xs">
                {revenueChange >= 0 ? (
                  <ArrowUpRight className="w-3 h-3 text-green-600 mr-1" />
                ) : (
                  <ArrowDownRight className="w-3 h-3 text-red-600 mr-1" />
                )}
                <span className={revenueChange >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {Math.abs(revenueChange).toFixed(1)}%
                </span>
                <span className="text-gray-500 ml-1">from last month</span>
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <h3 className="text-sm font-medium text-gray-600">
                Invoices Created
              </h3>
              <FileText className="h-4 w-4 text-gray-400" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {dashboardData.overview.totalInvoices.thisMonth}
              </div>
              <div className="flex items-center text-xs">
                {invoiceChange >= 0 ? (
                  <ArrowUpRight className="w-3 h-3 text-green-600 mr-1" />
                ) : (
                  <ArrowDownRight className="w-3 h-3 text-red-600 mr-1" />
                )}
                <span className={invoiceChange >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {Math.abs(invoiceChange).toFixed(1)}%
                </span>
                <span className="text-gray-500 ml-1">from last month</span>
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <h3 className="text-sm font-medium text-gray-600">
                Average Invoice
              </h3>
              <BarChart3 className="h-4 w-4 text-gray-400" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(dashboardData.overview.averageInvoiceAmount.thisMonth)}
              </div>
              <div className="text-xs text-gray-500">
                All-time: {formatCurrency(dashboardData.overview.averageInvoiceAmount.allTime)}
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex flex-row items-center justify-between space-y-0 pb-2">
              <h3 className="text-sm font-medium text-gray-600">
                Avg. Payment Time
              </h3>
              <Timer className="h-4 w-4 text-gray-400" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {dashboardData.overview.paymentTimeline.averageDaysToPay} days
              </div>
              <div className="text-xs text-gray-500">
                Range: {dashboardData.overview.paymentTimeline.fastestPayment}-{dashboardData.overview.paymentTimeline.slowestPayment} days
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Recent Invoices */}
      <div className="bg-white p-6 rounded-lg shadow-sm mb-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Invoices</h3>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search invoices..."
                value={invoiceFilter}
                onChange={(e) => setInvoiceFilter(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Status</option>
              <option value="draft">Draft</option>
              <option value="sent">Sent</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
            </select>
          </div>
        </div>

        {filteredInvoices.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">No invoices found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredInvoices.map((invoice) => (
              <div
                key={invoice._id}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex items-center space-x-4">
                  <div className={`w-3 h-3 rounded-full ${
                    invoice.status === 'paid' ? 'bg-green-500' :
                    invoice.status === 'sent' ? 'bg-blue-500' :
                    invoice.status === 'draft' ? 'bg-gray-500' :
                    'bg-red-500'
                  }`} />
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">
                        {invoice.invoiceNumber}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(invoice.status)}`}>
                        {invoice.status}
                      </span>
                      {invoice.isOverdue && (
                        <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                          Overdue
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-500">{invoice.clientName}</p>
                    <p className="text-xs text-gray-400">
                      Due: {new Date(invoice.dueDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="font-semibold text-gray-900">
                      {formatCurrency(invoice.total, invoice.currency)}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button className="p-1 text-gray-500 hover:text-gray-700">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-500 hover:text-gray-700">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-500 hover:text-gray-700">
                      <Copy className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-500 hover:text-gray-700">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Quick Status Overview */}
        <div className="grid grid-cols-4 gap-4 mt-6 pt-6 border-t border-gray-200">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">{dashboardData.statusBreakdown.draft}</div>
            <div className="text-xs text-gray-500">Draft</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{dashboardData.statusBreakdown.sent}</div>
            <div className="text-xs text-gray-500">Sent</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{dashboardData.statusBreakdown.paid}</div>
            <div className="text-xs text-gray-500">Paid</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{dashboardData.statusBreakdown.overdue}</div>
            <div className="text-xs text-gray-500">Overdue</div>
          </div>
        </div>
      </div>

      {/* Footer with Development Mode Notice */}
      <div className="mt-12 p-4 bg-gray-100 rounded-lg text-center">
        <p className="text-gray-600 text-sm">Development Mode | Using Mock Data</p>
        <p className="text-gray-500 text-xs mt-1">
          <Link href="/dashboard" className="underline hover:text-blue-600">Go to Normal Dashboard</Link>
        </p>
      </div>
    </div>
  )
}
