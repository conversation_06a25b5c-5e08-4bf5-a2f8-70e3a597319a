# Stripe Setup Guide

## 1. Create Stripe Account
1. Go to [stripe.com](https://stripe.com) and create an account
2. Complete your account setup and verification

## 2. Create Pro Plan Product
1. Go to **Products** in your Stripe dashboard
2. Click **+ Add product**
3. Set the following:
   - **Name**: "Pro Plan"
   - **Description**: "Unlimited invoices and premium features"
   - **Pricing**: 
     - **Model**: Standard pricing
     - **Price**: $9.99 USD
     - **Billing period**: Monthly
     - **Usage type**: Licensed (per user)

4. Click **Save product**
5. Copy the **Price ID** (starts with `price_`) to your `.env` file as `STRIPE_PRO_PRICE_ID`

## 3. Get API Keys
1. Go to **Developers** → **API keys**
2. Copy the **Publishable key** to `.env` as `STRIPE_PUBLISHABLE_KEY`
3. Copy the **Secret key** to `.env` as `STRIPE_SECRET_KEY`

## 4. Configure Webhooks
1. Go to **Developers** → **Webhooks**
2. Click **+ Add endpoint**
3. Set **Endpoint URL** to: `https://yourdomain.com/api/stripe/webhook`
4. Select these events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

5. Click **Add endpoint**
6. Copy the **Signing secret** to `.env` as `STRIPE_WEBHOOK_SECRET`

## 5. Configure Customer Portal
1. Go to **Settings** → **Billing** → **Customer portal**
2. Enable the customer portal
3. Configure these features:
   - ✅ Cancel subscriptions
   - ✅ Update payment methods
   - ✅ View billing history
   - ❌ Update subscriptions (we keep it simple)
   - ❌ Proration (no complex billing)

## 6. Test the Integration

### Test Cards (Use in test mode only)
- **Success**: `4242 4242 4242 4242`
- **Decline**: `4000 0000 0000 0002`
- **Requires Authentication**: `4000 0025 0000 3155`

### Testing Flow
1. Create a test user account
2. Try to create more than 3 invoices (should show upgrade prompt)
3. Go through checkout flow with test card
4. Verify user is upgraded to Pro
5. Test cancellation through customer portal
6. Verify user is downgraded to Free

## 7. Going Live
1. Activate your Stripe account (complete verification)
2. Switch to **Live mode** in dashboard
3. Get live API keys and update `.env`
4. Update webhook URL to production domain
5. Test with real payment methods

## Environment Variables Summary
```env
STRIPE_PUBLISHABLE_KEY=pk_test_... (or pk_live_... for production)
STRIPE_SECRET_KEY=sk_test_... (or sk_live_... for production)
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRO_PRICE_ID=price_...
```

## Simple Billing Features Included
✅ Single Pro plan: $9.99/month  
✅ Simple checkout flow  
✅ Cancel anytime (keeps access until period end)  
✅ Usage tracking (3 free invoices/month)  
✅ Automatic upgrade/downgrade via webhooks  
✅ Customer portal for subscription management  

## Features Intentionally NOT Included (Keeping it Simple)
❌ Multiple plans  
❌ Annual billing  
❌ Proration  
❌ Trial periods  
❌ Plan changes  
❌ Team features  
❌ Complex billing logic  

This keeps the billing system dead simple and easy to maintain!