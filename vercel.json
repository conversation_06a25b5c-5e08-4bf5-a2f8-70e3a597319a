{"buildCommand": "npm run build", "outputDirectory": ".next", "devCommand": "npm run dev", "installCommand": "npm install", "framework": "nextjs", "regions": ["iad1"], "redirects": [{"source": "/:path*", "has": [{"type": "host", "value": "www.templateinvoices.com"}], "destination": "https://templateinvoices.com/:path*", "permanent": true}], "functions": {"src/app/api/invoices/pdf/route.ts": {"maxDuration": 30}, "src/app/api/invoices/email/route.ts": {"maxDuration": 30}, "src/app/api/ai/generate-template/route.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS,PATCH,DELETE,POST,PUT"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}