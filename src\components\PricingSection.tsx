'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Check,
  X,
  Zap,
  Star,
  TrendingUp,
  Shield,
  DollarSign,
  Users,
  Award,
  Clock,
  CreditCard,
  HelpCircle,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import Image from 'next/image'

export default function PricingSection() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null)

  const pricing = {
    free: {
      name: 'Starter',
      description: '3 Invoices Forever Free',
      price: { monthly: 0, yearly: 0 },
      highlight: 'Perfect for testing',
      cta: 'Start Free Now',
      features: [
        'Up to 3 invoices per month',
        'Basic templates',
        'Email support',
        'PDF export',
        'Basic payment tracking'
      ],
      notIncluded: [
        'AI-powered descriptions',
        'Automated reminders',
        'Payment links',
        'Priority support'
      ]
    },
    pro: {
      name: 'Pro',
      description: 'Everything you need to get paid faster',
      price: { monthly: 19.99, yearly: 199.99 },
      highlight: 'MOST POPULAR',
      savings: 'Save $40/year',
      cta: 'Start 14-Day Free Trial',
      badge: 'Pays for itself with ONE faster payment',
      features: [
        'Unlimited invoices',
        'AI-powered descriptions',
        'Automated payment reminders',
        'One-click payment links',
        'Industry-specific templates',
        'Advanced analytics',
        'Priority email support',
        'Mobile app access',
        'Recurring invoices',
        'Multi-currency support'
      ],
      notIncluded: [
        'Team collaboration',
        'White-label options',
        'API access',
        'Dedicated account manager'
      ]
    },
    business: {
      name: 'Business',
      description: 'For growing teams and agencies',
      price: { monthly: 39.99, yearly: 399.99 },
      highlight: 'For Growing Teams',
      savings: 'Save $80/year',
      cta: 'Start 14-Day Free Trial',
      features: [
        'Everything in Pro',
        'Up to 5 team members',
        'Team collaboration tools',
        'White-label invoices',
        'API access',
        'Custom integrations',
        'Advanced permissions',
        'Phone support',
        'Onboarding session',
        'Custom workflows'
      ]
    }
  }

  const testimonials = [
    {
      name: "Alex Chen",
      role: "Freelance Designer",
      image: "/testimonials/alex.jpg",
      quote: "Upgraded to Pro and got paid $3,200 two weeks faster. The annual plan paid for itself immediately.",
      savings: "$3,200"
    },
    {
      name: "Maria Rodriguez",
      role: "Marketing Consultant",
      image: "/testimonials/maria.jpg",
      quote: "The automated reminders alone save me 5 hours per month. Worth every penny.",
      savings: "5 hrs/month"
    },
    {
      name: "James Wilson",
      role: "Web Developer",
      image: "/testimonials/james.jpg",
      quote: "My clients love the payment links. I'm getting paid in days instead of months now.",
      savings: "30 days faster"
    }
  ]

  const faqs = [
    {
      question: "What if I only send a few invoices?",
      answer: "Our Free plan includes 3 invoices per month forever - perfect for trying out the platform. Most users find that getting paid even one day faster on a single invoice covers the cost of our Pro plan."
    },
    {
      question: "How quickly will I get paid?",
      answer: "Our users report getting paid 3x faster on average - typically 14 days instead of 45. The automated reminders and one-click payment links make a huge difference."
    },
    {
      question: "Is my data secure?",
      answer: "Absolutely. We use bank-level 256-bit SSL encryption, are SOC 2 compliant, and never share your data. Your invoices are backed up daily and you can export them anytime."
    },
    {
      question: "Can I cancel anytime?",
      answer: "Yes! No contracts, no hidden fees. Cancel anytime with one click. You'll keep access until the end of your billing period and can download all your invoices."
    },
    {
      question: "What's included in the 14-day trial?",
      answer: "Full access to all Pro or Business features - no credit card required to start. Create unlimited invoices, set up automated reminders, and see how much faster you get paid."
    }
  ]

  const yearlyDiscount = billingCycle === 'yearly' ? 0.83 : 1 // ~17% off

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Pay for itself in{' '}
            <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
              one faster payment
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Join 500+ professionals who upgraded this week and are already seeing results
          </p>

          {/* Billing Toggle */}
          <div className="inline-flex items-center gap-4 bg-white rounded-full p-1 shadow-sm">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-6 py-2 rounded-full transition-all ${
                billingCycle === 'monthly'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('yearly')}
              className={`px-6 py-2 rounded-full transition-all flex items-center gap-2 ${
                billingCycle === 'yearly'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Yearly
              <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">
                Save 17%
              </span>
            </button>
          </div>
        </motion.div>

        {/* Comparison Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="grid md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto"
        >
          <div className="bg-white rounded-lg p-6 text-center">
            <DollarSign className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">$347</p>
            <p className="text-sm text-gray-600">Average monthly savings</p>
          </div>
          <div className="bg-white rounded-lg p-6 text-center">
            <Clock className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">2 hours</p>
            <p className="text-sm text-gray-600">Worth of your time/month</p>
          </div>
          <div className="bg-white rounded-lg p-6 text-center">
            <CreditCard className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">$35</p>
            <p className="text-sm text-gray-600">Less than one late fee</p>
          </div>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 mb-16 max-w-6xl mx-auto">
          {/* Free Tier */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl p-8 relative border border-gray-200"
          >
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{pricing.free.name}</h3>
              <p className="text-gray-600 mb-4">{pricing.free.description}</p>
              <div className="flex items-baseline gap-2 mb-4">
                <span className="text-4xl font-bold text-gray-900">$0</span>
                <span className="text-gray-600">/forever</span>
              </div>
              <p className="text-sm text-blue-600 font-medium">{pricing.free.highlight}</p>
            </div>

            <Button
              variant="outline"
              className="w-full mb-8"
              size="lg"
            >
              {pricing.free.cta}
            </Button>

            <div className="space-y-3">
              {pricing.free.features.map((feature, index) => (
                <div key={index} className="flex items-start gap-3">
                  <Check className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-700">{feature}</span>
                </div>
              ))}
              {pricing.free.notIncluded.map((feature, index) => (
                <div key={index} className="flex items-start gap-3 opacity-50">
                  <X className="w-5 h-5 text-gray-400 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-500 line-through">{feature}</span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Pro Tier */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-b from-blue-50 to-white rounded-2xl p-8 relative border-2 border-blue-600 shadow-xl"
          >
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                {pricing.pro.highlight}
              </span>
            </div>

            <div className="mb-8 pt-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{pricing.pro.name}</h3>
              <p className="text-gray-600 mb-4">{pricing.pro.description}</p>
              <div className="flex items-baseline gap-2 mb-2">
                <span className="text-4xl font-bold text-gray-900">
                  ${(pricing.pro.price[billingCycle] * yearlyDiscount).toFixed(2)}
                </span>
                <span className="text-gray-600">/{billingCycle === 'yearly' ? 'month' : 'month'}</span>
              </div>
              {billingCycle === 'yearly' && (
                <p className="text-sm text-green-600 font-medium mb-2">{pricing.pro.savings}</p>
              )}
              <p className="text-sm text-purple-600 font-medium">{pricing.pro.badge}</p>
            </div>

            <Button
              className="w-full mb-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              size="lg"
            >
              {pricing.pro.cta}
              <Zap className="w-4 h-4 ml-2" />
            </Button>

            <div className="space-y-3">
              {pricing.pro.features.map((feature, index) => (
                <div key={index} className="flex items-start gap-3">
                  <Check className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-700">{feature}</span>
                </div>
              ))}
            </div>

            {/* Social Proof */}
            <div className="mt-8 pt-8 border-t">
              <div className="flex -space-x-2 mb-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div
                    key={i}
                    className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full border-2 border-white"
                  />
                ))}
                <div className="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-600">+495</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">Joined this week</p>
            </div>
          </motion.div>

          {/* Business Tier */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl p-8 relative border border-gray-200"
          >
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{pricing.business.name}</h3>
              <p className="text-gray-600 mb-4">{pricing.business.description}</p>
              <div className="flex items-baseline gap-2 mb-2">
                <span className="text-4xl font-bold text-gray-900">
                  ${(pricing.business.price[billingCycle] * yearlyDiscount).toFixed(2)}
                </span>
                <span className="text-gray-600">/{billingCycle === 'yearly' ? 'month' : 'month'}</span>
              </div>
              {billingCycle === 'yearly' && (
                <p className="text-sm text-green-600 font-medium mb-2">{pricing.business.savings}</p>
              )}
              <p className="text-sm text-blue-600 font-medium">{pricing.business.highlight}</p>
            </div>

            <Button
              variant="outline"
              className="w-full mb-8"
              size="lg"
            >
              {pricing.business.cta}
            </Button>

            <div className="space-y-3">
              {pricing.business.features.map((feature, index) => (
                <div key={index} className="flex items-start gap-3">
                  <Check className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Risk Reversal */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="bg-green-50 rounded-2xl p-8 text-center mb-16 max-w-3xl mx-auto"
        >
          <Shield className="w-12 h-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-gray-900 mb-2">60-Day Money-Back Guarantee</h3>
          <p className="text-gray-700 mb-4">
            Try any plan risk-free for 60 days. If you're not getting paid faster, we'll refund every penny - no questions asked.
          </p>
          <div className="flex flex-wrap gap-4 justify-center text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-600" />
              Cancel anytime
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-600" />
              Keep your invoices
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-600" />
              Free data export
            </div>
          </div>
        </motion.div>

        {/* Testimonials */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Real results from real professionals
          </h3>
          <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg p-6 shadow-sm"
              >
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full" />
                  <div>
                    <p className="font-semibold text-gray-900">{testimonial.name}</p>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                  </div>
                </div>
                <p className="text-gray-700 mb-4">"{testimonial.quote}"</p>
                <div className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  <span className="font-semibold text-green-600">{testimonial.savings}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* FAQs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="max-w-3xl mx-auto"
        >
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Frequently asked questions
          </h3>
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-sm"
              >
                <button
                  onClick={() => setExpandedFaq(expandedFaq === index ? null : index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <span className="font-medium text-gray-900">{faq.question}</span>
                  {expandedFaq === index ? (
                    <ChevronUp className="w-5 h-5 text-gray-600" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-600" />
                  )}
                </button>
                {expandedFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-700">{faq.answer}</p>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}