import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

/**
 * Advanced Route Protection Middleware
 * 
 * Provides comprehensive protection with:
 * - User authentication validation
 * - Subscription level checks
 * - Invoice limit verification
 * - Graceful error handling
 * - Automatic session refresh
 */

interface UserSession {
  id: string;
  email: string;
  subscription: {
    plan: 'free' | 'pro';
    invoicesUsed: number;
    resetDate: string;
  };
}

export default withAuth(
  async function middleware(req) {
    try {
      const { pathname, searchParams } = req.nextUrl;
      const token = req.nextauth.token;
      const session = token as any as UserSession;
      
      // Skip processing for static files and API routes that don't need validation
      if (isStaticFile(pathname) || isPublicApiRoute(pathname)) {
        return NextResponse.next();
      }

      // Route-specific protection logic
      const routeConfig = getRouteConfiguration(pathname);
      
      // Handle public routes
      if (routeConfig.access === 'public') {
        return NextResponse.next();
      }
      
      // Handle mixed access routes (upgrade page)
      if (routeConfig.access === 'mixed') {
        return NextResponse.next(); // Allow both authenticated and unauthenticated
      }
      
      // Authentication required beyond this point
      if (!session?.id) {
        return redirectToSignIn(req.url);
      }
      
      // Handle invoice creation routes - check limits
      if (routeConfig.requiresInvoiceCheck) {
        const canCreate = await checkInvoiceCreationPermission(session);
        
        if (!canCreate.allowed) {
          if (canCreate.reason === 'limit_exceeded') {
            return redirectToUpgrade(req.url, 'limit_exceeded');
          } else if (canCreate.reason === 'subscription_issue') {
            return redirectToUpgrade(req.url, 'subscription_issue');
          }
        }
      }
      
      // Handle subscription-dependent features
      if (routeConfig.requiresSubscription && session.subscription?.plan !== 'pro') {
        return redirectToUpgrade(req.url, 'feature_locked');
      }
      
      // Session validation and refresh
      if (shouldRefreshSession(session)) {
        const response = NextResponse.next();
        response.headers.set('x-middleware-refresh-session', 'true');
        return response;
      }
      
      return NextResponse.next();
      
    } catch (error) {
      console.error('Middleware error:', error);
      return handleMiddlewareError(req, error);
    }
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        const routeConfig = getRouteConfiguration(pathname);
        
        // Allow public and mixed access routes regardless of auth status
        if (routeConfig.access === 'public' || routeConfig.access === 'mixed') {
          return true;
        }
        
        // Protected routes require valid token
        return !!token;
      },
    },
  }
);

/**
 * Route Configuration
 */
interface RouteConfig {
  access: 'protected' | 'public' | 'mixed';
  requiresInvoiceCheck: boolean;
  requiresSubscription: boolean;
  description: string;
}

function getRouteConfiguration(pathname: string): RouteConfig {
  // Create routes that require authentication and invoice limit checks
  if (pathname.startsWith('/create')) {
    return {
      access: 'protected',
      requiresInvoiceCheck: true,
      requiresSubscription: false,
      description: 'Invoice creation requires auth and limit validation'
    };
  }
  
  // My invoices - requires authentication only
  if (pathname.startsWith('/my-invoices')) {
    return {
      access: 'protected', 
      requiresInvoiceCheck: false,
      requiresSubscription: false,
      description: 'View existing invoices'
    };
  }
  
  // Dashboard and account management
  if (pathname.startsWith('/dashboard')) {
    return {
      access: 'protected',
      requiresInvoiceCheck: false,
      requiresSubscription: false,
      description: 'User dashboard'
    };
  }
  
  // Upgrade page - accessible to both authenticated and unauthenticated users
  if (pathname.startsWith('/upgrade')) {
    return {
      access: 'mixed',
      requiresInvoiceCheck: false,
      requiresSubscription: false,
      description: 'Upgrade page accessible to all'
    };
  }
  
  // Templates page - completely public
  if (pathname.startsWith('/templates')) {
    return {
      access: 'public',
      requiresInvoiceCheck: false,
      requiresSubscription: false,
      description: 'Public template gallery'
    };
  }
  
  // Pricing page - public
  if (pathname.startsWith('/pricing')) {
    return {
      access: 'public',
      requiresInvoiceCheck: false,
      requiresSubscription: false,
      description: 'Public pricing page'
    };
  }
  
  // API routes that need protection
  if (pathname.startsWith('/api/invoices') || pathname.startsWith('/api/clients')) {
    return {
      access: 'protected',
      requiresInvoiceCheck: pathname.includes('create') || pathname.includes('POST'),
      requiresSubscription: false,
      description: 'Protected API endpoints'
    };
  }
  
  // Stripe checkout/portal - protected
  if (pathname.startsWith('/api/stripe/checkout') || pathname.startsWith('/api/stripe/portal')) {
    return {
      access: 'protected',
      requiresInvoiceCheck: false,
      requiresSubscription: false,
      description: 'Stripe payment endpoints'
    };
  }
  
  // Default: require authentication for unspecified routes
  return {
    access: 'protected',
    requiresInvoiceCheck: false,
    requiresSubscription: false,
    description: 'Default protected route'
  };
}

/**
 * Invoice Creation Permission Check
 */
async function checkInvoiceCreationPermission(session: UserSession): Promise<{
  allowed: boolean;
  reason?: 'limit_exceeded' | 'subscription_issue' | 'network_error';
  details?: any;
}> {
  try {
    // Pro users always allowed
    if (session.subscription?.plan === 'pro') {
      return { allowed: true };
    }
    
    // Check free tier limits
    const invoicesUsed = session.subscription?.invoicesUsed || 0;
    const FREE_TIER_LIMIT = 3;
    
    if (invoicesUsed >= FREE_TIER_LIMIT) {
      return {
        allowed: false,
        reason: 'limit_exceeded',
        details: {
          used: invoicesUsed,
          limit: FREE_TIER_LIMIT,
          plan: 'free'
        }
      };
    }
    
    // Check if reset is needed (simplified check in middleware)
    const resetDate = new Date(session.subscription?.resetDate || new Date());
    const now = new Date();
    
    if (now > resetDate) {
      // Reset needed - allow creation but flag for backend processing
      return { allowed: true };
    }
    
    return { allowed: true };
    
  } catch (error) {
    console.error('Error checking invoice permission:', error);
    // On error, allow creation but log issue
    return { 
      allowed: true, 
      reason: 'network_error',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    };
  }
}

/**
 * Session Refresh Check
 */
function shouldRefreshSession(session: UserSession): boolean {
  // Simple refresh logic - can be enhanced based on needs
  const lastUpdate = session.subscription?.resetDate;
  if (!lastUpdate) return false;
  
  const hoursSinceUpdate = (Date.now() - new Date(lastUpdate).getTime()) / (1000 * 60 * 60);
  return hoursSinceUpdate > 24; // Refresh session data every 24 hours
}

/**
 * Utility Functions
 */
function isStaticFile(pathname: string): boolean {
  return pathname.startsWith('/_next/') || 
         pathname.startsWith('/favicon') ||
         pathname.startsWith('/public/') ||
         !!pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|webp)$/);
}

function isPublicApiRoute(pathname: string): boolean {
  const publicRoutes = [
    '/api/auth',
    '/api/stripe/webhook',
    '/api/webhooks/stripe',
    '/api/webhooks/test',
    '/api/health',
    '/api/status'
  ];
  
  return publicRoutes.some(route => pathname.startsWith(route));
}

/**
 * Redirect Handlers
 */
function redirectToSignIn(currentUrl: string): NextResponse {
  const url = new URL('/auth/signin', currentUrl);
  // Note: callbackUrl removed since NextAuth redirect callback always goes to /dashboard
  url.searchParams.set('error', 'AccessDenied');
  
  return NextResponse.redirect(url);
}

function redirectToUpgrade(currentUrl: string, reason: string): NextResponse {
  const url = new URL('/upgrade', currentUrl);
  url.searchParams.set('reason', reason);
  url.searchParams.set('returnTo', currentUrl);
  
  const messages: { [key: string]: string } = {
    limit_exceeded: 'You have reached your monthly invoice limit. Upgrade to Pro for unlimited invoices.',
    subscription_issue: 'There was an issue with your subscription. Please upgrade or contact support.',
    feature_locked: 'This feature requires a Pro subscription.'
  };
  
  url.searchParams.set('message', messages[reason] || 'Upgrade required');
  
  return NextResponse.redirect(url);
}

/**
 * Error Handler
 */
function handleMiddlewareError(req: NextRequest, error: any): NextResponse {
  console.error('Middleware error:', {
    pathname: req.nextUrl.pathname,
    error: error.message,
    stack: error.stack
  });
  
  // For API routes, return JSON error
  if (req.nextUrl.pathname.startsWith('/api/')) {
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Authentication middleware failed',
        code: 'MIDDLEWARE_ERROR'
      },
      { status: 500 }
    );
  }
  
  // For page routes, redirect to error page
  const errorUrl = new URL('/auth/error', req.url);
  errorUrl.searchParams.set('error', 'Configuration');
  return NextResponse.redirect(errorUrl);
}

/**
 * Middleware Matcher Configuration
 */
export const config = {
  matcher: [
    // Protected routes requiring authentication
    '/create/:path*',
    '/my-invoices/:path*',
    '/dashboard/:path*',
    
    // Mixed access routes (upgrade page allows both auth/unauth)
    '/upgrade/:path*',
    
    // API routes requiring protection
    '/api/invoices/:path*',
    '/api/clients/:path*', 
    '/api/analytics/:path*',
    '/api/stripe/checkout/:path*',
    '/api/stripe/portal/:path*',
    
    // Exclude public routes and static assets
    '/((?!api/auth|api/stripe/webhook|api/webhooks|auth|templates|pricing|privacy|_next/static|_next/image|favicon.ico|public).*)',
  ],
};