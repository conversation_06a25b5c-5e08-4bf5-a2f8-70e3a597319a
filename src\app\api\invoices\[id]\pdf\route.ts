import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ObjectId } from 'mongodb';
import { getInvoiceById } from '@/lib/invoice-service';
import { generateInvoicePDFWithRetry, generatePDFFilename, validateInvoiceForPDF } from '@/lib/pdf-export';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * API Route: Generate and download invoice PDF
 * GET /api/invoices/[id]/pdf
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Await params for Next.js 15
    const { id: invoiceId } = await params;
    const { searchParams } = new URL(request.url);
    const quality = searchParams.get('quality') as 'draft' | 'standard' | 'high' || 'standard';
    const download = searchParams.get('download') === 'true';

    // Validate invoice ID
    if (!ObjectId.isValid(invoiceId)) {
      return NextResponse.json({ error: 'Invalid invoice ID' }, { status: 400 });
    }

    // Get invoice
    const invoice = await getInvoiceById(invoiceId, session.user.id);
    
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Validate invoice for PDF generation
    const validation = validateInvoiceForPDF(invoice);
    if (!validation.isValid) {
      return NextResponse.json({ 
        error: 'Invoice validation failed', 
        details: validation.errors 
      }, { status: 400 });
    }

    // Generate PDF
    const pdfBuffer = await generateInvoicePDFWithRetry(invoice, {
      quality,
      maxRetries: 3
    });

    // Generate filename
    const filename = generatePDFFilename(invoice);

    // Set response headers
    const headers = new Headers();
    headers.set('Content-Type', 'application/pdf');
    headers.set('Content-Length', pdfBuffer.length.toString());
    
    if (download) {
      headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    } else {
      headers.set('Content-Disposition', `inline; filename="${filename}"`);
    }

    return new NextResponse(pdfBuffer, { headers });

  } catch (error) {
    console.error('PDF generation error:', error);
    
    if (error instanceof Error && error.message.includes('Failed to generate PDF')) {
      return NextResponse.json({ 
        error: 'PDF generation failed',
        details: error.message 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}