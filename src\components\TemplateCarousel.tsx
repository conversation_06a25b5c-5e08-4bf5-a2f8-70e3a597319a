'use client'

import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, Pagination } from 'swiper/modules'
import { motion } from 'framer-motion'
import { ExternalLink } from 'lucide-react'
import Link from 'next/link'
import 'swiper/css'
import 'swiper/css/pagination'

const templates = [
  {
    name: "Freelancer Classic",
    image: "/templates/freelancer.png",
    video: "/templates/freelancer-demo.mp4",
    industry: "Freelancers",
    demoLink: "/create?template=freelancer",
  },
  {
    name: "Consultant Modern",
    image: "/templates/consultant.png",
    video: "/templates/consultant-demo.mp4",
    industry: "Consultants",
    demoLink: "/create?template=consulting",
  },
  {
    name: "Contractor Pro",
    image: "/templates/contractor.png",
    video: "/templates/contractor-demo.mp4",
    industry: "Contractors",
    demoLink: "/create?template=professional",
  },
  {
    name: "Photographer Bold",
    image: "/templates/photography.png",
    video: "/templates/photography-demo.mp4",
    industry: "Photography",
    demoLink: "/create?template=photography",
  },
  {
    name: "Legal Detail",
    image: "/templates/legal.png",
    video: "/templates/legal-demo.mp4",
    industry: "Legal",
    demoLink: "/create?template=legal",
  },
  {
    name: "Real Estate Luxe",
    image: "/templates/realestate.png",
    video: "/templates/realestate-demo.mp4",
    industry: "Real Estate",
    demoLink: "/create?template=service-business",
  },
]

export default function TemplateCarousel() {
  return (
    <section className="relative w-full overflow-hidden">
      {/* Fullscreen, softly blurred background video */}
      <video
        autoPlay
        loop
        muted
        playsInline
        className="absolute inset-0 w-full h-full object-cover z-0 opacity-80"
        poster="/templates/hero-fallback.jpg"
        style={{ filter: "blur(2.5px) brightness(0.85) saturate(1.3)" }}
      >
        <source src="/video-bg.mp4" type="video/mp4" />
        {/* Fallback image if video can't load */}
      </video>
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-20">
        <motion.h2 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-3xl sm:text-4xl font-bold text-center mb-12 text-white drop-shadow-lg"
        >
          Preview Our Beautiful Invoice Templates
        </motion.h2>

        <Swiper
          modules={[Autoplay, Pagination]}
          autoplay={{ delay: 3900, disableOnInteraction: false }}
          pagination={{ clickable: true, dynamicBullets: true }}
          slidesPerView={1.08}
          spaceBetween={28}
          breakpoints={{
            640: { slidesPerView: 2.18 },
            1024: { slidesPerView: 3.25 },
          }}
          className="!pb-12 select-none template-carousel"
          grabCursor={true}
          loop={true}
        >
          {templates.map((tpl, i) => (
            <SwiperSlide key={tpl.name}>
              <motion.div
                whileHover={{
                  scale: 1.055,
                  y: -6,
                  boxShadow: "0 10px 44px 0 rgba(0, 224, 255, 0.1)",
                }}
                transition={{ type: "spring", stiffness: 250, damping: 22 }}
                className="relative rounded-2xl overflow-hidden border border-gray-300 shadow-2xl bg-white/75 backdrop-blur-2xl flex flex-col items-center transition-transform duration-300 min-h-[380px] group"
                style={{
                  borderImage: "linear-gradient(130deg, #007cf0 0%, #00dfd8 40%, #f80094 100%) 1",
                }}
              >
                {/* On-card animated gradient overlay */}
                <motion.div
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 0.12 }}
                  transition={{ duration: 0.5 }}
                  className="pointer-events-none absolute inset-0 z-10 bg-gradient-to-br from-[#00dfd8] via-[#007cf0] to-[#f80094] group-hover:opacity-20 opacity-0"
                />
                
                {/* Card content: use video preview for the first card, images for others */}
                <div className="relative w-full flex-1 flex items-center justify-center py-5 px-3">
                  {i === 0 && (
                    <video
                      src={tpl.video}
                      autoPlay
                      loop
                      muted
                      playsInline
                      className="rounded-xl w-full h-56 object-cover border border-[#007cf0]/20 shadow-md"
                      style={{ background: "#f6fafd" }}
                      poster={tpl.image}
                    />
                  )}
                  {i !== 0 && (
                    <img
                      src={tpl.image}
                      alt={tpl.name}
                      className="object-contain rounded-xl shadow-lg w-full h-56 bg-gradient-to-br from-blue-50 via-white to-fuchsia-50 border border-[#f80094]/20"
                      loading="lazy"
                      onError={e => {
                        (e.target as HTMLImageElement).src = "/templates/placeholder.png";
                      }}
                      draggable={false}
                    />
                  )}
                  
                  {/* "View Demo" button overlay */}
                  <motion.div
                    className="absolute bottom-4 left-1/2 -translate-x-1/2 z-20 opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto"
                    initial={{ y: 18, opacity: 0 }}
                    whileHover={{ y: 0, opacity: 1 }}
                    transition={{ type: "spring", stiffness: 160, damping: 18, delay: 0.22 }}
                  >
                    <Link
                      href={tpl.demoLink}
                      className="bg-gradient-to-tr from-[#007cf0] via-[#00dfd8] to-[#f80094] text-white px-6 py-2 rounded-full font-semibold shadow-lg flex items-center gap-2 transition-all duration-300"
                    >
                      <ExternalLink className="w-4 h-4" />
                      Use Template
                    </Link>
                  </motion.div>
                </div>
                
                {/* Template info */}
                <div className="w-full flex flex-col items-center py-4 z-20">
                  <div className="font-semibold text-lg text-gray-900 drop-shadow-sm">{tpl.name}</div>
                  <div className="text-xs text-fuchsia-600 mt-1 tracking-wide">{tpl.industry}</div>
                </div>
              </motion.div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      
      <style jsx global>{`
        .template-carousel .swiper-pagination-bullet {
          background: linear-gradient(90deg, #007cf0 0%, #00dfd8 50%, #f80094 100%) !important;
          opacity: 0.18;
        }
        .template-carousel .swiper-pagination-bullet-active {
          opacity: 1 !important;
          box-shadow: 0 0 0 3px rgba(0, 223, 216, 0.2) !important;
        }
      `}</style>
    </section>
  )
}