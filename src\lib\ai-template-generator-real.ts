import { invoiceTemplates, getTemplateById, InvoiceTemplate } from './template-data';

interface BusinessAnalysis {
  businessType: string;
  industry: string;
  serviceType: 'product' | 'service' | 'hybrid';
  billingStructure: 'hourly' | 'project' | 'item' | 'retainer';
  businessSize: 'freelancer' | 'small' | 'medium' | 'large';
  specialRequirements: string[];
}

interface CustomizedTemplate extends InvoiceTemplate {
  customizations: {
    fieldLabels: Record<string, string>;
    additionalFields: string[];
    removedFields: string[];
    paymentTerms: string;
    notes: string;
  };
}

export async function generateCustomTemplate(businessDescription: string): Promise<CustomizedTemplate> {
  try {
    // Analyze the business description
    const analysis = analyzeBusinessDescription(businessDescription);
    
    // Find the best base template
    const baseTemplate = findBestBaseTemplate(analysis);
    
    // Customize the template based on analysis
    const customizedTemplate = customizeTemplate(baseTemplate, analysis, businessDescription);
    
    return customizedTemplate;
  } catch (error) {
    console.error('Error generating custom template:', error);
    
    // Fallback to professional template with basic customizations
    const fallbackTemplate = getTemplateById('professional')!;
    return {
      ...fallbackTemplate,
      customizations: {
        fieldLabels: {},
        additionalFields: [],
        removedFields: [],
        paymentTerms: 'Payment due within 30 days',
        notes: 'Generated based on your business description'
      }
    };
  }
}

function analyzeBusinessDescription(description: string): BusinessAnalysis {
  const lowerDesc = description.toLowerCase();
  
  // Business type detection
  const businessTypes = {
    'photography': ['photo', 'photographer', 'photography', 'wedding', 'portrait', 'event photography'],
    'consulting': ['consult', 'advisor', 'consultant', 'consulting', 'strategy', 'business advisor'],
    'freelance': ['freelance', 'freelancer', 'independent', 'contractor', 'self-employed'],
    'creative': ['design', 'designer', 'creative', 'graphic', 'web design', 'marketing', 'advertising'],
    'service': ['service', 'cleaning', 'maintenance', 'repair', 'landscaping', 'tutoring'],
    'technology': ['web', 'app', 'software', 'tech', 'developer', 'programming', 'IT']
  };
  
  let detectedBusinessType = 'general';
  let maxMatches = 0;
  
  for (const [type, keywords] of Object.entries(businessTypes)) {
    const matches = keywords.filter(keyword => lowerDesc.includes(keyword)).length;
    if (matches > maxMatches) {
      maxMatches = matches;
      detectedBusinessType = type;
    }
  }
  
  // Industry mapping
  const industryMap: Record<string, string> = {
    'photography': 'Photography',
    'consulting': 'Consulting',
    'freelance': 'Freelance',
    'creative': 'Creative',
    'service': 'Service Business',
    'technology': 'General Business',
    'general': 'General Business'
  };
  
  // Service type detection
  const serviceType = lowerDesc.includes('product') || lowerDesc.includes('goods') || lowerDesc.includes('retail') 
    ? 'product' 
    : lowerDesc.includes('service') || lowerDesc.includes('consulting') || lowerDesc.includes('freelance')
    ? 'service'
    : 'hybrid';
  
  // Billing structure detection
  let billingStructure: BusinessAnalysis['billingStructure'] = 'project';
  if (lowerDesc.includes('hour') || lowerDesc.includes('hourly')) {
    billingStructure = 'hourly';
  } else if (lowerDesc.includes('retainer') || lowerDesc.includes('monthly')) {
    billingStructure = 'retainer';
  } else if (lowerDesc.includes('item') || lowerDesc.includes('product') || lowerDesc.includes('unit')) {
    billingStructure = 'item';
  }
  
  // Business size detection
  let businessSize: BusinessAnalysis['businessSize'] = 'small';
  if (lowerDesc.includes('freelance') || lowerDesc.includes('independent') || lowerDesc.includes('solo')) {
    businessSize = 'freelancer';
  } else if (lowerDesc.includes('large') || lowerDesc.includes('corporation') || lowerDesc.includes('enterprise')) {
    businessSize = 'large';
  } else if (lowerDesc.includes('medium') || lowerDesc.includes('team')) {
    businessSize = 'medium';
  }
  
  // Special requirements detection
  const specialRequirements: string[] = [];
  if (lowerDesc.includes('tax')) specialRequirements.push('tax-calculation');
  if (lowerDesc.includes('discount')) specialRequirements.push('discount-support');
  if (lowerDesc.includes('milestone')) specialRequirements.push('milestone-billing');
  if (lowerDesc.includes('recurring')) specialRequirements.push('recurring-billing');
  if (lowerDesc.includes('expense')) specialRequirements.push('expense-tracking');
  
  return {
    businessType: detectedBusinessType,
    industry: industryMap[detectedBusinessType],
    serviceType,
    billingStructure,
    businessSize,
    specialRequirements
  };
}

function findBestBaseTemplate(analysis: BusinessAnalysis): InvoiceTemplate {
  // Template priority based on analysis
  const templatePriority: Record<string, string[]> = {
    'photography': ['photography', 'creative', 'freelancer'],
    'consulting': ['consulting', 'professional', 'service-business'],
    'freelance': ['freelancer', 'professional', 'modern'],
    'creative': ['modern', 'freelancer', 'professional'],
    'service': ['service-business', 'professional', 'freelancer'],
    'technology': ['professional', 'modern', 'freelancer'],
    'general': ['professional', 'modern', 'service-business']
  };
  
  const priorities = templatePriority[analysis.businessType] || templatePriority['general'];
  
  for (const templateId of priorities) {
    const template = getTemplateById(templateId);
    if (template) {
      return template;
    }
  }
  
  // Fallback to professional template
  return getTemplateById('professional')!;
}

function customizeTemplate(
  baseTemplate: InvoiceTemplate, 
  analysis: BusinessAnalysis, 
  originalDescription: string
): CustomizedTemplate {
  const customizations = {
    fieldLabels: {} as Record<string, string>,
    additionalFields: [] as string[],
    removedFields: [] as string[],
    paymentTerms: generatePaymentTerms(analysis),
    notes: generateNotes(analysis, originalDescription)
  };
  
  // Customize field labels based on business type
  switch (analysis.businessType) {
    case 'photography':
      customizations.fieldLabels = {
        'lineItems': 'Photography Services',
        'quantity': 'Sessions',
        'rate': 'Session Rate',
        'notes': 'Shoot Details & Deliverables'
      };
      customizations.additionalFields = ['shootDate', 'deliveryDate', 'usageRights'];
      break;
      
    case 'consulting':
      customizations.fieldLabels = {
        'lineItems': 'Consulting Services',
        'quantity': 'Hours',
        'rate': 'Hourly Rate',
        'notes': 'Project Scope & Deliverables'
      };
      customizations.additionalFields = ['projectPhase', 'milestones'];
      break;
      
    case 'freelance':
      customizations.fieldLabels = {
        'lineItems': 'Services Provided',
        'quantity': analysis.billingStructure === 'hourly' ? 'Hours' : 'Quantity',
        'rate': analysis.billingStructure === 'hourly' ? 'Hourly Rate' : 'Rate',
        'notes': 'Work Details'
      };
      break;
      
    case 'creative':
      customizations.fieldLabels = {
        'lineItems': 'Creative Services',
        'quantity': 'Items',
        'rate': 'Rate per Item',
        'notes': 'Project Specifications'
      };
      customizations.additionalFields = ['revisions', 'deliveryFormat'];
      break;
      
    case 'service':
      customizations.fieldLabels = {
        'lineItems': 'Services',
        'quantity': 'Quantity',
        'rate': 'Service Rate',
        'notes': 'Service Details'
      };
      break;
  }
  
  // Add fields based on billing structure
  if (analysis.billingStructure === 'hourly') {
    customizations.additionalFields.push('timeTracking');
  } else if (analysis.billingStructure === 'project') {
    customizations.additionalFields.push('projectMilestones');
  } else if (analysis.billingStructure === 'retainer') {
    customizations.additionalFields.push('retainerPeriod');
  }
  
  // Add fields based on special requirements
  if (analysis.specialRequirements.includes('expense-tracking')) {
    customizations.additionalFields.push('expenses');
  }
  if (analysis.specialRequirements.includes('discount-support')) {
    customizations.additionalFields.push('discounts');
  }
  
  // Remove logo field for freelancers if not needed
  if (analysis.businessSize === 'freelancer' && !originalDescription.toLowerCase().includes('logo')) {
    customizations.removedFields.push('companyLogo');
  }
  
  return {
    ...baseTemplate,
    name: `Custom ${baseTemplate.name}`,
    description: `Customized ${baseTemplate.description.toLowerCase()} for ${analysis.industry.toLowerCase()}`,
    customizations
  };
}

function generatePaymentTerms(analysis: BusinessAnalysis): string {
  const baseTerms = {
    'freelancer': 'Payment due within 14 days',
    'small': 'Payment due within 30 days',
    'medium': 'Payment due within 30 days',
    'large': 'Payment due within 45 days'
  };
  
  let terms = baseTerms[analysis.businessSize];
  
  if (analysis.billingStructure === 'retainer') {
    terms = 'Retainer payment due monthly in advance';
  } else if (analysis.billingStructure === 'project') {
    terms = 'Payment due within 30 days of project completion';
  }
  
  return terms;
}

function generateNotes(analysis: BusinessAnalysis, originalDescription: string): string {
  const notes = [
    `Template customized for ${analysis.industry.toLowerCase()} business`,
    `Optimized for ${analysis.billingStructure} billing structure`
  ];
  
  if (analysis.specialRequirements.length > 0) {
    notes.push(`Includes support for: ${analysis.specialRequirements.join(', ')}`);
  }
  
  return notes.join('. ') + '.';
}

// Simulate API call delay for realistic UX
export async function simulateAIProcessing(description: string): Promise<void> {
  const processingTime = Math.min(2000 + description.length * 10, 5000);
  await new Promise(resolve => setTimeout(resolve, processingTime));
}

export type { BusinessAnalysis, CustomizedTemplate };