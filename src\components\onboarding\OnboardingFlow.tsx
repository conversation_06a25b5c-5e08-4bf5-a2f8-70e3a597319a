'use client'

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronRight, 
  ChevronLeft, 
  CheckCircle, 
  Sparkles,
  FileText,
  Mail,
  CreditCard,
  TrendingUp,
  Users,
  Zap,
  ArrowRight,
  X,
  PartyPopper,
  Lightbulb
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { trackEvent } from '@/lib/analytics-client';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  content: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface OnboardingFlowProps {
  isNewUser?: boolean;
  onComplete?: () => void;
  onSkip?: () => void;
}

export default function OnboardingFlow({ 
  isNewUser = true, 
  onComplete, 
  onSkip 
}: OnboardingFlowProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [showConfetti, setShowConfetti] = useState(false);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Template Invoice! 🎉',
      description: 'Create professional invoices in seconds with AI',
      icon: <Sparkles className="w-8 h-8 text-purple-600" />,
      content: (
        <div className="space-y-6">
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-3">Here\'s what you can do:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FeatureCard
                icon={<Zap className="w-5 h-5" />}
                title="AI-Powered Templates"
                description="Generate custom invoice templates instantly"
              />
              <FeatureCard
                icon={<FileText className="w-5 h-5" />}
                title="Professional PDFs"
                description="Download beautiful, print-ready invoices"
              />
              <FeatureCard
                icon={<Mail className="w-5 h-5" />}
                title="Email Directly"
                description="Send invoices to clients with one click"
              />
              <FeatureCard
                icon={<CreditCard className="w-5 h-5" />}
                title="Get Paid Faster"
                description="Include payment links in your invoices"
              />
            </div>
          </div>
          <div className="text-center text-gray-600">
            <p>Let\'s create your first invoice together!</p>
          </div>
        </div>
      )
    },
    {
      id: 'ai-template',
      title: 'AI Template Generation',
      description: 'Tell us about your business and we\'ll create the perfect template',
      icon: <Zap className="w-8 h-8 text-blue-600" />,
      content: (
        <div className="space-y-6">
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Lightbulb className="w-5 h-5 mr-2 text-blue-600" />
              How it works:
            </h3>
            <ol className="space-y-3">
              <li className="flex items-start">
                <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 flex-shrink-0">1</span>
                <div>
                  <p className="font-medium">Describe your business</p>
                  <p className="text-sm text-gray-600">Just a few words about what you do</p>
                </div>
              </li>
              <li className="flex items-start">
                <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 flex-shrink-0">2</span>
                <div>
                  <p className="font-medium">AI analyzes your needs</p>
                  <p className="text-sm text-gray-600">We identify your industry and requirements</p>
                </div>
              </li>
              <li className="flex items-start">
                <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 flex-shrink-0">3</span>
                <div>
                  <p className="font-medium">Get a custom template</p>
                  <p className="text-sm text-gray-600">Perfectly tailored to your business</p>
                </div>
              </li>
            </ol>
          </div>
          <SampleTemplatePreview />
        </div>
      ),
      action: {
        label: 'Try It Now',
        onClick: () => {
          trackEvent('onboarding', 'click', { label: 'try_ai_template', metadata: { step: 'ai-template' } });
          router.push('/create-invoice');
        }
      }
    },
    {
      id: 'invoice-creation',
      title: 'Creating Your First Invoice',
      description: 'See how easy it is to create professional invoices',
      icon: <FileText className="w-8 h-8 text-green-600" />,
      content: (
        <div className="space-y-6">
          <div className="bg-green-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Quick Tips:</h3>
            <div className="space-y-3">
              <TipItem
                emoji="💡"
                tip="Use descriptive service names for clarity"
                example='e.g., "Website Design - Homepage" instead of "Design Work"'
              />
              <TipItem
                emoji="📅"
                tip="Set clear payment terms"
                example="Net 30 is standard, but Net 15 gets you paid faster"
              />
              <TipItem
                emoji="📝"
                tip="Add notes for important details"
                example="Include project milestones or special instructions"
              />
              <TipItem
                emoji="🎨"
                tip="Your branding matters"
                example="Upload your logo for a professional touch"
              />
            </div>
          </div>
          <DemoInvoiceForm />
        </div>
      ),
      action: {
        label: 'Create First Invoice',
        onClick: () => {
          trackEvent('onboarding', 'click', { label: 'create_first_invoice', metadata: { step: 'invoice-creation' } });
          router.push('/create-invoice');
        }
      }
    },
    {
      id: 'payment-features',
      title: 'Get Paid Faster',
      description: 'Accept payments directly through your invoices',
      icon: <CreditCard className="w-8 h-8 text-purple-600" />,
      content: (
        <div className="space-y-6">
          <div className="bg-purple-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Payment Features:</h3>
            <div className="space-y-4">
              <PaymentFeature
                title="One-Click Payments"
                description="Clients can pay instantly with credit card"
                stats="2x faster payment collection"
              />
              <PaymentFeature
                title="Automatic Tracking"
                description="Know when invoices are viewed and paid"
                stats="Real-time notifications"
              />
              <PaymentFeature
                title="Multiple Options"
                description="Support various payment methods"
                stats="Increase payment success by 40%"
              />
            </div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">
              Payment processing powered by Stripe - secure and reliable
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'success',
      title: 'You\'re All Set! 🎊',
      description: 'Start creating professional invoices today',
      icon: <CheckCircle className="w-8 h-8 text-green-600" />,
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <PartyPopper className="w-16 h-16 text-purple-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold mb-2">Congratulations!</h3>
            <p className="text-gray-600">You\'re ready to create amazing invoices</p>
          </div>
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6">
            <h4 className="font-semibold mb-3">Quick Actions:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <QuickAction
                icon={<FileText className="w-5 h-5" />}
                label="Create Invoice"
                onClick={() => router.push('/create-invoice')}
              />
              <QuickAction
                icon={<Users className="w-5 h-5" />}
                label="Manage Clients"
                onClick={() => router.push('/clients')}
              />
              <QuickAction
                icon={<TrendingUp className="w-5 h-5" />}
                label="View Dashboard"
                onClick={() => router.push('/dashboard')}
              />
              <QuickAction
                icon={<Sparkles className="w-5 h-5" />}
                label="Explore Templates"
                onClick={() => router.push('/templates')}
              />
            </div>
          </div>
        </div>
      ),
      action: {
        label: 'Start Creating',
        onClick: () => {
          setShowConfetti(true);
          trackEvent('onboarding', 'complete', { metadata: { totalSteps: steps.length } });
          setTimeout(() => {
            onComplete?.();
            router.push('/create-invoice');
          }, 2000);
        }
      }
    }
  ];

  useEffect(() => {
    // Track onboarding start
    if (isNewUser && currentStep === 0) {
      trackEvent('onboarding', 'started', { metadata: { totalSteps: steps.length } });
    }
  }, [isNewUser, currentStep, steps.length]);

  const handleNext = () => {
    const currentStepId = steps[currentStep].id;
    setCompletedSteps(prev => new Set(Array.from(prev).concat(currentStepId)));
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      trackEvent('onboarding', 'complete', { 
        label: 'step_completed',
        metadata: {
          step: currentStepId,
          nextStep: steps[currentStep + 1].id
        }
      });
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    trackEvent('onboarding', 'abandoned', { 
      label: 'skipped',
      metadata: {
        skippedAtStep: steps[currentStep].id,
        completedSteps: Array.from(completedSteps)
      }
    });
    onSkip?.();
  };

  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-2xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="relative bg-gradient-to-r from-purple-600 to-blue-600 p-6 text-white">
            <button
              onClick={handleSkip}
              className="absolute top-4 right-4 text-white/80 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
            
            <div className="flex items-center space-x-4">
              {steps[currentStep].icon}
              <div>
                <h2 className="text-2xl font-bold">{steps[currentStep].title}</h2>
                <p className="text-white/80">{steps[currentStep].description}</p>
              </div>
            </div>
            
            {/* Progress bar */}
            <div className="mt-6 bg-white/20 rounded-full h-2 overflow-hidden">
              <motion.div
                className="h-full bg-white"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-280px)]">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {steps[currentStep].content}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-6 bg-gray-50">
            <div className="flex items-center justify-between">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className={cn(
                  "flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors",
                  currentStep === 0
                    ? "text-gray-400 cursor-not-allowed"
                    : "text-gray-600 hover:bg-gray-200"
                )}
              >
                <ChevronLeft className="w-5 h-5" />
                <span>Previous</span>
              </button>

              <div className="flex items-center space-x-2">
                {steps.map((_, index) => (
                  <div
                    key={index}
                    className={cn(
                      "w-2 h-2 rounded-full transition-colors",
                      index === currentStep
                        ? "bg-purple-600 w-8"
                        : index < currentStep
                        ? "bg-purple-400"
                        : "bg-gray-300"
                    )}
                  />
                ))}
              </div>

              <div className="flex items-center space-x-3">
                {steps[currentStep].action && (
                  <button
                    onClick={steps[currentStep].action.onClick}
                    className="px-4 py-2 bg-white border border-purple-600 text-purple-600 rounded-lg hover:bg-purple-50 transition-colors"
                  >
                    {steps[currentStep].action.label}
                  </button>
                )}
                <button
                  onClick={currentStep === steps.length - 1 ? steps[currentStep].action?.onClick : handleNext}
                  className="flex items-center space-x-2 px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <span>{currentStep === steps.length - 1 ? 'Get Started' : 'Next'}</span>
                  <ArrowRight className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Confetti animation */}
      <AnimatePresence>
        {showConfetti && <ConfettiAnimation />}
      </AnimatePresence>
    </>
  );
}

// Component helpers
function FeatureCard({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) {
  return (
    <div className="flex items-start space-x-3">
      <div className="bg-white p-2 rounded-lg shadow-sm">
        {icon}
      </div>
      <div>
        <h4 className="font-medium">{title}</h4>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    </div>
  );
}

function TipItem({ emoji, tip, example }: { emoji: string; tip: string; example: string }) {
  return (
    <div className="flex items-start space-x-3">
      <span className="text-2xl">{emoji}</span>
      <div className="flex-1">
        <p className="font-medium">{tip}</p>
        <p className="text-sm text-gray-600">{example}</p>
      </div>
    </div>
  );
}

function PaymentFeature({ title, description, stats }: { title: string; description: string; stats: string }) {
  return (
    <div className="border-l-4 border-purple-600 pl-4">
      <h4 className="font-medium">{title}</h4>
      <p className="text-sm text-gray-600">{description}</p>
      <p className="text-xs text-purple-600 font-medium mt-1">{stats}</p>
    </div>
  );
}

function QuickAction({ icon, label, onClick }: { icon: React.ReactNode; label: string; onClick: () => void }) {
  return (
    <button
      onClick={onClick}
      className="flex items-center space-x-2 p-3 bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-colors"
    >
      <span className="text-purple-600">{icon}</span>
      <span className="font-medium">{label}</span>
    </button>
  );
}

function SampleTemplatePreview() {
  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="bg-white rounded border border-gray-200 p-4 text-sm">
        <div className="font-semibold text-purple-600 mb-2">Sample AI Template Output:</div>
        <div className="space-y-2 text-gray-600">
          <p><span className="font-medium">Industry:</span> Web Development</p>
          <p><span className="font-medium">Template:</span> Professional Services Invoice</p>
          <p><span className="font-medium">Custom Fields:</span> Project phases, hourly breakdown</p>
          <p><span className="font-medium">Payment Terms:</span> Net 30, 2% early payment discount</p>
        </div>
      </div>
    </div>
  );
}

function DemoInvoiceForm() {
  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="bg-white rounded border border-gray-200 p-4">
        <div className="space-y-3">
          <div className="border-b pb-2">
            <h4 className="font-semibold">Invoice #001</h4>
            <p className="text-sm text-gray-600">Due: 30 days</p>
          </div>
          <div>
            <p className="text-sm font-medium">To: Sample Client Inc.</p>
            <p className="text-xs text-gray-600">123 Business St, City</p>
          </div>
          <div className="bg-gray-50 rounded p-3">
            <div className="flex justify-between text-sm">
              <span>Website Development</span>
              <span className="font-medium">$2,500</span>
            </div>
          </div>
          <div className="border-t pt-2">
            <div className="flex justify-between font-semibold">
              <span>Total</span>
              <span>$2,500</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function ConfettiAnimation() {
  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      {[...Array(50)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-2 h-2 bg-purple-600"
          initial={{
            top: '50%',
            left: '50%',
            opacity: 1,
          }}
          animate={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            opacity: 0,
            rotate: Math.random() * 360,
          }}
          transition={{
            duration: 2,
            ease: 'easeOut',
            delay: i * 0.02,
          }}
          style={{
            backgroundColor: ['#9333ea', '#3b82f6', '#10b981', '#f59e0b', '#ef4444'][Math.floor(Math.random() * 5)],
          }}
        />
      ))}
    </div>
  );
}