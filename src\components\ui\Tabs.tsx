import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

export interface Tab {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
}

export interface TabsProps {
  tabs: Tab[];
  defaultTab?: string;
  onChange?: (tabId: string) => void;
  className?: string;
  tabsClassName?: string;
  contentClassName?: string;
  variant?: 'default' | 'pills' | 'underline';
}

const Tabs: React.FC<TabsProps> = ({
  tabs,
  defaultTab,
  onChange,
  className,
  tabsClassName,
  contentClassName,
  variant = 'default',
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);
  const [indicatorStyle, setIndicatorStyle] = useState<React.CSSProperties>({});
  const tabRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  useEffect(() => {
    const activeTabElement = tabRefs.current[activeTab];
    if (activeTabElement) {
      const { offsetLeft, offsetWidth } = activeTabElement;
      setIndicatorStyle({
        left: offsetLeft,
        width: offsetWidth,
      });
    }
  }, [activeTab]);

  const handleTabClick = (tabId: string) => {
    const tab = tabs.find((t) => t.id === tabId);
    if (tab && !tab.disabled) {
      setActiveTab(tabId);
      onChange?.(tabId);
    }
  };

  const activeTabContent = tabs.find((tab) => tab.id === activeTab)?.content;

  const variantClasses = {
    default: {
      tabs: 'border-b border-gray-200',
      tab: 'px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors',
      activeTab: 'text-gray-900',
      indicator: 'h-0.5 bg-primary',
    },
    pills: {
      tabs: 'bg-gray-100 p-1 rounded-lg',
      tab: 'px-4 py-2 text-sm font-medium text-gray-600 rounded-md transition-all',
      activeTab: 'text-white bg-primary shadow-sm',
      indicator: 'h-full bg-primary rounded-md',
    },
    underline: {
      tabs: '',
      tab: 'px-6 py-3 text-sm font-medium text-gray-600 hover:text-primary transition-colors',
      activeTab: 'text-primary',
      indicator: 'h-1 bg-primary rounded-full',
    },
  };

  const classes = variantClasses[variant];

  return (
    <div className={cn('w-full', className)}>
      <div className={cn('relative', classes.tabs, tabsClassName)}>
        <div className="flex space-x-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              ref={(el) => {
                if (el) {
                  tabRefs.current[tab.id] = el;
                }
              }}
              onClick={() => handleTabClick(tab.id)}
              disabled={tab.disabled}
              className={cn(
                classes.tab,
                activeTab === tab.id && classes.activeTab,
                tab.disabled && 'opacity-50 cursor-not-allowed',
                variant === 'pills' && activeTab === tab.id && 'relative z-10'
              )}
            >
              {tab.label}
            </button>
          ))}
        </div>
        {variant !== 'pills' && (
          <motion.div
            className={cn('absolute bottom-0', classes.indicator)}
            initial={false}
            animate={{
              left: indicatorStyle.left,
              width: indicatorStyle.width,
            }}
            transition={{
              type: 'spring',
              stiffness: 500,
              damping: 30,
            }}
          />
        )}
      </div>
      <div className={cn('mt-4', contentClassName)}>
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
        >
          {activeTabContent}
        </motion.div>
      </div>
    </div>
  );
};

export { Tabs };