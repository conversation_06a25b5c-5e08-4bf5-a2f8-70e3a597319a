// Database Models for Template Invoice System
import { ObjectId } from 'mongodb';

// User Model
export interface UserDocument {
  _id?: ObjectId;
  email: string;
  name: string;
  googleId: string;
  image?: string;
  subscription: {
    plan: 'free' | 'pro';
    invoicesUsed: number;
    resetDate: Date;
    stripeCustomerId?: string;
    stripeSubscriptionId?: string;
    subscriptionStatus?: 'active' | 'cancelled' | 'past_due';
  };
  createdAt: Date;
  lastLogin: Date;
  preferences?: {
    defaultTemplate?: string;
    defaultCurrency?: string;
    timezone?: string;
  };
}

// Invoice Model
export interface InvoiceDocument {
  _id?: ObjectId;
  userId: ObjectId;
  invoiceNumber: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  
  // Business Information
  businessInfo: {
    name: string;
    address: string;
    city: string;
    email: string;
    phone?: string;
    website?: string;
    logo?: string;
  };
  
  // Client Information
  clientInfo: {
    name: string;
    email: string;
    address: string;
    city: string;
    contactPerson?: string;
  };
  
  // Invoice Details
  invoiceDate: Date;
  dueDate: Date;
  terms: string;
  
  // Line Items
  lineItems: Array<{
    id: string;
    description: string;
    details?: string;
    quantity: number;
    rate: number;
    amount: number;
    taxable?: boolean;
  }>;
  
  // Totals
  totals: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  
  // Template & Styling
  templateId: string;
  customStyling?: {
    primaryColor?: string;
    font?: string;
    logoPosition?: string;
  };
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  sentAt?: Date;
  paidAt?: Date;
  
  // File Storage
  pdfUrl?: string;
  pdfGeneratedAt?: Date;
  
  // Payment Information
  payment?: {
    stripePaymentLinkId?: string;
    stripePaymentLinkUrl?: string;
    stripePaymentIntentId?: string;
    stripeSessionId?: string;
    amountPaid?: number;
    paymentMethod?: string;
    linkCreatedAt?: Date;
    paidAt?: Date;
  };
  
  // Additional Fields
  notes?: string;
  internalNotes?: string;
  currency: string;
  language?: string;
}

// Client Model
export interface ClientDocument {
  _id?: ObjectId;
  userId: ObjectId;
  name: string;
  email: string;
  phone?: string;
  address: string;
  city: string;
  country?: string;
  contactPerson?: string;
  
  // Business Info
  businessType?: string;
  taxId?: string;
  website?: string;
  
  // Invoice History
  lastInvoiceDate?: Date;
  totalInvoiced: number;
  invoiceCount: number;
  averageInvoiceAmount: number;
  
  // Status
  status: 'active' | 'inactive' | 'blocked';
  paymentTerms?: string;
  preferredCurrency?: string;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  
  // Additional Info
  notes?: string;
  tags?: string[];
}

// Template Model
export interface TemplateDocument {
  _id?: ObjectId;
  userId: ObjectId;
  name: string;
  description?: string;
  industry: string;
  isCustom: boolean;
  isPublic: boolean; // For sharing templates
  
  // Template Structure
  templateStructure: {
    layout: 'standard' | 'modern' | 'classic' | 'creative' | 'simple' | 'service';
    sections: Array<{
      id: string;
      type: 'header' | 'business' | 'client' | 'items' | 'totals' | 'notes' | 'footer';
      enabled: boolean;
      order: number;
      settings?: Record<string, any>;
    }>;
  };
  
  // Styling
  styling: {
    primaryColor: string;
    secondaryColor?: string;
    font: string;
    fontSize: number;
    headerStyle?: Record<string, any>;
    tableStyle?: Record<string, any>;
    footerStyle?: Record<string, any>;
  };
  
  // Preview Data
  previewData?: {
    businessName: string;
    clientName: string;
    sampleItems: Array<{
      description: string;
      quantity: number;
      rate: number;
    }>;
  };
  
  // Usage Stats
  usageCount: number;
  lastUsed?: Date;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  
  // Additional
  tags?: string[];
  category?: string;
}

// Payment Model (for tracking Stripe payments)
export interface PaymentDocument {
  _id?: ObjectId;
  userId: ObjectId;
  invoiceId?: ObjectId;
  stripePaymentIntentId: string;
  stripePaymentMethodId?: string;
  
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed' | 'cancelled';
  
  // Payment Details
  paymentMethod: 'card' | 'bank_transfer' | 'other';
  description?: string;
  
  // Timestamps
  createdAt: Date;
  paidAt?: Date;
  
  // Metadata
  metadata?: Record<string, any>;
}

// Subscription Model (separate from User for detailed tracking)
export interface SubscriptionDocument {
  _id?: ObjectId;
  userId: ObjectId;
  stripeSubscriptionId: string;
  stripeCustomerId: string;
  
  plan: 'free' | 'pro';
  status: 'active' | 'cancelled' | 'past_due' | 'unpaid';
  
  // Billing
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  canceledAt?: Date;
  
  // Usage Tracking
  invoicesUsed: number;
  resetDate: Date;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  
  // Metadata
  metadata?: Record<string, any>;
}

// Activity Log Model (for audit trail)
export interface ActivityLogDocument {
  _id?: ObjectId;
  userId: ObjectId;
  action: string;
  resourceType: 'invoice' | 'client' | 'template' | 'user' | 'subscription';
  resourceId?: ObjectId;
  
  details: {
    description: string;
    metadata?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
  };
  
  createdAt: Date;
}

// Export collection names as constants
export const COLLECTIONS = {
  USERS: 'users',
  INVOICES: 'invoices',
  CLIENTS: 'clients',
  TEMPLATES: 'templates',
  PAYMENTS: 'payments',
  SUBSCRIPTIONS: 'subscriptions',
  ACTIVITY_LOGS: 'activity_logs',
  // NextAuth collections
  ACCOUNTS: 'accounts',
  SESSIONS: 'sessions',
  VERIFICATION_TOKENS: 'verificationtokens'
} as const;

// Type helpers
export type CreateUserInput = Omit<UserDocument, '_id' | 'createdAt' | 'lastLogin' | 'subscription'> & {
  subscription?: Partial<UserDocument['subscription']>;
};

export type CreateInvoiceInput = Omit<InvoiceDocument, '_id' | 'createdAt' | 'updatedAt'>;
export type UpdateInvoiceInput = Partial<Omit<InvoiceDocument, '_id' | 'userId' | 'createdAt'>>;

export type CreateClientInput = Omit<ClientDocument, '_id' | 'createdAt' | 'updatedAt' | 'totalInvoiced' | 'invoiceCount' | 'averageInvoiceAmount'>;
export type UpdateClientInput = Partial<Omit<ClientDocument, '_id' | 'userId' | 'createdAt'>>;

export type CreateTemplateInput = Omit<TemplateDocument, '_id' | 'createdAt' | 'updatedAt' | 'usageCount'>;
export type UpdateTemplateInput = Partial<Omit<TemplateDocument, '_id' | 'userId' | 'createdAt'>>;