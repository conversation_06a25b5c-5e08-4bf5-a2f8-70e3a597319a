import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { MongoDBAdapter } from "@next-auth/mongodb-adapter";
import clientPromise from "@/lib/mongodb";
import { createUser, updateUserLastLogin } from "@/lib/user-service";

export const authOptions: NextAuthOptions = {
  adapter: MongoDBAdapter(clientPromise, {
    databaseName: "templateinvoices", // Explicitly set database name
  }),
  
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      },
      // Add this to allow account linking
      allowDangerousEmailAccountLinking: true,
    }),
  ],
  
  session: { 
    strategy: "jwt" // Change from "database" to "jwt"
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      console.log("🔍 [SIGNIN] Attempting sign in for:", user.email);
      
      if (account?.provider === "google") {
        try {
          // Create or update user in our system
          await createUser({
            email: user.email!,
            name: user.name!,
            googleId: account.providerAccountId,
            image: user.image || undefined
          });
          console.log("✅ [SIGNIN] User created/updated successfully");
          return true;
        } catch (error) {
          console.error("❌ [SIGNIN] Error creating user:", error);
          return false;
        }
      }
      return true;
    },
    
    async redirect({ url, baseUrl }) {
      console.log("🔍 [REDIRECT] Redirect called with:", { url, baseUrl });
      return baseUrl + '/dashboard';
    },
    async session({ session, token }) {
      if (session.user?.email) {
        try {
          // Update last login and get user data
          const userData = await updateUserLastLogin(session.user.email);
          if (userData && userData._id) {
            session.user.id = userData._id.toString();
            session.user.subscription = userData.subscription;
            session.user.createdAt = userData.createdAt;
          }
        } catch (error) {
          console.error("❌ [SESSION] Error updating user:", error);
        }
      }
      return session;
    },
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    }
  },
  
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  
  debug: true,
};

// Extend NextAuth types
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      image?: string;
      subscription: {
        plan: 'free' | 'pro';
        invoicesUsed: number;
        resetDate: Date;
        stripeCustomerId?: string;
        stripeSubscriptionId?: string;
      };
      createdAt: Date;
    };
  }

  interface User {
    id: string;
    subscription?: {
      plan: 'free' | 'pro';
      invoicesUsed: number;
      resetDate: Date;
      stripeCustomerId?: string;
      stripeSubscriptionId?: string;
    };
  }
}
