'use client'

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { 
  Activity, 
  Users, 
  FileText, 
  CreditCard, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'down';
  services: {
    database: { status: string; responseTime: number };
    ai: { status: string; successRate: number };
    email: { status: string; deliveryRate: number };
    payment: { status: string; successRate: number };
  };
  performance: {
    avgResponseTime: number;
    errorRate: number;
    activeUsers: number;
  };
}

interface CriticalMetrics {
  userSignups: number;
  firstInvoiceCreated: number;
  aiTemplateSuccess: number;
  aiTemplateFailure: number;
  invoiceCompletionRate: number;
  pdfDownloads: number;
  emailsSent: number;
  paymentLinkClicks: number;
  averageResponseTime: number;
  errorRate: number;
}

interface DashboardData {
  currentActiveUsers: number;
  todayStats: {
    signups: number;
    invoicesCreated: number;
    revenue: number;
    aiGenerations: number;
  };
  last24Hours: {
    hourlyActivity: Array<{ hour: number; events: number }>;
  };
  alerts: Array<{ type: string; message: string; severity: 'low' | 'medium' | 'high' }>;
}

export default function MonitoringPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [view, setView] = useState<'dashboard' | 'health' | 'metrics'>('dashboard');
  const [period, setPeriod] = useState<'today' | 'week' | 'month'>('today');
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<{
    dashboard?: DashboardData;
    health?: SystemHealth;
    metrics?: CriticalMetrics;
  }>({});

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    } else if (status === 'authenticated') {
      fetchData();
    }
  }, [status, view, period]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/analytics/monitoring?view=${view}&period=${period}`);
      if (response.ok) {
        const result = await response.json();
        setData(prev => ({ ...prev, [view]: result }));
      }
    } catch (error) {
      console.error('Error fetching monitoring data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">System Monitoring</h1>
          <p className="text-gray-600 mt-2">Real-time analytics and system health</p>
        </div>

        {/* View Tabs */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex -mb-px">
              <button
                onClick={() => setView('dashboard')}
                className={cn(
                  "py-4 px-6 border-b-2 font-medium text-sm",
                  view === 'dashboard'
                    ? "border-purple-600 text-purple-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                )}
              >
                <Activity className="w-4 h-4 inline mr-2" />
                Dashboard
              </button>
              <button
                onClick={() => setView('health')}
                className={cn(
                  "py-4 px-6 border-b-2 font-medium text-sm",
                  view === 'health'
                    ? "border-purple-600 text-purple-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                )}
              >
                <CheckCircle className="w-4 h-4 inline mr-2" />
                System Health
              </button>
              <button
                onClick={() => setView('metrics')}
                className={cn(
                  "py-4 px-6 border-b-2 font-medium text-sm",
                  view === 'metrics'
                    ? "border-purple-600 text-purple-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                )}
              >
                <TrendingUp className="w-4 h-4 inline mr-2" />
                Metrics
              </button>
            </nav>
          </div>

          {/* Period Selector */}
          <div className="p-4 flex justify-end">
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value as any)}
              className="rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            >
              <option value="today">Today</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
            </select>
          </div>
        </div>

        {/* Content */}
        {view === 'dashboard' && data.dashboard && (
          <DashboardView data={data.dashboard} />
        )}
        
        {view === 'health' && data.health && (
          <HealthView data={data.health} />
        )}
        
        {view === 'metrics' && data.metrics && (
          <MetricsView data={data.metrics} />
        )}
      </div>
    </div>
  );
}

function DashboardView({ data }: { data: DashboardData }) {
  return (
    <div className="space-y-6">
      {/* Active Users */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Active Users</h2>
          <span className="text-3xl font-bold text-purple-600">{data.currentActiveUsers}</span>
        </div>
        <p className="text-sm text-gray-600">Users active in the last 5 minutes</p>
      </div>

      {/* Today's Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <StatCard
          icon={<Users className="w-5 h-5" />}
          label="New Signups"
          value={data.todayStats.signups}
          color="blue"
        />
        <StatCard
          icon={<FileText className="w-5 h-5" />}
          label="Invoices Created"
          value={data.todayStats.invoicesCreated}
          color="green"
        />
        <StatCard
          icon={<CreditCard className="w-5 h-5" />}
          label="Revenue"
          value={`$${data.todayStats.revenue}`}
          color="purple"
        />
        <StatCard
          icon={<Zap className="w-5 h-5" />}
          label="AI Generations"
          value={data.todayStats.aiGenerations}
          color="yellow"
        />
      </div>

      {/* Alerts */}
      {data.alerts.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">System Alerts</h2>
          <div className="space-y-2">
            {data.alerts.map((alert, index) => (
              <Alert key={index} alert={alert} />
            ))}
          </div>
        </div>
      )}

      {/* Activity Chart (simplified) */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">24-Hour Activity</h2>
        <div className="h-40 flex items-end space-x-2">
          {data.last24Hours.hourlyActivity.map((hour, index) => (
            <div
              key={index}
              className="flex-1 bg-purple-200 rounded-t"
              style={{
                height: `${(hour.events / Math.max(...data.last24Hours.hourlyActivity.map(h => h.events))) * 100}%`,
                minHeight: '4px'
              }}
              title={`Hour ${hour.hour}: ${hour.events} events`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

function HealthView({ data }: { data: SystemHealth }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'degraded':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'down':
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Overall Status */}
      <div className={cn(
        "bg-white rounded-lg shadow p-6 border-l-4",
        data.status === 'healthy' ? "border-green-500" :
        data.status === 'degraded' ? "border-yellow-500" : "border-red-500"
      )}>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">System Status</h2>
            <p className="text-gray-600 capitalize">{data.status}</p>
          </div>
          {getStatusIcon(data.status)}
        </div>
      </div>

      {/* Services */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ServiceCard
          name="Database"
          status={data.services.database.status}
          metric={`${data.services.database.responseTime}ms`}
          metricLabel="Response Time"
        />
        <ServiceCard
          name="AI Service"
          status={data.services.ai.status}
          metric={`${data.services.ai.successRate.toFixed(1)}%`}
          metricLabel="Success Rate"
        />
        <ServiceCard
          name="Email Service"
          status={data.services.email.status}
          metric={`${data.services.email.deliveryRate}%`}
          metricLabel="Delivery Rate"
        />
        <ServiceCard
          name="Payment Service"
          status={data.services.payment.status}
          metric={`${data.services.payment.successRate}%`}
          metricLabel="Success Rate"
        />
      </div>

      {/* Performance Metrics */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Performance Overview</h2>
        <div className="grid grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-gray-600">Avg Response Time</p>
            <p className="text-2xl font-bold">{data.performance.avgResponseTime.toFixed(0)}ms</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Error Rate</p>
            <p className="text-2xl font-bold">{data.performance.errorRate.toFixed(1)}%</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Active Users</p>
            <p className="text-2xl font-bold">{data.performance.activeUsers}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function MetricsView({ data }: { data: CriticalMetrics }) {
  const aiSuccessRate = data.aiTemplateSuccess + data.aiTemplateFailure > 0
    ? (data.aiTemplateSuccess / (data.aiTemplateSuccess + data.aiTemplateFailure)) * 100
    : 0;

  return (
    <div className="space-y-6">
      {/* Critical Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <MetricCard
          label="User Signups"
          value={data.userSignups}
          icon={<Users className="w-5 h-5" />}
        />
        <MetricCard
          label="First Invoices"
          value={data.firstInvoiceCreated}
          icon={<FileText className="w-5 h-5" />}
        />
        <MetricCard
          label="Invoice Completion Rate"
          value={`${data.invoiceCompletionRate.toFixed(1)}%`}
          icon={<TrendingUp className="w-5 h-5" />}
        />
        <MetricCard
          label="AI Success Rate"
          value={`${aiSuccessRate.toFixed(1)}%`}
          icon={<Zap className="w-5 h-5" />}
          subValue={`${data.aiTemplateSuccess} / ${data.aiTemplateSuccess + data.aiTemplateFailure}`}
        />
        <MetricCard
          label="PDF Downloads"
          value={data.pdfDownloads}
          icon={<FileText className="w-5 h-5" />}
        />
        <MetricCard
          label="Emails Sent"
          value={data.emailsSent}
          icon={<Mail className="w-5 h-5" />}
        />
        <MetricCard
          label="Payment Link Clicks"
          value={data.paymentLinkClicks}
          icon={<CreditCard className="w-5 h-5" />}
        />
        <MetricCard
          label="Avg Response Time"
          value={`${data.averageResponseTime.toFixed(0)}ms`}
          icon={<Clock className="w-5 h-5" />}
        />
        <MetricCard
          label="Error Rate"
          value={`${data.errorRate.toFixed(1)}%`}
          icon={<AlertCircle className="w-5 h-5" />}
          isError={data.errorRate > 5}
        />
      </div>
    </div>
  );
}

// Component helpers
function StatCard({ icon, label, value, color }: any) {
  const colorClasses: Record<string, string> = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    purple: 'bg-purple-50 text-purple-600',
    yellow: 'bg-yellow-50 text-yellow-600',
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className={cn("w-10 h-10 rounded-lg flex items-center justify-center mb-4", colorClasses[color])}>
        {icon}
      </div>
      <p className="text-sm text-gray-600">{label}</p>
      <p className="text-2xl font-bold mt-1">{value}</p>
    </div>
  );
}

function ServiceCard({ name, status, metric, metricLabel }: any) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'degraded':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      default:
        return <XCircle className="w-5 h-5 text-red-500" />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">{name}</h3>
        {getStatusIcon(status)}
      </div>
      <p className="text-sm text-gray-600">{metricLabel}</p>
      <p className="text-2xl font-bold">{metric}</p>
    </div>
  );
}

function MetricCard({ label, value, icon, subValue, isError }: any) {
  return (
    <div className={cn(
      "bg-white rounded-lg shadow p-6",
      isError && "border-l-4 border-red-500"
    )}>
      <div className="flex items-center justify-between mb-2">
        <span className={cn("text-gray-400", isError && "text-red-500")}>
          {icon}
        </span>
      </div>
      <p className="text-sm text-gray-600">{label}</p>
      <p className={cn("text-2xl font-bold mt-1", isError && "text-red-600")}>
        {value}
      </p>
      {subValue && (
        <p className="text-xs text-gray-500 mt-1">{subValue}</p>
      )}
    </div>
  );
}

function Alert({ alert }: { alert: any }) {
  const severityClasses: Record<string, string> = {
    low: 'bg-blue-50 text-blue-800 border-blue-200',
    medium: 'bg-yellow-50 text-yellow-800 border-yellow-200',
    high: 'bg-red-50 text-red-800 border-red-200',
  };

  return (
    <div className={cn(
      "p-4 rounded-lg border",
      severityClasses[alert.severity]
    )}>
      <div className="flex items-center">
        <AlertCircle className="w-4 h-4 mr-2" />
        <span className="text-sm font-medium">{alert.type}</span>
        <span className="text-sm ml-2">{alert.message}</span>
      </div>
    </div>
  );
}

// Add missing import
import { Mail } from 'lucide-react';