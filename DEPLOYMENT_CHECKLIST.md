# Vercel Deployment Checklist

## Pre-Deployment Steps

### 1. Environment Variables (Required in Vercel Dashboard)
```
# Required for basic functionality
MONGODB_URI=your_mongodb_connection_string
NEXTAUTH_SECRET=generate_secure_secret_32_chars
NEXTAUTH_URL=https://your-domain.vercel.app

# Google OAuth (Required for authentication)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Email Configuration (Optional - for sending invoices)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Stripe (Optional - for payments)
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRO_PRICE_ID=price_...

# OpenAI (Optional - for AI features)
OPENAI_API_KEY=sk-...

# Public URLs
NEXT_PUBLIC_BASE_URL=https://your-domain.vercel.app

# Feature Flags
NEXT_PUBLIC_AI_CONFIGURED=false
NEXT_PUBLIC_EMAIL_CONFIGURED=false
NEXT_PUBLIC_STRIPE_CONFIGURED=false
```

### 2. MongoDB Setup
1. Create MongoDB Atlas account (free tier available)
2. Create cluster and database
3. Create database user with read/write permissions
4. Add Vercel IP addresses to network access (or allow from anywhere: 0.0.0.0/0)
5. Copy connection string to MONGODB_URI

### 3. Google OAuth Setup
1. Go to Google Cloud Console
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `https://your-domain.vercel.app/api/auth/callback/google`
   - `http://localhost:3000/api/auth/callback/google` (for local dev)

### 4. Deployment Steps

1. **Install Vercel CLI** (optional):
   ```bash
   npm i -g vercel
   ```

2. **Deploy to Vercel**:
   
   Option A - Using Vercel Dashboard:
   - Connect GitHub repository
   - Import project
   - Add environment variables
   - Deploy

   Option B - Using CLI:
   ```bash
   vercel --prod
   ```

### 5. Post-Deployment Verification

- [ ] Homepage loads correctly
- [ ] Authentication works (Google sign-in)
- [ ] Can access /templates page
- [ ] Can create invoice (if authenticated)
- [ ] MongoDB connection successful
- [ ] Images and assets loading
- [ ] SEO meta tags present
- [ ] Sitemap accessible at /sitemap.xml
- [ ] Robots.txt accessible at /robots.txt

## Known Issues & Solutions

### Issue: Build fails with TypeScript errors
**Solution**: The codebase has been tested and should build successfully. Ensure all dependencies are installed.

### Issue: MongoDB connection fails
**Solution**: 
- Check connection string format
- Verify IP whitelist includes Vercel IPs
- Ensure database user has correct permissions

### Issue: Google OAuth not working
**Solution**:
- Verify redirect URIs match exactly
- Check client ID and secret are correct
- Ensure NEXTAUTH_URL matches your domain

### Issue: PDF generation fails
**Solution**: PDF generation using Puppeteer may not work on Vercel's serverless functions. The app will fallback to HTML invoices.

## Optional Features

These features require additional setup but are not required for MVP:

1. **Email Sending**: Configure SMTP settings
2. **Stripe Payments**: Set up Stripe account and add products
3. **AI Features**: Add OpenAI API key

## Performance Optimizations

1. **Image Optimization**: All images use Next.js Image component
2. **Font Optimization**: Using next/font for optimal loading
3. **Code Splitting**: Automatic with Next.js
4. **Caching**: Configured in vercel.json

## Security Checklist

- [x] Environment variables not exposed to client
- [x] Authentication required for protected routes
- [x] CORS headers configured
- [x] Security headers in vercel.json
- [x] Input validation on all forms
- [x] SQL injection protection (using MongoDB)
- [x] XSS protection enabled

## Monitoring

After deployment:
1. Set up Vercel Analytics (automatic)
2. Monitor build logs in Vercel dashboard
3. Check function logs for errors
4. Use Google Search Console for SEO monitoring

## Support

If you encounter issues:
1. Check Vercel deployment logs
2. Verify all environment variables are set
3. Test locally with production environment variables
4. Check browser console for client-side errors