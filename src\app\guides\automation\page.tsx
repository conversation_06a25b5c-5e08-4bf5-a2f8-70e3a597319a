import type { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { <PERSON>R<PERSON>, Zap, Clock, Repeat, Mail, Calendar, Settings, CheckCircle } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Invoice Automation and Recurring Billing Setup',
  description: 'Set up automatic invoicing for recurring clients. Save time with automated billing and payment reminders.',
  keywords: ['recurring invoices', 'invoice automation', 'automatic billing', 'automated reminders', 'invoice scheduling'],
  openGraph: {
    title: 'Invoice Automation and Recurring Billing Setup',
    description: 'Set up automatic invoicing for recurring clients. Save time with automated billing and payment reminders.',
    type: 'article',
  },
}

export default function AutomationGuide() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Invoice Automation & Recurring Billing
            </h1>
            <p className="text-xl text-orange-100 max-w-3xl mx-auto">
              Save time with automated billing and never miss a payment deadline again
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-gray-900">Home</Link>
          <span>/</span>
          <Link href="/guides" className="hover:text-gray-900">Guides</Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">Automation</span>
        </nav>

        {/* Introduction */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Transform Your Billing Workflow</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Invoice automation eliminates repetitive tasks, reduces human error, and ensures you never 
            miss sending a bill. Whether you have monthly retainers, subscription services, or regular 
            maintenance contracts, automation keeps your cash flow steady and predictable.
          </p>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <Clock className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="font-semibold mb-2">Save Time</h3>
              <p className="text-gray-600 text-sm">Automate up to 80% of your billing tasks and focus on growing your business</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <Repeat className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="font-semibold mb-2">Consistent Cash Flow</h3>
              <p className="text-gray-600 text-sm">Regular, automated billing improves payment predictability</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold mb-2">Fewer Errors</h3>
              <p className="text-gray-600 text-sm">Eliminate manual data entry mistakes and missed invoices</p>
            </div>
          </div>
        </section>

        {/* Recurring Invoices */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4 flex items-center">
            <Repeat className="w-8 h-8 mr-3 text-orange-600" />
            Setting Up Recurring Invoices
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Recurring invoices are perfect for subscription services, retainers, and any regular billing. 
            Set them up once and they'll run automatically according to your schedule.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Step-by-Step Setup</h3>
              
              <ol className="space-y-4">
                <li className="flex">
                  <span className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-4 mt-0.5">1</span>
                  <div className="flex-1">
                    <h4 className="font-semibold mb-1">Create Your Base Invoice</h4>
                    <p className="text-gray-700 text-sm">Start by creating a regular invoice with all the details you want to repeat - client info, line items, amounts, and terms.</p>
                  </div>
                </li>
                <li className="flex">
                  <span className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-4 mt-0.5">2</span>
                  <div className="flex-1">
                    <h4 className="font-semibold mb-1">Enable Recurring Billing</h4>
                    <p className="text-gray-700 text-sm">In the invoice options, toggle on "Make this a recurring invoice" and select your billing frequency.</p>
                  </div>
                </li>
                <li className="flex">
                  <span className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-4 mt-0.5">3</span>
                  <div className="flex-1">
                    <h4 className="font-semibold mb-1">Configure Schedule</h4>
                    <p className="text-gray-700 text-sm">Set the start date, frequency (weekly, monthly, quarterly), and optional end date or number of occurrences.</p>
                  </div>
                </li>
                <li className="flex">
                  <span className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-4 mt-0.5">4</span>
                  <div className="flex-1">
                    <h4 className="font-semibold mb-1">Review and Activate</h4>
                    <p className="text-gray-700 text-sm">Double-check all settings and activate the recurring schedule. You'll receive confirmation via email.</p>
                  </div>
                </li>
              </ol>
            </div>

            <div className="bg-orange-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Recurring Schedule Options</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-orange-800 mb-3">Frequency Options</h4>
                  <ul className="space-y-2 text-orange-700 text-sm">
                    <li>• Weekly (every 7 days)</li>
                    <li>• Bi-weekly (every 14 days)</li>
                    <li>• Monthly (same date each month)</li>
                    <li>• Quarterly (every 3 months)</li>
                    <li>• Semi-annually (every 6 months)</li>
                    <li>• Annually (yearly billing)</li>
                    <li>• Custom intervals (every X days)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-orange-800 mb-3">Advanced Settings</h4>
                  <ul className="space-y-2 text-orange-700 text-sm">
                    <li>• Auto-send on generation</li>
                    <li>• Send preview before sending</li>
                    <li>• Custom email templates</li>
                    <li>• Automatic payment processing</li>
                    <li>• Proration for partial periods</li>
                    <li>• Escalation for failed payments</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Payment Reminders */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4 flex items-center">
            <Mail className="w-8 h-8 mr-3 text-orange-600" />
            Automated Payment Reminders
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Reduce late payments with automated reminder sequences that gently nudge clients 
            while maintaining professional relationships.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Reminder Timeline</h3>
              
              <div className="space-y-4">
                <div className="flex items-center p-4 bg-blue-50 rounded border-l-4 border-blue-500">
                  <Calendar className="w-6 h-6 text-blue-600 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-800">3 Days Before Due Date</h4>
                    <p className="text-blue-700 text-sm">Friendly reminder with payment link and thank you message</p>
                  </div>
                </div>
                
                <div className="flex items-center p-4 bg-yellow-50 rounded border-l-4 border-yellow-500">
                  <Calendar className="w-6 h-6 text-yellow-600 mr-3" />
                  <div>
                    <h4 className="font-semibold text-yellow-800">On Due Date</h4>
                    <p className="text-yellow-700 text-sm">Polite reminder that payment is due today with easy payment options</p>
                  </div>
                </div>
                
                <div className="flex items-center p-4 bg-orange-50 rounded border-l-4 border-orange-500">
                  <Calendar className="w-6 h-6 text-orange-600 mr-3" />
                  <div>
                    <h4 className="font-semibold text-orange-800">7 Days Overdue</h4>
                    <p className="text-orange-700 text-sm">More urgent reminder with late fees (if applicable) clearly stated</p>
                  </div>
                </div>
                
                <div className="flex items-center p-4 bg-red-50 rounded border-l-4 border-red-500">
                  <Calendar className="w-6 h-6 text-red-600 mr-3" />
                  <div>
                    <h4 className="font-semibold text-red-800">30 Days Overdue</h4>
                    <p className="text-red-700 text-sm">Final notice before collections or service suspension</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Customizing Reminder Messages</h3>
              <div className="space-y-4">
                <div className="border border-gray-200 rounded p-4">
                  <h4 className="font-semibold mb-2">Professional Tone Templates</h4>
                  <p className="text-gray-700 text-sm mb-3">
                    Choose from pre-written templates that maintain a professional yet friendly tone:
                  </p>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• Gentle reminder for valued clients</li>
                    <li>• Standard business reminder</li>
                    <li>• Urgent but polite overdue notice</li>
                    <li>• Final notice before collections</li>
                  </ul>
                </div>
                
                <div className="border border-gray-200 rounded p-4">
                  <h4 className="font-semibold mb-2">Personalization Options</h4>
                  <p className="text-gray-700 text-sm mb-3">
                    Make reminders more effective with personalization:
                  </p>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• Client name and company</li>
                    <li>• Invoice amount and number</li>
                    <li>• Days overdue calculation</li>
                    <li>• Direct payment links</li>
                    <li>• Contact information for questions</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Workflow Automation */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4 flex items-center">
            <Zap className="w-8 h-8 mr-3 text-orange-600" />
            Advanced Workflow Automation
          </h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Create sophisticated automation workflows that handle complex billing scenarios and 
            integrate with your existing business processes.
          </p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-xl font-semibold mb-4">Conditional Automation Rules</h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Payment-Based Actions</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Auto-generate next invoice when paid</li>
                    <li>• Apply early payment discounts</li>
                    <li>• Suspend services for non-payment</li>
                    <li>• Send thank you messages</li>
                    <li>• Update client status automatically</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Time-Based Triggers</h4>
                  <ul className="space-y-2 text-gray-700 text-sm">
                    <li>• Monthly report generation</li>
                    <li>• Quarterly business reviews</li>
                    <li>• Annual contract renewals</li>
                    <li>• Seasonal pricing adjustments</li>
                    <li>• Holiday billing schedule changes</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Integration Workflows</h3>
              <p className="text-gray-700 mb-4">
                Connect Template Invoice with your favorite business tools for seamless automation:
              </p>
              
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-white rounded p-4 border border-gray-200">
                  <h4 className="font-semibold mb-2">CRM Integration</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• Sync client data automatically</li>
                    <li>• Update deal stages on payment</li>
                    <li>• Create follow-up tasks</li>
                  </ul>
                </div>
                
                <div className="bg-white rounded p-4 border border-gray-200">
                  <h4 className="font-semibold mb-2">Accounting Software</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• Auto-sync with QuickBooks</li>
                    <li>• Update expense tracking</li>
                    <li>• Generate tax reports</li>
                  </ul>
                </div>
                
                <div className="bg-white rounded p-4 border border-gray-200">
                  <h4 className="font-semibold mb-2">Project Management</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• Invoice on project completion</li>
                    <li>• Time tracking integration</li>
                    <li>• Milestone-based billing</li>
                  </ul>
                </div>
                
                <div className="bg-white rounded p-4 border border-gray-200">
                  <h4 className="font-semibold mb-2">Communication Tools</h4>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• Slack payment notifications</li>
                    <li>• Email marketing updates</li>
                    <li>• Customer support tickets</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Monitoring & Analytics */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Monitoring Your Automation</h2>
          <p className="text-gray-700 leading-relaxed mb-6">
            Keep track of your automated processes to ensure they're working effectively and 
            identify opportunities for optimization.
          </p>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-semibold mb-4">Automation Analytics Dashboard</h3>
            
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">Success Rate</h4>
                <p className="text-2xl font-bold text-green-600">94.2%</p>
                <p className="text-sm text-gray-600">Invoices sent successfully</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Clock className="w-8 h-8 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">Time Saved</h4>
                <p className="text-2xl font-bold text-blue-600">12.5 hrs</p>
                <p className="text-sm text-gray-600">Per month</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Zap className="w-8 h-8 text-orange-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">Active Rules</h4>
                <p className="text-2xl font-bold text-orange-600">8</p>
                <p className="text-sm text-gray-600">Automation workflows</p>
              </div>
            </div>
          </div>
        </section>

        {/* Best Practices */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Automation Best Practices</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-green-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3 text-green-800">Do's</h3>
              <ul className="space-y-2 text-green-700">
                <li>• Test automation rules with small client groups first</li>
                <li>• Monitor automated emails for deliverability</li>
                <li>• Provide easy opt-out options for clients</li>
                <li>• Keep backup manual processes ready</li>
                <li>• Regularly review and update automation rules</li>
              </ul>
            </div>
            
            <div className="bg-red-50 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-3 text-red-800">Don'ts</h3>
              <ul className="space-y-2 text-red-700">
                <li>• Don't automate everything immediately</li>
                <li>• Avoid overly aggressive reminder sequences</li>
                <li>• Don't ignore failed automation alerts</li>
                <li>• Don't set and forget - regular review is essential</li>
                <li>• Avoid complex rules that are hard to maintain</li>
              </ul>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-orange-600 to-red-600 rounded-lg p-8 text-white text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Automate Your Billing?</h2>
          <p className="mb-6">Start saving time and never miss another invoice with powerful automation tools.</p>
          <div className="space-x-4">
            <Link href="/create" className="inline-flex items-center bg-white text-orange-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Create Recurring Invoice
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
            <Link href="/settings" className="inline-flex items-center bg-orange-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-800 transition-colors border border-orange-500">
              Setup Automation
              <Settings className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>

        {/* Related Guides */}
        <div className="mt-12">
          <h3 className="text-xl font-semibold mb-4">Related Guides</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <Link href="/guides/managing-clients" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <h4 className="font-semibold mb-2">Managing Clients</h4>
              <p className="text-sm text-gray-600">Organize client information for better automation</p>
            </Link>
            <Link href="/guides/payment-processing" className="block bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <h4 className="font-semibold mb-2">Payment Processing</h4>
              <p className="text-sm text-gray-600">Set up automatic payment collection</p>
            </Link>
          </div>
        </div>
      </div>
    </main>
  )
}