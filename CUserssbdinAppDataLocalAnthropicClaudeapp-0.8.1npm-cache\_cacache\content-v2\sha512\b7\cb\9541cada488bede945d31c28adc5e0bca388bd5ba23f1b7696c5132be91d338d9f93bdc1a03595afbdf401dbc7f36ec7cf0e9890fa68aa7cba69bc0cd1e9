{"_id": "@anthropic-ai/claude-code", "_rev": "120-e29fd441dd0a1475f779209a971727fe", "name": "@anthropic-ai/claude-code", "dist-tags": {"latest": "1.0.6", "next": "1.0.7"}, "versions": {"0.2.9": {"name": "@anthropic-ai/claude-code", "version": "0.2.9", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.9", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "66dcb65aad58b0c4a7e288db287b162ee97518e6", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.9.tgz", "fileCount": 424, "integrity": "sha512-UGSEQbgDvhlEXC8rf5ASDXRSaq6Nfd4owY7k9bDdRhX9N5q8cMN+5vfTN1ezZhBcRFMOnpEK4eRSEgXW3eDeOQ==", "signatures": [{"sig": "MEUCIQCBHUgjeXUkAbLYQeg6EHmrn6SvwzbXdXWfxlBN6koVAAIgIcj0MNWmkC9U9cjJIW2IlhRDq27KGM6Mb/SXeIOFzbE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35099447}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "c6582a012422fa8e35c5c41fdb0bd2c8a3b013ec", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\""}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/anthropics/claude-cli-internal.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.9_1740430210904_0.4569467867181731", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.14": {"name": "@anthropic-ai/claude-code", "version": "0.2.14", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.14", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "os": ["!win32"], "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "df66859eb9c25189d802478da1c6678bccaf611f", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.14.tgz", "fileCount": 424, "integrity": "sha512-p729wIUq9/K/TecpE64nzWKEZJ1qddn20eQg1nUoMdEQtwWjwWYiecwJ6lDxCmNRWr0ukC7ovu0Kgtmh+uOPYg==", "signatures": [{"sig": "MEUCIQC4sj3iGzZ61q5CDR5AIknhQWY3oHo25eb4SMMQCJZDVwIgXGJ6A+dhJiGl06+9C7YDZ4G1VctbA4r+GfS3emjJ1Js=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35104130}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "1399236bb93691ebcdfedf56ec789bc2977eb886", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\""}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/anthropics/claude-cli-internal.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "23.7.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.14_1740523998534_0.005835264527071171", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.18": {"name": "@anthropic-ai/claude-code", "version": "0.2.18", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.18", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "3ffed8740eab30acb1089b741aba959b2dd8eee9", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.18.tgz", "fileCount": 325, "integrity": "sha512-Yx3SsVQd3VKDHGswAvPpoT/JqHhJj6B+CFSshb0+rI6KG3Nn5Fv3zQdU8XWfAZwLnVZrUjSAO0JcVqoU+6f5Eg==", "signatures": [{"sig": "MEUCIQDMU9NBZnlf1S77tNIk+w+8uneJCh1RdpI/ZiJPAtVfogIge0eejC9tNlwu0Y+jWHDj+m5WJsExYel5ubl7FZViF+c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33032909}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "5fa957f499f8b3d6e84f9d4f5f2cba1c210e38a1", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/anthropics/claude-cli-internal.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.18_1740607177177_0.8010180455389544", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.19": {"name": "@anthropic-ai/claude-code", "version": "0.2.19", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.19", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "4df2fbed2731ad4c1c371284f8bfaa15fc3927c0", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.19.tgz", "fileCount": 325, "integrity": "sha512-3A9K8438+eYysRjdq2NLGkyETwKOR5CIKh/I03kMahRFnB3h/YwL/b9M1JX38qR1Chna1hNgZRurFOK9Z+pofg==", "signatures": [{"sig": "MEQCIFA2nT5DkZNVQ2N0zrXvC3VCpFZ0qvxi5SSf//Kx3/u+AiBnUomrmDkWce0SXNjmo/G/tppEjPwPhof/b/WuQOEh7A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33039102}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "c38e0c56dbce4bc060329cff1acc6c68d462277c", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/anthropics/claude-cli-internal.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.19_1740618512272_0.46104933236722667", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.25": {"name": "@anthropic-ai/claude-code", "version": "0.2.25", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.25", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "56be74cce8d94829e4da57d477db662aa8833010", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.25.tgz", "fileCount": 425, "integrity": "sha512-n0hQshFUyrkQ5gFj1yW0gXU78aZlnGgURRcUXuU19JLpxdXpVhS7blBqSs1NtWLGwthD5o2YJCO3dJE9YG0CUQ==", "signatures": [{"sig": "MEUCIQCwxFZODJzkL2j1MV/NPctdk6qtKzJZN+j+qH/ao/m3IAIgGUeLgo0hetiSsp1whUyn6MniuiyfuJvWTY0aiIaNbrI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35161000}, "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/anthropics/claude-cli-internal.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "23.7.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.25_1740697738936_0.5426880188021768", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.27": {"name": "@anthropic-ai/claude-code", "version": "0.2.27", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.27", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "dc86b8b7441749b1c60ed38d9c88f722bc8bb58e", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.27.tgz", "fileCount": 425, "integrity": "sha512-j7YqM5FmbHBdG5a1RVy95JMmZ5xef2/rbKI6zTeTohBI1XxEYyrXJcG4IsfD+Nf3vYwuzb4YaVYJGX+Oo3KZcA==", "signatures": [{"sig": "MEUCIQCPHc58LrjOXMJQM2Qq516hQPmC++0NkETNAOAwV+q1qgIgWR73R7YErxaa1FJs9rFQgDmfdmDTeqmThRYA5Wf4SPg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35165865}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "df460a81cf9984608483e1034a6cc2494971306f", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/anthropics/claude-cli-internal.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "23.7.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.27_1740706804267_0.9595676374878523", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.29": {"name": "@anthropic-ai/claude-code", "version": "0.2.29", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.29", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "75ee591ca9509eb189c7c1fcf19af3dfc6e26563", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.29.tgz", "fileCount": 425, "integrity": "sha512-4lZbbg+Qqz+mY3SLKcUYjnOd6/PIB+SQh7ICVIXFYGB1zOB4mVfiY7l2nBt67iw+Rxko2wGJAfg6gPM57S/Q/g==", "signatures": [{"sig": "MEUCIAjoZ63XAZjLFA729YEF9HaUF9jLL+G7kxUGqwWbr2d1AiEA9ReLlwtW+9zh0lDH9w2fxNwDNPwoY/KMBWimGQzTtIw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35173456}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "78afaffea882fbb7fda2a538054d4f153c5d9cd2", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/anthropics/claude-cli-internal.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.29_1740774023012_0.7247592029313852", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.30": {"name": "@anthropic-ai/claude-code", "version": "0.2.30", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.30", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "fb07cef75b6a61e35988d017697ec98974969ab7", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.30.tgz", "fileCount": 12, "integrity": "sha512-dX0CNMRpbJ3ZrIPuDzCowAdn5P0CZsa8ncBHOHt9WbqFy7y1wMbgUacJbGB41AgKp9YC7QD0C3G4nhfjW9onUA==", "signatures": [{"sig": "MEQCIDCkzKopFxHx7in3iP75o1xygWLlBFLj95oe0bTuG/6WAiBGuJ/bKlMtv8UW8595drVcMQaVNcogk1WLyae3IRlH+Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31755894}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "ff6434b402b4adb0cc0387944d89c83bf28cde66", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.30_1741114025509_0.052986483673783", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.31": {"name": "@anthropic-ai/claude-code", "version": "0.2.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.31", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "f2c12711190efe832f6864da0deec058abad1999", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.31.tgz", "fileCount": 12, "integrity": "sha512-GHoO6Mzowf3fXdhDX8lXB0Lb38OwsPizeh2kkCCDj9/lJKUGUNtSMQBXO4nZkNzGVVLXh7/aReLcVNxmD1/QMA==", "signatures": [{"sig": "MEUCIHQcPMlu51xcn1uLxMEkdYcloTlveXTbDEOLfnw39miUAiEAmuP854tSrRI8riONh1yGQUZP273MMEonR02g0XPxWPY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31756151}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "2ea95d3ecf4b77ca8dfe36d95bced823a81472a0", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.31_1741196900527_0.6929535482839542", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.32": {"name": "@anthropic-ai/claude-code", "version": "0.2.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.32", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.mjs"}, "dist": {"shasum": "53c4ebd82d5222118b7b287f274ddca0e8f2834d", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.32.tgz", "fileCount": 12, "integrity": "sha512-BhVAlBGkgMbkiWPein6fADLgfZKakR9FQNYGzReSebvBxxQRy9jypYuuZgd+4p5RIYsOtyevlUltAm0KHDgs7A==", "signatures": [{"sig": "MEYCIQC58upDQu2oEO6rhEKK/23aBRLFRj9yq2P1YUSxcTLl0AIhAPw6UDusfhbkkDozzIujnanoN+vxV0byHCXUY2JEAmxU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31981128}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "6a1860b990462fc7d744d7d155f056d60dbfa88a", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.32_1741210101883_0.3860210542902278", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.33": {"name": "@anthropic-ai/claude-code", "version": "0.2.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.33", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "f85396eab69f9c94e536a4d63683dfca9ad1bd32", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.33.tgz", "fileCount": 13, "integrity": "sha512-aW0UKF0wfbArvj9mzbt51Zj+ldqNnSeIN9kFNIzOB79VuDjWlz26iVNjDA1FHM+pARXzypohanMMrbLg4uveSg==", "signatures": [{"sig": "MEYCIQDaTLnOeAWk0Ao3b85Fx4e6eZ38wnZzXHcqCByu2X92KAIhAPPrGSsZhGEVS5kOUA8WTzJLoURGg0iD6U3/Yy+IXx8G", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32245406}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "9115987c23c365a3226e7e63d30898611a2ef933", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.33_1741388715682_0.9710562460923444", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.34": {"name": "@anthropic-ai/claude-code", "version": "0.2.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.34", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "0be9d1983c46f49e4ee6cc427936a7789a18f16e", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.34.tgz", "fileCount": 13, "integrity": "sha512-VGE45sXuJZW62gTF7oEN6ToMZB82yZOUNrCQozZC4/EJq13UHG5fiApxPR31QPUIb7SViTtiL7Q79ZyMbHh9QQ==", "signatures": [{"sig": "MEUCIDJZW8AfcnMcQm4aQ1Sg4GnzPTJaGM+xxuG4JAKEQxX1AiEA/72cCHd2kdOAgUpvDgVDRBzNmfqhkTaTuOzViMDCLYM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32245477}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "8d023c14a8e10028411fe747f76d7d4b76d93098", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.34_1741394923918_0.6568134126700891", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.35": {"name": "@anthropic-ai/claude-code", "version": "0.2.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.35", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "3309167f18e2e8ca40c42846ca213c65c1ad94f3", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.35.tgz", "fileCount": 13, "integrity": "sha512-OTCLa2kvtbYJ2tDbKNxZ7N2kUMXGXTIzr5DW18zYSMMu5RpOLyL+A/DfKXNgcvri76ttttj7lcQRLmQ0pCqZDA==", "signatures": [{"sig": "MEYCIQCxzvdNyAGqfN2xkndySm6uNfK1xrnRE7B0/+yIbbWU+wIhALyVXw5YAA+PqzLMNPzlQBwAM4pVtunNhJ891pGkvTPf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32245697}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "27b85cae4448556c05d76f4b440d6f3a8203fcf6", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.35_1741397013878_0.6135542620554097", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.36": {"name": "@anthropic-ai/claude-code", "version": "0.2.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.36", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "f6cd7b22ee29ae3fb4d70c844528048fd1410f56", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.36.tgz", "fileCount": 13, "integrity": "sha512-RDUigrVYkV/Ws4q05tt5m9zDFVHuI1iciIzE3jwK+0ULF/H3lXh3zzl92ZyFmY2yeYWGUk/tHJPeySOI6FNAdA==", "signatures": [{"sig": "MEYCIQCN+TgCnI0q6bLYGGai4YRZq5klt7j4vWJMVSPU+DtkWQIhAK7j862mZx6RsNnshFdlBbPapdmTXzk3hcCkGF5ljS1U", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32260482}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "b84e4d7a594d45ca3fa642ffe5ad995ca351b7e6", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.36_1741649327526_0.5115649587166398", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.37": {"name": "@anthropic-ai/claude-code", "version": "0.2.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.37", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "dd88782669ab82727b2ba61e36aec02ad82164a9", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.37.tgz", "fileCount": 13, "integrity": "sha512-3ZIAV8ttVj2jlAMGPNmz4D+Au2jkSni0zfQX89FqxPBkBa9dm0i5A3SYQFA1FO+uvWxCpBuqdv6gZEy1woYjrQ==", "signatures": [{"sig": "MEUCIGzXKoXgizxcqhGgtFsZtbWFGc/Ze/9u6wgRHTpG1RrrAiEA7Q5n3tJsSqQeVFus0jzq6CNtaZIEk5n2QjE/QKfinxM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32260188}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "6a2ba5d4047be6c2019ee955285aa9a3864ae2b0", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.37_1741726164968_0.6950854719714206", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.38": {"name": "@anthropic-ai/claude-code", "version": "0.2.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.38", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "56ba1004f2f3bd106f5be89ba74df4fac16fab84", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.38.tgz", "fileCount": 13, "integrity": "sha512-lhRYLx46lsfrAHsQuPbHLfERs+hzPVz0KNzyC0cwXXgdn4wt7YXExV0X5DrbIMl1Qqn1ehCgYz+v0Uagr1sfMA==", "signatures": [{"sig": "MEYCIQDujR0ka2HgcJiYoHmVEhgd/yAcpYQ3moaLFXjPGKUQZgIhAIB9X/D3CwwJFkYOU/8daJ/ukIaJ+2FTDo4hX+aHFxpS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32377755}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "e684ca20053daa2e4e140c0033bb4c597fabebe8", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.38_1741800930154_0.44620726981400294", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.39": {"name": "@anthropic-ai/claude-code", "version": "0.2.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.39", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "2e451bba32b6f87473a0f832026c5ffdd717f1cb", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.39.tgz", "fileCount": 13, "integrity": "sha512-Gil5H8Ejv6ZMGL/HVsIF85bUX6YDdqEz7kmCTletx9RHGyttgrwMtaN5SI+r3gSu1T3iKyiAGSzxbCWv/id+Hg==", "signatures": [{"sig": "MEUCIQD035wzxBc1HsYvjAwsAlaTSDmA1sBGOkoH+hzPLOqiZAIgfNK3bktqrt+9wFpUPzyPPsXGuOaQr7PkDAYnPZ92ilM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32379960}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "c74c4712bcc4db21a1d014589c1091851ca3a510", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.39_1741828867633_0.6600288905843044", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.40": {"name": "@anthropic-ai/claude-code", "version": "0.2.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.40", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "3fb17ea3e550b6fa7b65d30bdd46653a4fe454c4", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.40.tgz", "fileCount": 13, "integrity": "sha512-StMSirDZ6nJSBiIx6nETJ0V7nBp245tsPbfsZ/X6Jf6FygH5/dgQctjSUuJXcDMuQqfM2gwjRiThbwflJwp/+w==", "signatures": [{"sig": "MEUCIQCyGefA3yrDr3cwh4dWC46Bix4JvpjZ1Dbz5rdUjYN9BgIgRQnCZy55jfyBACOWezlxMjlTOvjOpU1+10UjQAItIs0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32382669}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "f58c80dce1d6463075a44841dbe1a5d0239f9014", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.40_1741899947400_0.6189100508159535", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.41": {"name": "@anthropic-ai/claude-code", "version": "0.2.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.41", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "00476653b6638ebd4abfcd7f51ab76356e471f80", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.41.tgz", "fileCount": 13, "integrity": "sha512-tbzmDPsD+WQ/KnA92kKpxb3/PEYk1FDbpIMvbzXFuXDONXW66o4seTl4JcpBVtb9zk5wv6srTlB7M9Nn7Tel1A==", "signatures": [{"sig": "MEUCIQCDC7v8MRlsEdTS8JanszRTf8zlECQjXsPSfwocD5E8fwIgKMLoUwDxNTN+hKWK2Fplb/vH26fs2qSVlhig5omyU4o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32384631}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "0bda5fdde4bf8b8581531d625a7a48bbacc06d02", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.41_1741916081894_0.21641176849745203", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.42": {"name": "@anthropic-ai/claude-code", "version": "0.2.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.42", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "b24561a6ff8f3f988f6762a4b0f260cdfe3811ad", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.42.tgz", "fileCount": 13, "integrity": "sha512-8UBLTUhlh9cDgt1QWZ+FEmlkga9ZFLFffj7brcmIrxao0pLFlZjFcGWLjmJ21GlITdmLpk9gqIrONIXoSqjU+w==", "signatures": [{"sig": "MEYCIQCcwzE73oZL4QnSdLresLQj30yK4oBz3e+GlFBCA5npewIhAIYGVRZ5c2+ooKpntVQj3Vn3So7T0tFMUwRNLIqQPgVr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32390966}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "23fd33e9cc3c0606add986b88a433d5efe222a99", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.42_1741969790559_0.30154955092230873", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.43": {"name": "@anthropic-ai/claude-code", "version": "0.2.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.43", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "fee7a457426f7eb505af578e118049bd1d5824f1", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.43.tgz", "fileCount": 13, "integrity": "sha512-0PYNn1j5hIWSF1KdHXU5BPmtlLLHExSaoyMGmb48VaE58LHUvywLpF5u73OlbpRPnRT26/mJp68NUcfn6mLYCw==", "signatures": [{"sig": "MEQCIEsSbXdbZf8nq0oVtdv6gpUD3skKjV87Je0s00ssFSLxAiAgnj/Q68sFjVNsaZO0QtTUD/mGT6vBCtVDsG3ylZFizg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32390137}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "5d59b6e3c0e0ee86c6c3dd4d0e9ccf5f6fef1e3a", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.43_1741992179746_0.29126856837269677", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.44": {"name": "@anthropic-ai/claude-code", "version": "0.2.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.44", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "0c7b7936c90eabc7e339e2546d325695a59f2f82", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.44.tgz", "fileCount": 13, "integrity": "sha512-hs0+xTsyS6DiBVUFlA/xNApoUcsooTbFFutLNM8zOA+klrcUMj1AAYlXSI/KiHt1t2AjYOXS4/qh+IxyQSJTIw==", "signatures": [{"sig": "MEUCIQD9hAwGYkaYqUgk2dhDG9cILz3rMHXDs8CS4Z+QXz+sGQIgExUuMmTw3omh1CSwkVwgkS1HFSX0WFEMhAUTzGRmRVI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32396636}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "ade213bf44b5c8c1014ce796fe55efd091c95edf", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.44_1742000664175_0.48250094493781237", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.45": {"name": "@anthropic-ai/claude-code", "version": "0.2.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.45", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "1e5886be5e256f3023be8b45e82bafc58b3af812", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.45.tgz", "fileCount": 13, "integrity": "sha512-r8uSA59wuNUHjlU+snwkZBHGsuv7z5+sxDFhLxI+1wz6PU9CU2/V37k26c7YpN9OFfeRmt9hk7gb3KaIlTH2ZA==", "signatures": [{"sig": "MEUCICnPxLCznzT4aaPekqraVao+sZzVhZO+wwDEIh1qM8g/AiEAgVjM3HK9EFFa0CDeSPdQvA+OibR3BoVja1NIHmKac0U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32397061}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "30fdc071a3be011f374a2aa2a59953d51f469ea1", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.45_1742008982813_0.010614511061515008", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.46": {"name": "@anthropic-ai/claude-code", "version": "0.2.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.46", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "b85cfaa5b90fc3792e057ab9ef343631315cb79b", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.46.tgz", "fileCount": 13, "integrity": "sha512-6SAadsCBqYHjDAFoe8UCK012P91mYgdZet8cmqEjKoiwALxwknA1ngvF07Sklx0tFmpeGc1nFMF9VNH6Cnw5zQ==", "signatures": [{"sig": "MEUCIBNKPytcBMA6yfy1NT2WBr/0/PASFBdctuVh8ue2l+lnAiEAiAY/KpIuwdukyN0vpSAgF7c37GSag92O6QSAD/PmGk8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32407988}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "d17b6674656c1806a3206365cadb449091dd150a", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.46_1742249592606_0.5800376053234966", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.47": {"name": "@anthropic-ai/claude-code", "version": "0.2.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.47", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "09de2139fb1a605f2c8e3defce82b97e7b9eea52", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.47.tgz", "fileCount": 13, "integrity": "sha512-3035KwTBfdhyDqyG5B+i/Sr11OkVItQWS8bUFdjhf577ywzKPbnZ4UKPbfP/AUFaxVQmCwx2HnnXaEtq9MiFDg==", "signatures": [{"sig": "MEQCIBJDRzAlrrpAT5GJQVJ6/EmwzcYmt+uvr5x2xkNitTBxAiBoBcWWrguAEoBoW+Z9iOaql5ePpiocYTQUVqJhc8qw2Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32412057}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "a711577bfb3100fd7608691395ebc59bbe10e1e5", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.47_1742262157512_0.016568177138406304", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.48": {"name": "@anthropic-ai/claude-code", "version": "0.2.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.48", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "fcd7ecfc522f6f7386e7e92c044882099d42724c", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.48.tgz", "fileCount": 13, "integrity": "sha512-Av9EC7oBGSgNzJ8IFyYQnzCURtzmUZb1fRL+4vKJunCF33nCkTW82DEOKsfcj+Z268/Pf8WhXuI/vLbwpMdymw==", "signatures": [{"sig": "MEUCIA1/OrYYmE6ibFzbIpYSMQh/XqPujfguDip22Uk9QoRuAiEA6tt7Jtp6mKf3SDh1XvI0SXY890vHUqbqdCKh4Regals=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32412209}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "82adef1b0705d462d8311120dd5c9de7f6987eef", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.48_1742262789280_0.7674440911436708", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.49": {"name": "@anthropic-ai/claude-code", "version": "0.2.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.49", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "5f9a7d1130638cf8bd9d3eb5b25bfd4b8354da3d", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.49.tgz", "fileCount": 13, "integrity": "sha512-65X8dOKwZxohHOk3T3m3udqDC7bMCQJIYcCOA704IH3dM6TvvgJANAYR3rAaIL3/eSOijOFjMUfDWifLNv/OCQ==", "signatures": [{"sig": "MEUCIAornPuHut2h8NJN7FaK52xMe21S91t74e/wJ3zN4DmdAiEAmYanPDy6rhFroEfWxgBJuuoQxgRQaYhSW+1rt3l31ag=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32412105}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "971bfcb7076a78eb049da73e0b399834f097e8e1", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.49_1742325501241_0.19695459068316046", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.50": {"name": "@anthropic-ai/claude-code", "version": "0.2.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.50", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "1cd39246f14e5d67eead21aaabe2ea5a336f5929", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.50.tgz", "fileCount": 13, "integrity": "sha512-YUQFmrRQ/SSngRq9ZMXY49EsPiZJzd33uXAQLWZjVKUrJ9eKXXZOKfcofXnxXE2fPy1qpfZbroGqcPOHnB9DzQ==", "signatures": [{"sig": "MEUCIQClVc0QZu0tdVrmcHZOYau6Z5qbuwvmkEMS2EFAAjQWvwIgLoN4uNHRkIfPTXC7lKd0RUDAvdv1nkUB7GjmBM+P0LA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32415690}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "9c4f2b649edd7832f36375cad44dd45b26a24b51", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.50_1742418406389_0.8833111913500431", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.51": {"name": "@anthropic-ai/claude-code", "version": "0.2.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.51", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "95ee7657754999fdaadc03eaff86aea7918bbf92", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.51.tgz", "fileCount": 13, "integrity": "sha512-LRx8clB3/+NiNDiZSaIK7+OvqnFsOhjIzQxSLPIuKFd5WMPmzmWZtu4XLwKMu6DRVK4VceIQElkeu5yNZ/a0Hg==", "signatures": [{"sig": "MEUCIDVfVyh58j94qFyiRsl6SPLYIZ6J8oGzE7J0RX7akkxKAiEA9Zdw4oXEHmjVNqlFNcrIpNGl1nnQ/Q9utyjn6Mq7fk8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32426927}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "439c50889a21a37f4905918e0a338d5e014f342e", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.51_1742490904091_0.3775367269484835", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.52": {"name": "@anthropic-ai/claude-code", "version": "0.2.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.52", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "f514d43625b7e5a502949c162c42b3163901a666", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.52.tgz", "fileCount": 13, "integrity": "sha512-r+t2xAEoimXNJCytZ2uPGx3sP7dG6sA7kGoV16QtnAYjghB/k8YHB4Fq5sIumekYfHUWEp75CcwZ73cmIlrjYg==", "signatures": [{"sig": "MEYCIQC2pXZfZSQgEcq2zo8aLSb51vlsmyFZ8ZWYJ0T4hWEYEwIhAMdPb9ctOvq0t0e+QO+XDAuugUsjNW7NTKk5BXtyWodo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32426721}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "335deb5adc4b1dba8da3c886e6219e8362112a40", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.52_1742491266082_0.3287632373747609", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.53": {"name": "@anthropic-ai/claude-code", "version": "0.2.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.53", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "b580b619d1c46689f76b7fe880cc70d547207ee6", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.53.tgz", "fileCount": 13, "integrity": "sha512-DKXGjSsu2+rc1GaAdOjRqD7fMLvyQgwi/sqf6lLHWQAarwYxR/ahbSheu7h1Ub0wm0htnuIqgNnmNZUM43w/3Q==", "signatures": [{"sig": "MEUCIQCwTsTJRqOcQo/cQhVZauTPcSx9eVTRzdrwl+3JLDZLDQIgC1z40i7YTPfDOZUaU5M/bbQD1XvCcyx2cIdf3UiW7tI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32512834}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "bd1cc0c667ca842527d1e9893d695e5f6db20b8a", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.53_1742580242109_0.27470029680800767", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.54": {"name": "@anthropic-ai/claude-code", "version": "0.2.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.54", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "ce6b0e1b03510224ca5f4fe23218c47acd02c9c8", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.54.tgz", "fileCount": 13, "integrity": "sha512-kl/g4oBg+FBQHRQv3INaiDuNWoYIUm4BbIk4qd5YbesFQVMPGtblh0Avst61FUo9ZCm64iBPWupPe5aaiJ6Y9Q==", "signatures": [{"sig": "MEYCIQCrNE+wbcVyBEa9JaahQOYacIgkq6ZekiWKNImL1bmD9gIhAOOuo5O+GdblI1D7oT8HxDwOuSYC4zNk7t4kQx9rCquw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32617343}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "81a03368e5e8d4bddead44f390f14b33c5093d71", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.54_1742935487643_0.6445271571704305", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.55": {"name": "@anthropic-ai/claude-code", "version": "0.2.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.55", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "3040cfc747bdeb157fe9e3c9834409f5b7827b40", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.55.tgz", "fileCount": 13, "integrity": "sha512-FYKNHeV7uwovW4jvOA3VVTqx3nOgNPVCHmD9RXv75w2/WJpINIyjyUEaOa79bnKw5vBokZfD3GXQ9Ofe5JOuoQ==", "signatures": [{"sig": "MEUCIQCEGGHoDIikIGR/Ih5ZIfKrvSP8w6lmIxaEY0msiWmBjgIgDa12NMhcQHHK9SDiW+pfxY7WaSFHrJDIQZ1ODEYnFZA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32618765}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "5861834efd4e5c30835f47728ec35c8995ea1408", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.55_1743025253151_0.59502043567796", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.56": {"name": "@anthropic-ai/claude-code", "version": "0.2.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.56", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "ddfedcd11df07492e05cb0b1e9dacce1b2e4cbbf", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.56.tgz", "fileCount": 13, "integrity": "sha512-+ouNnASXYEgR2RNB5LDTVsR8bzUBNU3n8ZN16tIzlm0jIO/kWL243zBubdrjwmQcqroYqyu1CtN2Aav/vaouUA==", "signatures": [{"sig": "MEQCIEWxUTanCmbNf7AaK/f07+J/Zlnjtml62eI6d0n4OfsvAiBMZk0ainvqcUipt9QozRJwBbbyHoIzDthlvdFN3k5ZeA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32629954}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "a17dfd2ab4939309007d6c17a3b6e9e1ad04dfc5", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.56_1743114336770_0.28481400351318986", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.57": {"name": "@anthropic-ai/claude-code", "version": "0.2.57", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.57", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "095a6a96d0a92ba6c4e740299d6a0272dfb76fd1", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.57.tgz", "fileCount": 13, "integrity": "sha512-/NS22ojBphLj0zhEWV26WIcLS1JcMT809tmkiwHrb1D9fGJccbbfkLyMI7eg7TAmgnXUINWxlwmMIExdOsRXfQ==", "signatures": [{"sig": "MEUCIBhRoDiQcmcKbeXsECbjWE5tVrNAVbZXTQa9BvoTWlKDAiEAwXMKa1NJJekpBtGytMDvlIhCBrJWFrbxGOY+UTBwKmw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32649208}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "38f194000a8eb565aa6293dcf06666750efcc101", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.57_1743464983534_0.6730559933380937", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.59": {"name": "@anthropic-ai/claude-code", "version": "0.2.59", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.59", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "9e1811d9fb5c45a60c541ac667b94507fcb515a7", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.59.tgz", "fileCount": 13, "integrity": "sha512-hcUHEiPUmkgU00J4/1dlLgWvf5ZkWOjMpUrXhMq2o143LOElKuTGxPGt2RtmFHKk6DesFZcV/gabZYkcTqraBw==", "signatures": [{"sig": "MEQCICeomHTKtrCptoIYA1U9GtpYnptaSOCCmXIKI4wo07mLAiAEfp5xHvaaVEYmDyhty4N0Ix3tC5AQojiIeO2Wdan4VA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32656814}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "5d1e781b4761548ca63cb79394dcbd44b538669b", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.59_1743553499194_0.5814819396390729", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.60": {"name": "@anthropic-ai/claude-code", "version": "0.2.60", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.60", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "f76a93878c7e20ac757c881fd95088d9275f969d", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.60.tgz", "fileCount": 13, "integrity": "sha512-axaARQIqPWdDfcS+ZyHNomIRmfkPr6AL51Tb+BtvtWrs0mP1kHPVoLP/o290U7X8XaYlP5DIpWkxjag4ES5+Dg==", "signatures": [{"sig": "MEUCICRI13UdyKkcolKsyO7lOCQeqwm5BTcFUU1xRaPTcaFqAiEAhv65K4c5ImeLGjmB39oWgtAWkRp7i5wsxpt6JH9glWI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32660475}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "5bea6885b7dd5d400842b497e332d2e8f4f2323b", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.60_1743615013510_0.7811449057933926", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.61": {"name": "@anthropic-ai/claude-code", "version": "0.2.61", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.61", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "8f6b88959ed1f7062459b70af1ed0ade3f74e61f", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.61.tgz", "fileCount": 13, "integrity": "sha512-GBSY2hNZg1MdcZpRzGinW3ouwM0vOF16ZaHLC6G9c7lJPtQzHdd228M2n4sUPPvnAUBwe8CQceo4fq01mMgcYw==", "signatures": [{"sig": "MEQCIBKDWd8Qpmf5fiH2kdFHEOUeom6ndbFPQqSavfAVlEwvAiB6xdl8P3L5P69L9XQD0+HvIYJuG6PHeeAqNcDWiVHwEQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32666995}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "17a2f2c2b0c986eef9f116b60e946b1eef293955", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.61_1743642259084_0.2254775674899503", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.62": {"name": "@anthropic-ai/claude-code", "version": "0.2.62", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.62", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "562a787d5ea1deb0a2851dcddf82d5b01ec1e57d", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.62.tgz", "fileCount": 13, "integrity": "sha512-Mod9/kbqKy344lm5YmDJLn8dR3HYlA2zGCQy4exU7hmECNqg3KlTAz8u4O4YdiRMxXeUJ3Izi9YSJUT7oZOKdg==", "signatures": [{"sig": "MEYCIQDjeItgxPSD+uikWcwCozw/CgRvsx5YGBa0Gl+IssAuHwIhAN16vD3W3WdYpBUY7Y+rX0A5HvXjJ6EdrqSp3/0Vv7VQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32568484}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "6c05347dec8c20baf49d6e72a6b8819153c9609d", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.62_1743735417716_0.8156312348082202", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.64": {"name": "@anthropic-ai/claude-code", "version": "0.2.64", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.64", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "0d74a95fa735c8123073c7adea7fd66ce85402d9", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.64.tgz", "fileCount": 13, "integrity": "sha512-qwXePRKDi1P/r1ya96tGXN4agHX4OnAtHj5kkL1W17CKJP+JZa64izCQL2z/iEXvA4KV59gWiYogBHzFgBqH3Q==", "signatures": [{"sig": "MEUCIQCzTYQMLtWMzvVidc8y4AHk41AOOi/QIFMHMBeNzbB1bAIge19o0fpdlJyFQFG/vvgr6vRxaRkRTyQE8k2e5akJvxg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32573661}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "050482b70e840ded5808ba6067a1cd1366eb6a37", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.64_1743800334237_0.3961025668311746", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.65": {"name": "@anthropic-ai/claude-code", "version": "0.2.65", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.65", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "f7de0d6124a56e03abcb6c837ab206e251cad209", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.65.tgz", "fileCount": 13, "integrity": "sha512-LCxFb/WeHoHfVhQfEQGbGlFURYCm5Brcff4GHD+lVX2N3GtexLTcf0iXElAYz3S2vlWX9km8nGVfB/Yd/ieVUw==", "signatures": [{"sig": "MEUCIF9Jay6gAnKgX6GY9JzAGaGK+DOqbLKxosuKaJkNuhQdAiEA08bNIIni5eJEFJaZZjmYoRwG8TjJKs+teEp54Bbxa1A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32588242}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "cd10fd917c4768a521732614665627575bfc8c5c", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.65_1744064955132_0.5503865973932316", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.66": {"name": "@anthropic-ai/claude-code", "version": "0.2.66", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.66", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "76387f08a565ec678dd3d2c550aa47506042f2bb", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.66.tgz", "fileCount": 13, "integrity": "sha512-5SipNatUIQTgDLyFmrke1yv3DKaKoLcNFW/8bRagab2mtrDQWC4F9g6hZOjeXx9gEPE0ZooZDpO/Cxd6q+jjPw==", "signatures": [{"sig": "MEUCIF/bUXxzHmTg/Vcc91YHoHeN4k/9whJ7Je6SNT88IFHCAiEAlczll2cA7qd/LxJqv96J/q4rS1KuCnxoYvgpnUMvrr0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32599596}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "ebdd48769f45332db43912f7ac8665c95fc4b566", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.66_1744157085989_0.10753911141764694", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.67": {"name": "@anthropic-ai/claude-code", "version": "0.2.67", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.67", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "7c5dbd7f0527740586fa2cb7e45809e9040028ac", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.67.tgz", "fileCount": 13, "integrity": "sha512-z+9luCzhbmTzi/s550fMZCPbDOdc7sre4v0Ig72Svu+Ny+bNQ4TfGua5BP6E97hIummHq0DnYblkev8+RnCmlg==", "signatures": [{"sig": "MEUCIAti0noePHNdY/KTMR79jWuWwWnMmoSKggoPPA9cp8dLAiEAtD/8fwcYO7bPgH4gSshDHbsvTaZTbSbH/JjZliYqokc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32601866}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "0ce8b5d93de6e04ba84280b46e983e05f2263dbd", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.67_1744237067393_0.03214581353820578", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.68": {"name": "@anthropic-ai/claude-code", "version": "0.2.68", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.68", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "44b806da5229df11ba16e3a1793c45eb1d90da0d", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.68.tgz", "fileCount": 13, "integrity": "sha512-3CPQbSVqBhk1ZvI/JWFGsZTTZXKihr3+yGlCt8j0WSawOw0iEIwPVr2SoFgkDvd61vq+a6M53qveUnHqwHjP+w==", "signatures": [{"sig": "MEUCIAn1h0a7oRqChQBD7tamMxrSlCpUJOxOIXcKAsF6KYwhAiEAySjy6yj3LR9RFrE4pH6EuV80cjpHXZPYDA9VgfulqTo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32602372}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "a850df8728279d2ab64d68414a3510114cd8cab1", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.68_1744315494057_0.34854054053965866", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.69": {"name": "@anthropic-ai/claude-code", "version": "0.2.69", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.69", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "109bc5f0cf7e991b68442119a6ec0ceed1c5634e", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.69.tgz", "fileCount": 13, "integrity": "sha512-41rEx924nNM8k8pJqj8aeLOYtQYFyw94aKmKQdBUoewSPIpySiJVC7VUDxm7Q+k+oJhnXB6YQY/8+qxofyuJNA==", "signatures": [{"sig": "MEUCIDgWzvL5q2d/mAHSYMmpEwu1x385i5ueLG0UZkP1ViZwAiEA8v9H5GkxSevG7Yd0SluPn18LZpiK576Bv005aANZIrk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32603635}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "92679a14de100234f9614d7f774a37b7312622e2", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.69_1744410628073_0.28444906934062386", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.70": {"name": "@anthropic-ai/claude-code", "version": "0.2.70", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.70", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "e44519a9cc77c3363c2d3f35eb5666082aa2e844", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.70.tgz", "fileCount": 13, "integrity": "sha512-OwwTPIjnz2O36yaby/7kYu/94bG2AZaRvZ8BfWdDHMbGhRO+MxpEVjwy9E5mIWKv0y5IgZljqg+mfxAqghoMpQ==", "signatures": [{"sig": "MEUCIQDGwvDiQu5CMTDtLbypCFeMqAYKpP75V1lH9SXvV7mSqQIgMjHTPqK9yZMRkSxrKEBBw3z0Bj9JKwWQFT5C8xHvN0c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32604909}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "95c332182721baab880d93c2c878bf00e92ea6b8", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.70_1744739155151_0.20120111761067716", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.72": {"name": "@anthropic-ai/claude-code", "version": "0.2.72", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.72", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "c22bc6736612d9255952c5adbb64e33d9ae297fa", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.72.tgz", "fileCount": 13, "integrity": "sha512-46h1S1/ZA7ktx4kwIY2VWZmSL3wMqPrWGYDhcS8kieelxOx4u8dLxLs1einVCmNB5Jgsr3Tz9YLF69NRnEUvlg==", "signatures": [{"sig": "MEUCIHVhKaHNUjbcfHQ+f0adJf2dlUYO67KTPF+db+/Zt3bBAiEA+wg2F+Qo3ygv7q3cxp8Nozh9DPLJZDaoAASeVF8mkxs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32602849}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "e8c4bcbff9a0ae19c384a82fa4451226ce991a7a", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.72_1744899675919_0.07119319018635073", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.73": {"name": "@anthropic-ai/claude-code", "version": "0.2.73", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.73", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "06d51461fda357142f1d91433c5d65dc1508d34f", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.73.tgz", "fileCount": 13, "integrity": "sha512-VlEfyjuOQbY93gcD7SzQ2aIJBnmHceMWPntX75suPzOVwi7wf97wnfVwYe5LTObBkAbjMDD4d56GbYz54VNKKQ==", "signatures": [{"sig": "MEUCIHia3nuaBJNMQ/bkYcEQAC1gMf4LlgJj5FW30J70RAfjAiEAqzsz5LBYCAXDQIGiGiBsd0eC1CPVlBCfdGZOXWLPhxY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32604804}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "c0a275898eeca5f0bba2120918c86172e9f80570", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.73_1744994083387_0.5531566556987253", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.74": {"name": "@anthropic-ai/claude-code", "version": "0.2.74", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.74", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "7f23c6887ed86854a412c6787ca93349668d91dc", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.74.tgz", "fileCount": 13, "integrity": "sha512-/ACVmaEyWTPuFeRAujnCYcDI9cBqY8J4jUgZYQU+71Px8KMmWL6Sk0LrdfQsZLtRUHLRtjUF5qvTM6Nuzxl5dQ==", "signatures": [{"sig": "MEUCIAMz9NDOl6E6GC6FxrhL/Da8IeAmH5bsPVX/TtLEDGC9AiEAgJ5qEtoRPPVCK/oCoFr1f172hQwSdTzGUHy0yfEkf9o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32605624}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "26157af5cec08417a5f0e90b9e8b99ea058a9cdf", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.74_1745015606337_0.7167692013478033", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.76": {"name": "@anthropic-ai/claude-code", "version": "0.2.76", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.76", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "6d6d0114c5cf1da2b48eddd19a16ee967d880c54", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.76.tgz", "fileCount": 13, "integrity": "sha512-O/Wl5skIOp+BuaxGoQJiMEu3Ovo4/LxivLwDFwcH/I1LSQxt4fn5D4j1hMKBOFNDLKsVY3vZdMHSHYgw2gzQZQ==", "signatures": [{"sig": "MEUCICg/XcfpH9CAL9WwDwDaTb8N51zR081Afvi05d4nlrLeAiEAzBNa6y74gCZlIGifkHCMY9eebq37qFrbT6+rf3UBxpY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33463365}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "dcb9104e3625204e92fd61cbc78a4ab4ac9be881", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.76_1745272765255_0.19165552054625978", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.77": {"name": "@anthropic-ai/claude-code", "version": "0.2.77", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.77", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "6032c5393dcdd400ddbeeaa5aa073e2c9992370e", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.77.tgz", "fileCount": 13, "integrity": "sha512-txcUbMhQdTS6keQEZJTk/+evjHgDZJxZivjgTtlpw9ny2/lCphyhPGl5IelW9Un/tjVFE8t0lDKWajJGtjqFYw==", "signatures": [{"sig": "MEUCIQCZy7t7lVKwuo5w7Nty7oV5P8twTr6DbfrQB5Qi9X3KMQIgYTNu6ZnqfyCgxePZ/SMgik6o+8GFHF+dNCLCi2jAW/s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33465090}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "9e2419a0c7a9c90135aa8f73bddc9ee54ae7b627", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.77_1745345247458_0.8688198328303751", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.78": {"name": "@anthropic-ai/claude-code", "version": "0.2.78", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.78", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "9eef1b14d007e572c03f8cb370aa8109a70ccea0", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.78.tgz", "fileCount": 13, "integrity": "sha512-kXjjTYKTp6rG7Vis8ryC4tRNij+jO75zG7y6ymotTFMTuZljGcG07daO8KuGHBz8ZGAxnzWbEQqTB7gkZ2/0HA==", "signatures": [{"sig": "MEYCIQCNmN5mNJoC3KfcArGZGnEdT0cda9/g9dOJGNRc9lWKmQIhAPWv1Io4VYIN/InmZ+NeFAEdvbJe5UKZ6LRhmt6/NEJl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33467330}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "1df6c274a8acef08ff9d19e9031cefe0cc105bd6", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.78_1745351334569_0.06146140091822794", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.79": {"name": "@anthropic-ai/claude-code", "version": "0.2.79", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.79", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "9ea27ebe40cfa18d1815b2df94f03d57d8bfa964", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.79.tgz", "fileCount": 13, "integrity": "sha512-9qB8yvaSDeKhVv6ITsEjTCs35OUD7hXbrS7KpAVFtc8iDLwJx6HNiwjy12amvFXOSlmrnIku7U4kOo2aql3lrA==", "signatures": [{"sig": "MEUCIBEY3/1hiPOqNa5Ep3nnj8sGnvgTkh6iB301D3gyouSgAiEAqWuhxFNMXjDZ0wPs8sC0zeFjDAApv9/xeNNSfinDYSE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33470583}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "4937b1160de94b65e32507d6b31ee9a6402beb0c", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.79_1745429058130_0.3349708110418934", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.80": {"name": "@anthropic-ai/claude-code", "version": "0.2.80", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.80", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "f6d56df697e1d94aaa00ed64068b3d6a8a83d4b7", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.80.tgz", "fileCount": 13, "integrity": "sha512-rkzmAcmr840DS7ErLLQH7lhWahT+3EhYXZ0ln2UdWX6YFvN7Z4yxRC4JQjrKXO0oTN3Au4QZ3AqERlB3XihvSA==", "signatures": [{"sig": "MEUCICJPLrVRp+yhXOj0JK7jch9pZrBvynflWFKPkShAC0qbAiEA1eHXJ4A5eJYx3cnelEnRfWpWTqnGfw9MuQHytvvjvHk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33477394}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "e8a924b5128a852e23b383c017eafcc2732ee22e", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.80_1745510871952_0.43252215735501665", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.81": {"name": "@anthropic-ai/claude-code", "version": "0.2.81", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.81", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "c8d6eb749f98e320b81222a00af7bc171cabd8c9", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.81.tgz", "fileCount": 13, "integrity": "sha512-7SoVXeIc+J6E3nMdyD0LOyApHgP/RCwsDDQ7va4s6146qgbCIM4Zh+L/hAJcKemBm+X6UX31QQWXnfwKmalFiw==", "signatures": [{"sig": "MEQCIBIw8KdiK4LXlCyvJC7amGDfMya1rRj64JUX2NKhHoW+AiACI/7fEclBVccK3u32/H1uNSQcVj9fZk8muyDcPD7VJQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33477780}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "a7c88aad64f41812285f5b46eea329d4c7d24409", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.81_1745526688652_0.22981315937081215", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.83": {"name": "@anthropic-ai/claude-code", "version": "0.2.83", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.83", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "31d8e4bf209bc0746437c4000c741e2eb7e59905", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.83.tgz", "fileCount": 16, "integrity": "sha512-Mu7cIGKjNf9VMHPzNliaSuASILXIt2Vl6d28O8PQZHTfmFE1r4c+r/sExqwMN49pFAqKaJF37WfyfcJFPrhsQg==", "signatures": [{"sig": "MEYCIQDwDMYaltP5XkcJ5UuqYGr5wKOVuJeYnXH9QkkMdvVvFQIhAPvjOPvox6C8ar3o5tLcnozthx5vn271u3FeVgL4ODkk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34207996}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "e270f64f728c402fe85e63ec3b0f41e9426d2d57", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.83_1745601309351_0.9407609037957503", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.84": {"name": "@anthropic-ai/claude-code", "version": "0.2.84", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.84", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "cf494651af07e2742159dc92e33297776ac1c8a9", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.84.tgz", "fileCount": 16, "integrity": "sha512-9L5Z00U93mAu9nKdmpIRB9YM2Z1LDYExQtaPqrKBQV91N/JjtdXkVGaAkNUnhHbYHj/eXPJX2VDYFriZV7JA4Q==", "signatures": [{"sig": "MEUCIFhMvVeom2rCw9yFwtNYTVsAJByoxY+/HVesaz50wdbGAiEA8baS07uZotDCQJzGnQKV8WxcS5XjHhfsUgYB1bz6VC4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34208158}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "494b1872fe673261d188abe9238b42e179cfd859", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.84_1745607356411_0.4448589962085072", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.85": {"name": "@anthropic-ai/claude-code", "version": "0.2.85", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.85", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "d6e983def85427dedd509a806abf8a874339444a", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.85.tgz", "fileCount": 16, "integrity": "sha512-B<PERSON>wueIzRT4HbWubYwWp9nj2Ju/0ZKDVNXUghflpztXyK91rTjv1amFIbfWbUEvS9GEQeDqeqO4l3xM1kfdee2Q==", "signatures": [{"sig": "MEUCIFeMomNMrZN9MeWQYPhDz2bg4FJizrY3cyJ7sX7feijaAiEAnrXFOWSqj/PWQTVV46Pz9r52TgbD/vBKmvmnJPRERtE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34209038}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "f5f3c58281c1ffb5b5467d01f8a454e909bc8729", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.85_1745617718159_0.6373624723901985", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.86": {"name": "@anthropic-ai/claude-code", "version": "0.2.86", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.86", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "b17968ae54006c4460d49bf3a48b043017a6894c", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.86.tgz", "fileCount": 16, "integrity": "sha512-/tXtae5a8DzTbFJiGe1mHsgeVlD5hG5ObAsnc2yWu35vk8oCjU8w/mw5PFTMVR8K+Lcv9FNM8jmX8UfNwJSNyA==", "signatures": [{"sig": "MEUCIQD7oWo/C+RVYflWuar8WnUacjXO4Lo92CT5uM+yRBkqGgIgDxq23B4w275S9OLucfhZmW83yblKn4B8sljyLPeGCUM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34210004}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "1504c78d21c5d70d7d5ebfb1dff7ca11c99bf5b2", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.86_1745626068427_0.3766191463205586", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.89": {"name": "@anthropic-ai/claude-code", "version": "0.2.89", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.89", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "c439144e2c88a4355436836c9b4938cf53a95c29", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.89.tgz", "fileCount": 17, "integrity": "sha512-FXk1k86N2ep+YS6Pc87gRZyMnqpUue3GP4uIP0K6I6W+GSQseZEwZa8FbKcYgoqCNz1QCs9Yamk+raJ66htnJA==", "signatures": [{"sig": "MEUCIQCiicZzb/avJRsANyBw89hqMaz1b65gIGDcnT6iqIwo9AIgZ3u+2rrKQMPKsxyIX5oSwqkwawRDsVSV5bdrjK6hpHc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34186485}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "d1be1e7a5d650dbe46c731b4dc862560e8718013", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.89_1745871311853_0.5963619737429626", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.90": {"name": "@anthropic-ai/claude-code", "version": "0.2.90", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.90", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "7d695b9b7d02ef27f3796381d64aad83dd3cbcb3", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.90.tgz", "fileCount": 50, "integrity": "sha512-2EkO2Nna4EOe0Umy/LGOrxrkLQj/bEvLm3lpuFj+IhNT9FWT4mN28aiHxDc8BiV2TPRx3qCCyAbPqRNBf+sd/A==", "signatures": [{"sig": "MEUCIQDVi7ZL2QRseYiVZqgyOT6iKL9DcnNcx7b3MeM3MhRzlwIgIaEXW8SmOmfHuJWdKsvXsl3BFmV7D775jjOl6bA20ng=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46470349}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "6188d6ba704d492efeae7eb89735897def3c5b16", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.90_1745887519130_0.6942478758982324", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.91": {"name": "@anthropic-ai/claude-code", "version": "0.2.91", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.91", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "d1c6a23b424a33b25677b58dd2c99c9995b331d8", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.91.tgz", "fileCount": 54, "integrity": "sha512-7wStLc6CUfpK0j2wLV/Jr/YwVY6eXBzRirTNS4uujzgUKwXpofoSOE7R8R6GmSBkIC2eBkGWBNwR6pcwhinvow==", "signatures": [{"sig": "MEYCIQCupGB6eQTLlQFP/m4lRlMorUpeVC2rNlXBAXVRaG665QIhALhZeUKCDmrN8aPF7AbDpn2psMUEKBvatXRbtZK+hOYc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46497679}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "2ff33eff5ee16dce312187ac3de359461826bd31", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.91_1745957946313_0.07788436795046083", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.92": {"name": "@anthropic-ai/claude-code", "version": "0.2.92", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.92", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "d25619e84add08c43f8d835a7daea5a5169f1be8", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.92.tgz", "fileCount": 54, "integrity": "sha512-S/JaRJdenrN++TI7cTBUzu1PwckLlo7rAm/dNo0mSLVzLIIsCxwtTbsGZF+gDFYG3l9D8LymCr/HsuuUKG+rjQ==", "signatures": [{"sig": "MEUCIQDBvc3xDzzJ5RuqeQh62jd9wqkpbn6O9A5OEHLk5OLxgQIgQ5V3lpyyCUAXTEOCXCGXW0E96+IFjP+/42XNJm/vAcU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46498317}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "df866f7494268150423b4a35a0bd233eb101faa3", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.92_1745960548840_0.35333437906059717", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.93": {"name": "@anthropic-ai/claude-code", "version": "0.2.93", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.93", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "847854af41c1f99a82e5b89d7e170992e85f4464", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.93.tgz", "fileCount": 54, "integrity": "sha512-1cd1NK94a6Hhl2eUBLN4HiGIBvb2mNelKQjYYYV9+rAzohv4OyMyMvsuSUzEqMUWs4Zdrv+iNbP8bBmHn+ekfA==", "signatures": [{"sig": "MEQCIBRBk0Z/ywNiEDXACoEvLYlXJqDRoqmjFxADwzV5y4lOAiALicvX9nDlMQ6L6S9HMsv8dFBekmnU6NkfSi2N6LqvAg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46736301}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "36e38766819a3148ad6fbd2ea98b58f12c3f8023", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.93_1746036882332_0.23242352461720928", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.94": {"name": "@anthropic-ai/claude-code", "version": "0.2.94", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.94", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "96dff2d5a4261449fe78d490f4ae4cf82ed6c120", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.94.tgz", "fileCount": 54, "integrity": "sha512-IHYUqgX9FJbKoMSf1S0727wdmXENABE+fZii0CL2mBYuQ+yxg2FApz2JNX9I7J3Ss8EXZaw3AkoQe6138sSdBw==", "signatures": [{"sig": "MEUCIDrjGXXNRgNzR2DrtECQz7Q/G1bp/R5iG2upL7zfebAcAiEAlJzO+400LUrkxlIFa00wDJm/eaoPIh+INit+t0gKWG0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46736151}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "8088e35d629c840bc44332793c649ca2f92f85b0", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.94_1746040877387_0.18872946056460793", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.96": {"name": "@anthropic-ai/claude-code", "version": "0.2.96", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.96", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "a5bcc90f65c28993bff004809d954f031c3e0c7e", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.96.tgz", "fileCount": 54, "integrity": "sha512-B5t84vvH6bdIdBTViQ0osPSVfrhkLEksyipI7T/y7UzcKVKLnI7jW3HYF21bKvYdq6rJ41VWl/guowK1sSX/Gg==", "signatures": [{"sig": "MEUCIAgNXz4CdkrLMJGEmK00PHgAHAvL+7wllmp4VG980CspAiEAqQwC5c371sOB6IC2BqeUjTKHX2HihWGJ6Br7Sh2k4MQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46737831}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "b436bc4c95c22fb901a5664e1e81f5ca83b34bff", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.96_1746116628990_0.88518087772371", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.97": {"name": "@anthropic-ai/claude-code", "version": "0.2.97", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.97", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "36aad9e6598e587730cb76bc3692b4113a6253fa", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.97.tgz", "fileCount": 54, "integrity": "sha512-bAaxFyhlT3ZbHQwQdnw0UJ37qhCRwI6BTB4YssXgOe83A+vCpMJErE4g1gURMEPjmRAAALRXOvK2KpffW7djHA==", "signatures": [{"sig": "MEUCIAwcV4/IKjcXDMaRpWnxgq4/GWErDS5QBZmyl/a4/7ywAiEAn4xw2v3DDml/OcQt8G7gvsMaRbQOgpbwTpmNAqvpeig=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46737822}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "1dca6348f8d30d5f7f5ac0d90975c8e256e1586f", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.97_1746118387475_0.03874535053902051", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.98": {"name": "@anthropic-ai/claude-code", "version": "0.2.98", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.98", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "14c370b401f0129eb30a2a98cbbd9614d86f0bce", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.98.tgz", "fileCount": 54, "integrity": "sha512-V7gBJrebvyHGWXx/w36O9vXXPEWlrPtesRh4vLStZ8JyHyvRCHD0xd9/hRwi1VlJHSy62kIffZj/JbjoYmWVbg==", "signatures": [{"sig": "MEUCIQDdwmUE6QvgkBqreZ6OpPH26A4YMqEUhyv4TncTr1JTqgIgdlr9Zz/VqtaSNtcVyxY3JwYyR64x+FsH6EbVKe2U+FY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46739225}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "ba0c266bf404e604dddece9003c5aa47269b68b0", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.98_1746143310360_0.2670325527896773", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.99": {"name": "@anthropic-ai/claude-code", "version": "0.2.99", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.99", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "6acf88172c6c10a3d962d87ab43ec3052eb304e6", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.99.tgz", "fileCount": 54, "integrity": "sha512-KG+pFtiqSRmrSzcBRtgiPSqu9zaGg7GIlppKLEyDBlTn9M9JJbe1SLY5W8eB2AXC/fro+ePmM9cHzHO+zhkr7g==", "signatures": [{"sig": "MEYCIQCUgK/fwSTy4emG2/xgKwRK53D/HbeEleWrLtWX3Sl6/AIhAJ9DDXMUHH/RTucfZOMv+WEMSi0xBKoGZWLWjGacvVXO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46739627}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "4a7a597d6e1c01a82e9448e1aa58735eaca83b89", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.99_1746152895230_0.8196838270267828", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.100": {"name": "@anthropic-ai/claude-code", "version": "0.2.100", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.100", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "844e341f636f779e696bc0db285bc7bfe57842a1", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.100.tgz", "fileCount": 54, "integrity": "sha512-SzYrQz6GtNRbVR37j0rz0bzMlbGOVrysua6/JfpPVYqeb3bjtnoOuHEebM0ko3K/bkNg68lca+WsT96tjnxvtA==", "signatures": [{"sig": "MEQCIAnlW+U5ZYDLp96L6jMmYw4ZLsXcYm3zJnyOu7wvp8iUAiBMiEMBVb0aRKocUUrrsyxaOD8WjBuZc96AGtVdHjGxsQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46739303}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "372b4558fc69698281c4a996cd00f055716a22b8", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.100_1746206137957_0.7257265206512553", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.101": {"name": "@anthropic-ai/claude-code", "version": "0.2.101", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.101", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "a6b3dca19119af385eeac5a17f4137ed6836163b", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.101.tgz", "fileCount": 61, "integrity": "sha512-WFfJ4LFize/YOBruAu8/4kX9AWnCWMUSvvaISvJQYxFZwUUVsPGAgtVf4VsWJMYLOSO99gU8unyyGRfRYfretg==", "signatures": [{"sig": "MEQCIAXG2h7EOvFuokA11+l6aV0Etlh2EbQZSwklCIL/bQSgAiAvooU+08KWN20LI6tVctvGVheuGARdz0qOKQnBlOsSDg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168811278}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "cae5b1764510d991517073c4b28aa946cbdb9dce", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.101_1746458658368_0.8626106709116772", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.102": {"name": "@anthropic-ai/claude-code", "version": "0.2.102", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.102", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "b6f59391184900ebeb0e89e75245accc6436c576", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.102.tgz", "fileCount": 62, "integrity": "sha512-zJV6aOHoPhOe3n6TLd4zhoHHG0T/lc7iWiW17xIOvnE31lEALTZZa/a6VBUR+sTTOr3RAy/QfYOs5zq51qIKvA==", "signatures": [{"sig": "MEQCIEM4anvPPhVMLdoI8gb5TyA1rC67Hj/m0yOK1ZTkm0U0AiAj8ucVVfCsLFXHesEyZEYRfJeXBq5/ijUgV+VrGTXUrg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168291421}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "8a05d20d51bd05fd1606f99c538ec0b8fb89910c", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.102_1746485930366_0.15886117355279095", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.103": {"name": "@anthropic-ai/claude-code", "version": "0.2.103", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.103", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "5706f1f48e6de9773a0e5e9f4f68d4c7fe90f917", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.103.tgz", "fileCount": 61, "integrity": "sha512-xIQf2JyOiuOz6D4QEUVsFb00Wn6WfYHAGs+iB9trB7jfbxzctr9iYC2gRptSzzy7B8Y/MU8cpW8+lH5qjA7c+Q==", "signatures": [{"sig": "MEQCICiMtkZJ6eOUQDhPMuzeXi+/eadTuAS8PktZPYnGR/00AiA6TW6+CHJUeTzt0cL/YckvxQqiUHNKla8z1gYQvZ3Yrw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168296460}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "ca1f1c99b89e7455d8ff77139a8762ccc5546c25", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.103_1746575939927_0.31290019403321123", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.104": {"name": "@anthropic-ai/claude-code", "version": "0.2.104", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.104", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "e5a4f8ed38433ffc4488e6c7603c02406dc7adf8", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.104.tgz", "fileCount": 61, "integrity": "sha512-HYeJ/G3Wa0YgDrYFSoFVfH374rmxX7T00LMuEUsLdpzJhKx6qsnfpqQmCKLCLtNda5NG5F/P7OkFSIjJSqEEkA==", "signatures": [{"sig": "MEUCIQCiN12Icl8tZzDGdeVehWXEOj7hIVm1COJ44t+jLU52UQIgNf3Ys2hKzSyQZIY+GykXRNzP2FyW7+/k91YRkHC9k0I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168329912}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "185635ce73941cd47086040d1164ace860e77ed0", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.104_1746647616412_0.25573887769583536", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.105": {"name": "@anthropic-ai/claude-code", "version": "0.2.105", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.105", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "202540137819974a3e2505c6d6e54bca257f2553", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.105.tgz", "fileCount": 61, "integrity": "sha512-GR4fOK6WTTgMzM66eHBZsaOA2fKZULgfeZoi87v6Fv6hXGy8SPbrdAH8ULKAPlBqxGlPkUo1KcfyXD5VE28Olw==", "signatures": [{"sig": "MEUCIDaQG8T3KuHnJOP1XJaFFF44nhMp9M4A922qV8tlmKReAiEAp5x6qgnOmNPzfpiETF9BUU/ORnNgCKvy0yw5VZhIH5c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168335877}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "dd8ac684b869e7255757e0117f83eeab08df99ef", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.105_1746722493584_0.5995376791312468", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.106": {"name": "@anthropic-ai/claude-code", "version": "0.2.106", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.106", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "845b28d84edac39ce9b23f6193a2526dbbe48015", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.106.tgz", "fileCount": 61, "integrity": "sha512-bryGjkA4VhQ4E7McxHVGgectYP/J9zhtkOP9nMNkLPepi5m85tyPHOZgTIhA3R3N7wrIRHPlpMv5y2GjGOo5TQ==", "signatures": [{"sig": "MEUCIG6ORxtmKBVPGJ57As2MIAA9kxJ9WqqbsA6AGpriRwt5AiEA3V4xVzm7wpxrNeoBtrqr28DKftdHgePk7T3LqBnY0hc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168359635}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "562a18b74f88b87874421cf12358c8bcb28a95a1", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.106_1746757990978_0.68926497417792", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.107": {"name": "@anthropic-ai/claude-code", "version": "0.2.107", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.107", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "c4c7e6308bb8b58a98fec91ec1a499a8495430b9", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.107.tgz", "fileCount": 61, "integrity": "sha512-Z5UzGWfK3ohvuVBG0NZfPFP2S8PXZk37c6hmoWI7vtPNa7rCtKNyexqCs1kjN4oW8Ou9eIooeQd+FOXdKTTJFw==", "signatures": [{"sig": "MEUCIEWKSQ+3kAbgMFgD3WnX+nXyMbfepPcNSS5dWpyhSJN+AiEAhKHYbecxks6hGdHOk/R9qaYYPCuInSI1YCs7vS3JP20=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168359934}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "44f9ffa45f28f04074726570f595dfd930638f39", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.107_1746807302993_0.03459097147505208", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.108": {"name": "@anthropic-ai/claude-code", "version": "0.2.108", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.108", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "4348a6a8e9fd28fcf32958cd3aa994ebadeb8005", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.108.tgz", "fileCount": 62, "integrity": "sha512-ponVKdxl4JNPVlrcYZ0+GPUkgwhRsyYxVWrF7F+15052JRAE8BI6y98FEC7Ne87/5jesXIhEz/CG77MKomxD/w==", "signatures": [{"sig": "MEUCIBaJabnPOqZSYNLeKzUf0htSUJZuvfP5BY6429ycmVxyAiEAgJObLaBgA9fvStdtddwShBKYhGpGx9x869DZC0/R41U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168886148}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "b7951cbfd93373fcd60282635d253ffd6f0c7326", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.108_1747101351459_0.8183634163677127", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.109": {"name": "@anthropic-ai/claude-code", "version": "0.2.109", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.109", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "539eb2fb082d631c0fa0e3d214f06d30136d2490", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.109.tgz", "fileCount": 62, "integrity": "sha512-dxOUgATehMCBZHzC47H3i6CCAf2YlXQMcTxrfzl0XEkwqEhbT8EzwWVbqDVc7n/eLykY2SK7xf5hD1iK8kr/dw==", "signatures": [{"sig": "MEUCIF6pd3fg0HowaVl/766+xNMEXSQ/8W8vRKN+N45XQXkYAiEA4vwE+9lKqN+v/sjN0QvXzTiUO6BD+mOGuj+EFqqkXTo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168892168}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "286ce553fe3be522d984ee0adc40a846208fe37a", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "deprecated": "use 0.2.113 instead", "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.109_1747159034301_0.5908879686238491", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.113": {"name": "@anthropic-ai/claude-code", "version": "0.2.113", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.113", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "dba44bbb5f4786b87ae4e221abd52a1182592ba1", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.113.tgz", "fileCount": 51, "integrity": "sha512-f+EeSzkSph8G5r45yexfGFH1TYxd0XxDe2FUCQzn1XUorFyREtNivVRZRiR6ZfWnl7D1yC2O0vaMrIvfy9vKKQ==", "signatures": [{"sig": "MEUCIHFHQMzIsHri5JcRS5/v1axIIIcY6hKeg3+tYbfZ/rgpAiEAjJDGYzmR9qi29Db6tHwXq5DhoIIOqRTVXa7aXK39nic=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168614994}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "72117ef64a0432ae79f90e7644add22ac997de90", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.113_1747180388436_0.2983733785890186", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.114": {"name": "@anthropic-ai/claude-code", "version": "0.2.114", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.114", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "8e1a63665e047982d2fe49d0a1aec891dd054124", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.114.tgz", "fileCount": 51, "integrity": "sha512-Wa+5VcY0jTWAuwBjguPzER5qNXpaT2Ye2ssaCADrWjWHv1gRp9OEtnX8mYPypywyB6rmKeMzioMDrtHAW3VD0w==", "signatures": [{"sig": "MEYCIQCm+Az0NdVyC7riVzkIcIgoe3pBvG87FvmJJ2kljjpvWgIhAJ/ybDxmb8cfxaLaKzRyWSNOFjS3t/Aai5fM+JVxdpJ4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168613687}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "23c34bd6c3c942d0c01bbf27f927d84f3cdf4bed", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.114_1747240234947_0.7248795366562046", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.115": {"name": "@anthropic-ai/claude-code", "version": "0.2.115", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.115", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "c1e365e8ba870d36bea3642df6af01f96d0f792c", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.115.tgz", "fileCount": 51, "integrity": "sha512-hyCDa/WQTbPZEhnrzOfOMsJVaZNMZVHKeMwQ8KQVjSvCXlsEaOotSJWB62uyAXCam1DXJ3ZDcrHNvpodMzJgaA==", "signatures": [{"sig": "MEQCIDpOZ2FQQkp6zjZjA9+8Omoz7AbcYKczkQfyOdFyGLspAiBOyDSClV+ri7ntzyw6vERpoD7NWB7UDk59oP1CU4RJHg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168619084}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "9de6fa75a372026e7f71d98cd73ac8fa93efee25", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.115_1747324650733_0.5156721663524555", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.116": {"name": "@anthropic-ai/claude-code", "version": "0.2.116", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.116", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "d4bfb2d8b0184fa481f5c35d7218c5b3e77587d6", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.116.tgz", "fileCount": 51, "integrity": "sha512-APM1K+tY1IJ5lbvy8KUgokll1BJF+/Id5UmT/vTzuqAdiVHi7bBHe3a5fpI7nP/ye3T2T9kS2Lc26oQfRUfRrA==", "signatures": [{"sig": "MEUCIQCahIUjS6vHwmAJpkJTyqetqAIgcpc5L4lKDrWzx7tJLgIgRAkJZRtefMX89oo2Q/YHE7Q6YDFL+3Lji+U6U8Qt1RU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168165052}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "3e33f57a63cc6b6ec5fd17cd0950308611dd5aee", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.116_1747513309093_0.6558324843379173", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.117": {"name": "@anthropic-ai/claude-code", "version": "0.2.117", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.117", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "63f1c138e2c47320665e6627f112398d2ec8afcf", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.117.tgz", "fileCount": 51, "integrity": "sha512-0mJr2n7bxoBLpxT7DjhOXkVK7lZB3ML5uUAhC3pny/IipF6Q+05XPO2UNoaNLB/9yhXSUmu0cNwmHz6m1e2Oqw==", "signatures": [{"sig": "MEUCIGk4QPL3qiSo97kZlXq+Yuz6OcrlJV7Tmb/lY9yBvBEAAiEAzqmzmzIefj5JJE8m/4ODdiKZsLfTueB2OpU1MrcU5es=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168173417}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "782ccd48dcfa9c81bd87d9a16b03906c00a539c6", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.117_1747525886430_0.37135216539670246", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.118": {"name": "@anthropic-ai/claude-code", "version": "0.2.118", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.118", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "8bf0740da7a8cf27e20ce5af074e1b21753a8865", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.118.tgz", "fileCount": 51, "integrity": "sha512-pMz0P7rn/Dl/epaoPCPJnK8yFy7HlqmbFgHU/Jx/WNSlCkyEYWJil2aaGoCcFlnv5FVWak5eU5uXOcSGfopfGQ==", "signatures": [{"sig": "MEUCIA/83zYO2g28k7tg7ysbKwT7cZ5la8RoeEewQIdJV5N6AiEAiOU7fSenKrjwL0IaJnnxC1g2V5OXcReiMmRyJhDuVbQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168172469}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "6233cf933fe9b2e18f49d42fd4eaa2c59db4e625", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.118_1747587269527_0.16812414206641613", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.119": {"name": "@anthropic-ai/claude-code", "version": "0.2.119", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.119", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "71b83fbd0d0be9d6b15fd1b6d9856949831bbb12", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.119.tgz", "fileCount": 52, "integrity": "sha512-fS73GNzZi7kr8tmohj3aFckhHmf6/65hD7YzXSO7PgokLkU+xldZmo+QEYHG35dyLnnh34Tyewc16usSToB45A==", "signatures": [{"sig": "MEYCIQDsVUr+h+kLq50ToPw4G+MAQXPT6R/ouX1K78Pht6yhCAIhANtZDdmDB6vi4xYNCNOdDM9yPy4XyNqjUoApOCwRklLC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 174630535}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "31463d251f0d62ba912022a06c74b636a990d76b", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "deprecated": "deprecated", "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.119_1747628814963_0.27746598700598835", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.120": {"name": "@anthropic-ai/claude-code", "version": "0.2.120", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.120", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "cfe62a5d27953e0f1a7515a1b6c39bb1bedc4f4e", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.120.tgz", "fileCount": 51, "integrity": "sha512-VRKDu1iYAExfFi24o3etWvXCq7WJlXYTZ412Qh7kUarxlMF+3Sq1KMrBdLWffZiT944EC7O7l92EbRUD87Mmpg==", "signatures": [{"sig": "MEYCIQCNdvcyzIQ9dfa0x31TPcub3gY/NkGwkf/F1Qmp9NpAywIhAKTA+o6s0vbovfElc6fk4TUR9U+NWiMa6M0psGnLyoc8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168175314}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "769a92bce62ce2fd988ced63100fa16d2f12e941", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.120_1747630752972_0.05394457359110949", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.122": {"name": "@anthropic-ai/claude-code", "version": "0.2.122", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.122", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "01c6cbbe3d860934816db191202e46dd915d2eb0", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.122.tgz", "fileCount": 51, "integrity": "sha512-q9XnW6a4btqHM2XYxkcl2d7dDNRTX8pvaeisiNWYzAOSKC+wUfOrkioUUS3BG+i6sNtJB03jPKJdqvEvtXbZjw==", "signatures": [{"sig": "MEQCIGdjBPePccYsgapGUee61y3EOM8Zu8GCIEEXJPUDjs4dAiBsECDw0aCP63r0ML9AN71wuvmky3UMqPB/EOs65bGD9A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168208129}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "f3471df94638fdb6497321443b4cdac8fd601a51", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.122_1747673375827_0.9468288382497045", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.123": {"name": "@anthropic-ai/claude-code", "version": "0.2.123", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.123", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "782265c9c6320cf25dad7993c3474a9461aae30f", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.123.tgz", "fileCount": 51, "integrity": "sha512-UpnhrDAh8UUWU+lVuhJl27sn/CEMtjMsM1/jJJvv0JPjNacIhl/tlkSoXFzE+eu40z7P1GNX3HCvLXFu3FemQg==", "signatures": [{"sig": "MEQCIDH7NIImelLHqwVPFV5MJToUs1C1N8Y4Ja6zS21wmVNkAiAtnCbJNXGQ3gwCYpPUs07db3hwGR74nDineESbEdlkhA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168417440}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "a1af6adf60dfd5ac18cdffb27d723e84602ef9f9", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.123_1747768950507_0.04372770640102086", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.124": {"name": "@anthropic-ai/claude-code", "version": "0.2.124", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.124", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "a2c7fd137a2ae0697ef8855f0f47f0e380a5fb5c", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.124.tgz", "fileCount": 51, "integrity": "sha512-W/sPzrDhZq/M6vOK0SsWBWbHZ1SMsstq+tPjNCz10e7l6QVrCxfSSHAKIJRgmXAJRl5cEAt2pcu4Wor0cGimbA==", "signatures": [{"sig": "MEYCIQC64g0ZZv9M7hY2bhl0ZQ2Sleryif8LdDd7Cq21UD6zywIhAP3SQ23l0hB98LL+dx3yfSIqQCWU3TsTUK2waQ0+D5E5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168417445}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "b5b62056f0519e200da8bfee037321c5b5b8293b", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.124_1747772899190_0.8796575084565779", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.125": {"name": "@anthropic-ai/claude-code", "version": "0.2.125", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.125", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "3af47707395eb958a6315ece3617519300179761", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.125.tgz", "fileCount": 51, "integrity": "sha512-HP1COHPpsF3aJC3RxsaMp+utiBoCId3FSBhSvGPi8w3y9MBnYE1S6ku75AV9PCm+bY8wxGgDNXfHhhqqaxLXLw==", "signatures": [{"sig": "MEUCIC7cUAuphs7SD1YKWr50ykKOJ4f46TzdkdPEGTY9MiDDAiEAndVX18Uok0uz2Nhq0F9HkSJc14F/BTAgizcvrnDBWiY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168787650}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "368e9c778ca28481fea03451fca72c68c05fa922", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.125_1747850410669_0.8796100950044463", "host": "s3://npm-registry-packages-npm-production"}}, "0.2.126": {"name": "@anthropic-ai/claude-code", "version": "0.2.126", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.126", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "15ce31919d168b6c848d7268457b02b64db9a8b2", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-0.2.126.tgz", "fileCount": 51, "integrity": "sha512-YBAtMs0OobKlrmvCicl8DZa8uLOSiaorW+V0aVYeGVU6q/lf7ZoPmckZT2B3e8rS43tgXmjadK/KHdJ1UztT6Q==", "signatures": [{"sig": "MEUCIE3pv/bTyzMcpxAvjoXtxZK9YTNDBXlfqLf1k5+AtHzuAiEAinQPTlTjNK8nSbSsLPXJXiU6aSQ+EfHJh85aICR4LyM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168797165}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "a86f61319be23e0c03956a58c6abef370ed8accd", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.126_1747880527027_0.5528580262120526", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0": {"name": "@anthropic-ai/claude-code", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.0", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "e28b1848914925f16c086f555e6c1351f73463a0", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-1.0.0.tgz", "fileCount": 51, "integrity": "sha512-5+FV9oNtV3sZ8rq1pw3B6KX24Ib+qs9FoLR1ghxOAzk4ZXKzCSSHhW4GOWJy+6N9926KHN2wFNVcuZC9NW75Xg==", "signatures": [{"sig": "MEQCIAv+WbwXmIMWVX1dhOKr6bGjlMxakWUD827oSvItDpiYAiAGpp3wntfr8QPBWT29GrtKa7mX9Xr8UcyMdMu6cu9hjQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168828650}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "7313027bbfb09f241fac7c78bf29135a87f8037b", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.0_1747933037194_0.8931532341735808", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.1": {"name": "@anthropic-ai/claude-code", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.1", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "d28ac92f34b84d6d8d0adfb101609dced7272e49", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-1.0.1.tgz", "fileCount": 51, "integrity": "sha512-tMwmy2ESCrGd+mgiW45wCQtQAtsVtQSt2pcz+RkkKt9zgHaO5UqqzCJEmpf+4UBJitDp1ZQ3NiL+rxYtTwI51A==", "signatures": [{"sig": "MEUCIBJyqC2Yp36O8qlDQY6dxkWmOe64W68jMYxGShQ2RXPGAiEA7ZnG61pxoagAD3Hn0HsZv57nzngKk5b3WGxYUs+hIz4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168828876}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "5034bc9b81029d9fbd5c6cda0e5a5d5e5bf17e06", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.1_1747941816743_0.43994540457029774", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.2": {"name": "@anthropic-ai/claude-code", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.2", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "8708037720c7ffbe96c4a4b3eec458b57891a7bb", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-1.0.2.tgz", "fileCount": 51, "integrity": "sha512-6p7RMXEcVfymsJ7v0GyMfA3gcD2AbFVSb9b1Py4tbxM2ensjyaSx3wVQW2QiFk+9mhoRkYc0ETVnJ1x/G0LW7g==", "signatures": [{"sig": "MEQCIG33LnhvL6QRNEoz0M/SEGEVg2mL543IWAvnYznlUzWqAiBpvEB+a//48lbLF6Y/0B//9FFIu5D6JJxGaPdAs6fekA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168828831}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "08ac91e2586dfff9e78091eb0002d1c53c470c40", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.2_1747950409986_0.08438328100352721", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.3": {"name": "@anthropic-ai/claude-code", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.3", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "8bf41d5c338f66737c25c48df500088e5343c1d2", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-1.0.3.tgz", "fileCount": 51, "integrity": "sha512-a4aBhcXGG7k4YtyF2aYVKt08h48BG+F12RA/G3K1vb6unl9CAvWDP/A7xudtCOzxFKxLwxctrn74pHEdpmHSPw==", "signatures": [{"sig": "MEYCIQDB6ygq0x0F+8ixd+xmVQmxWczzmAMSox3WetM88c+SMwIhAODegvYtRcCf4ugUIzDLdbT6wBEavCbuuHFV3+0+Vd5v", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168827655}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "54491bd2926f5856e119903a4c58a1eddccd0b76", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.3_1748040372403_0.15166697725473477", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.4": {"name": "@anthropic-ai/claude-code", "version": "1.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.4", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "34bc3a8239a9481c9f1561578b03c54a43a64272", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-1.0.4.tgz", "fileCount": 52, "integrity": "sha512-INnMutAQ4K7wk7Luau9IQ+YSqTls4UUMYq9tQjwH3H/rqOn46XHW1Sg8qO01uobyuijut66XDQJYY7yC6nq22Q==", "signatures": [{"sig": "MEUCIQDb5HN/P1zet2+wPHyFfq8wRTKXuS/WHOtiJgA6DwYfBwIgKNpg8B0IEUPvQXCGo7IxAH3E/i6sSsLEYKt+Sp0iwCs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168841040}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "6bb6ecc153901077f6287ccbc0fb489143dfba66", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.4_1748454163459_0.9337302704658563", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.5": {"name": "@anthropic-ai/claude-code", "version": "1.0.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.5", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "9ad1aca30d6efd4a499de20be4f2e22d9e8383ee", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-1.0.5.tgz", "fileCount": 52, "integrity": "sha512-A4RkIuHktcNygeZXfj/EcF1M5KXlPSwBBXLQLldUNyX4DFAeCpKh9b0UqnLlMd519QBmfmRYDvZqUlt7Fx0a7g==", "signatures": [{"sig": "MEUCICKJ9Q/ffJJ/WweAGx91EhJhtGyXFvzSKxX664ncPRcoAiEA1gjBBYvYpg8EuXZXaD08YYQzwQelZnHLauz4pyH3ZOE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168842788}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "edb526975a3cce5a458d90a6ca84c48ab1dc188f", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.5_1748473682036_0.5705154015547615", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.6": {"name": "@anthropic-ai/claude-code", "version": "1.0.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.6", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "971dcaac7ce01a94325e8307b5d64654a933d000", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-1.0.6.tgz", "fileCount": 51, "integrity": "sha512-QqPZZ4IXbUaSLqXEx4z68Stjc7NlGh/b4woyYyMks+cMUJ5ZYDEkAe09cRZQ6tjubZNgBaGTOqfTaT8NgnkioQ==", "signatures": [{"sig": "MEUCIEdQb03J8rpuJ1NM3H6BSgHqFWV8uLOAb6f81I7p6bSiAiEAtdzofm+XyDG2fvQVWmdvdDIf6Qr94JE2Be7KD8d/8bM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168814346}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "80c26058757c6423dc2b4413e22bbd2739455034", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.6_1748536438345_0.9433181591643349", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.7": {"name": "@anthropic-ai/claude-code", "version": "1.0.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.7", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "da4c4b583820683644e04d3742f6dfafad7eb59c", "tarball": "https://registry.npmjs.org/@anthropic-ai/claude-code/-/claude-code-1.0.7.tgz", "fileCount": 51, "integrity": "sha512-DVkLtDVz9xKHsgm7L9AEcelWGQ6EYEKCKyytKKWHOT4pZ0Y1zUC8kZJtpOhT6MFO9p3TUvVIifJuZ3QUTqc0cw==", "signatures": [{"sig": "MEUCIDBUN+svKLyCnMuIMohJ9QEt6oLIilzeiIATkkTU5iOjAiEAraaEzAZaTOJJM0mQH7RHAWNZ61QttPzAwO6JXd9HZ4s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168847815}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "0549cd730d0e412aacecfc2d0570fdd67e3ce7dc", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.7_1748622588817_0.3186010652797706", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2025-02-24T18:03:40.929Z", "modified": "2025-05-30T17:50:36.879Z", "0.2.6": "2025-02-24T18:03:41.458Z", "0.2.7": "2025-02-24T18:31:57.017Z", "0.2.8": "2025-02-24T18:38:07.927Z", "0.2.9": "2025-02-24T20:50:11.262Z", "0.2.14": "2025-02-25T22:53:18.971Z", "0.2.18": "2025-02-26T21:59:37.558Z", "0.2.19": "2025-02-27T01:08:32.650Z", "0.2.25": "2025-02-27T23:08:59.360Z", "0.2.27": "2025-02-28T01:40:04.642Z", "0.2.29": "2025-02-28T20:20:23.700Z", "0.2.30": "2025-03-04T18:47:05.839Z", "0.2.31": "2025-03-05T17:48:20.869Z", "0.2.32": "2025-03-05T21:28:22.509Z", "0.2.33": "2025-03-07T23:05:16.317Z", "0.2.34": "2025-03-08T00:48:44.306Z", "0.2.35": "2025-03-08T01:23:34.228Z", "0.2.36": "2025-03-10T23:28:47.867Z", "0.2.37": "2025-03-11T20:49:25.325Z", "0.2.38": "2025-03-12T17:35:30.488Z", "0.2.39": "2025-03-13T01:21:08.162Z", "0.2.40": "2025-03-13T21:05:47.827Z", "0.2.41": "2025-03-14T01:34:42.240Z", "0.2.42": "2025-03-14T16:29:50.959Z", "0.2.43": "2025-03-14T22:43:00.299Z", "0.2.44": "2025-03-15T01:04:24.513Z", "0.2.45": "2025-03-15T03:23:03.350Z", "0.2.46": "2025-03-17T22:13:12.958Z", "0.2.47": "2025-03-18T01:42:37.884Z", "0.2.48": "2025-03-18T01:53:09.696Z", "0.2.49": "2025-03-18T19:18:21.616Z", "0.2.50": "2025-03-19T21:06:46.787Z", "0.2.51": "2025-03-20T17:15:04.548Z", "0.2.52": "2025-03-20T17:21:06.572Z", "0.2.53": "2025-03-21T18:04:02.580Z", "0.2.54": "2025-03-25T20:44:47.993Z", "0.2.55": "2025-03-26T21:40:53.726Z", "0.2.56": "2025-03-27T22:25:37.204Z", "0.2.57": "2025-03-31T23:49:44.153Z", "0.2.59": "2025-04-02T00:24:59.952Z", "0.2.60": "2025-04-02T17:30:14.740Z", "0.2.61": "2025-04-03T01:04:19.442Z", "0.2.62": "2025-04-04T02:56:58.288Z", "0.2.64": "2025-04-04T20:58:54.659Z", "0.2.65": "2025-04-07T22:29:15.495Z", "0.2.66": "2025-04-09T00:04:46.906Z", "0.2.67": "2025-04-09T22:17:47.830Z", "0.2.68": "2025-04-10T20:04:54.725Z", "0.2.69": "2025-04-11T22:30:28.481Z", "0.2.70": "2025-04-15T17:45:55.713Z", "0.2.72": "2025-04-17T14:21:16.372Z", "0.2.73": "2025-04-18T16:34:43.734Z", "0.2.74": "2025-04-18T22:33:26.843Z", "0.2.76": "2025-04-21T21:59:25.609Z", "0.2.77": "2025-04-22T18:07:27.857Z", "0.2.78": "2025-04-22T19:48:55.543Z", "0.2.79": "2025-04-23T17:24:18.815Z", "0.2.80": "2025-04-24T16:07:52.297Z", "0.2.81": "2025-04-24T20:31:29.117Z", "0.2.83": "2025-04-25T17:15:09.772Z", "0.2.84": "2025-04-25T18:55:56.821Z", "0.2.85": "2025-04-25T21:48:38.527Z", "0.2.86": "2025-04-26T00:07:48.834Z", "0.2.89": "2025-04-28T20:15:12.241Z", "0.2.90": "2025-04-29T00:45:19.628Z", "0.2.91": "2025-04-29T20:19:06.809Z", "0.2.92": "2025-04-29T21:02:29.314Z", "0.2.93": "2025-04-30T18:14:42.819Z", "0.2.94": "2025-04-30T19:21:17.898Z", "0.2.96": "2025-05-01T16:23:49.548Z", "0.2.97": "2025-05-01T16:53:07.961Z", "0.2.98": "2025-05-01T23:48:31.409Z", "0.2.99": "2025-05-02T02:28:15.844Z", "0.2.100": "2025-05-02T17:15:38.454Z", "0.2.101": "2025-05-05T15:24:19.332Z", "0.2.102": "2025-05-05T22:58:51.202Z", "0.2.103": "2025-05-06T23:59:01.370Z", "0.2.104": "2025-05-07T19:53:37.472Z", "0.2.105": "2025-05-08T16:41:34.425Z", "0.2.106": "2025-05-09T02:33:11.820Z", "0.2.107": "2025-05-09T16:15:03.847Z", "0.2.108": "2025-05-13T01:55:52.356Z", "0.2.109": "2025-05-13T17:57:15.176Z", "0.2.113": "2025-05-13T23:53:09.326Z", "0.2.114": "2025-05-14T16:30:35.917Z", "0.2.115": "2025-05-15T15:57:31.601Z", "0.2.116": "2025-05-17T20:21:49.934Z", "0.2.117": "2025-05-17T23:51:27.284Z", "0.2.118": "2025-05-18T16:54:30.382Z", "0.2.119": "2025-05-19T04:26:55.850Z", "0.2.120": "2025-05-19T04:59:13.833Z", "0.2.122": "2025-05-19T16:49:36.680Z", "0.2.123": "2025-05-20T19:22:31.365Z", "0.2.124": "2025-05-20T20:28:20.021Z", "0.2.125": "2025-05-21T18:00:11.547Z", "0.2.126": "2025-05-22T02:22:07.953Z", "1.0.0": "2025-05-22T16:57:18.061Z", "1.0.1": "2025-05-22T19:23:37.662Z", "1.0.2": "2025-05-22T21:46:50.893Z", "1.0.3": "2025-05-23T22:46:13.282Z", "1.0.4": "2025-05-28T17:42:44.392Z", "1.0.5": "2025-05-28T23:08:03.022Z", "1.0.6": "2025-05-29T16:33:59.854Z", "1.0.7": "2025-05-30T16:29:49.709Z"}, "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "homepage": "https://github.com/anthropics/claude-code", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "README.md"}