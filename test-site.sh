#!/bin/bash

echo "======================================"
echo "Template Invoice - Development Server"
echo "======================================"
echo ""
echo "Starting server on port 3000..."
echo ""
echo "Make sure you have created .env.local with:"
echo "- NEXTAUTH_URL=http://localhost:3000"
echo "- NEXT_PUBLIC_BASE_URL=http://localhost:3000"
echo ""
echo "Google OAuth Console must have:"
echo "- Authorized origins: http://localhost:3000"
echo "- Redirect URI: http://localhost:3000/api/auth/callback/google"
echo ""
echo "======================================"
echo ""

# Check if port 3000 is already in use
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  Port 3000 is already in use!"
    echo "Kill the process using: lsof -ti:3000 | xargs kill -9"
    echo "Or use a different port"
    exit 1
fi

# Start the development server
npm run dev