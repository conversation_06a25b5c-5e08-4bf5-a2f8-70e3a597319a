import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { renderTemplate, prepareInvoiceData } from './template-renderer';
import { InvoiceData } from './templates/template-definitions';

// PDF generation options
interface PDFOptions {
  format?: 'A4' | 'Letter' | 'Legal';
  orientation?: 'portrait' | 'landscape';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  printBackground?: boolean;
  displayHeaderFooter?: boolean;
  headerTemplate?: string;
  footerTemplate?: string;
}

// Default PDF options
const DEFAULT_PDF_OPTIONS: PDFOptions = {
  format: 'A4',
  orientation: 'portrait',
  printBackground: true,
  displayHeaderFooter: false,
  margin: {
    top: '20px',
    right: '20px',
    bottom: '20px',
    left: '20px'
  }
};

// Global browser instance for reuse
let browserInstance: Browser | null = null;

/**
 * Get or create browser instance
 */
async function getBrowser(): Promise<Browser> {
  if (browserInstance && browserInstance.isConnected()) {
    return browserInstance;
  }
  
  try {
    browserInstance = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ],
      timeout: 30000
    });
    
    return browserInstance;
  } catch (error) {
    console.error('Failed to launch browser:', error);
    throw new Error('Failed to initialize PDF generator');
  }
}

/**
 * Generate PDF from invoice data and template
 */
export async function generateInvoicePDF(
  templateId: string, 
  invoiceData: Partial<InvoiceData>, 
  options: PDFOptions = {}
): Promise<Buffer> {
  let page: Page | null = null;
  
  try {
    // Prepare and render template
    const data = prepareInvoiceData(invoiceData, templateId);
    const htmlContent = renderTemplate(templateId, data);
    
    // Get browser and create page
    const browser = await getBrowser();
    page = await browser.newPage();
    
    // Set viewport for consistent rendering
    await page.setViewport({
      width: 1200,
      height: 1600,
      deviceScaleFactor: 2
    });
    
    // Set content and wait for it to load
    await page.setContent(htmlContent, { 
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000
    });
    
    // Wait for fonts and images to load
    await page.evaluateHandle('document.fonts.ready');
    
    // Additional wait for any dynamic content
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Merge options with defaults
    const pdfOptions = { ...DEFAULT_PDF_OPTIONS, ...options };
    
    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: pdfOptions.format,
      printBackground: pdfOptions.printBackground,
      displayHeaderFooter: pdfOptions.displayHeaderFooter,
      headerTemplate: pdfOptions.headerTemplate,
      footerTemplate: pdfOptions.footerTemplate,
      margin: pdfOptions.margin,
      preferCSSPageSize: true,
      timeout: 30000
    });
    
    return Buffer.from(pdfBuffer);
    
  } catch (error) {
    console.error('PDF generation failed:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    // Always close the page
    if (page) {
      try {
        await page.close();
      } catch (error) {
        console.error('Error closing page:', error);
      }
    }
  }
}

/**
 * Generate PDF with custom HTML content
 */
export async function generatePDFFromHTML(
  htmlContent: string,
  options: PDFOptions = {}
): Promise<Buffer> {
  let page: Page | null = null;
  
  try {
    const browser = await getBrowser();
    page = await browser.newPage();
    
    await page.setViewport({
      width: 1200,
      height: 1600,
      deviceScaleFactor: 2
    });
    
    await page.setContent(htmlContent, { 
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000
    });
    
    await page.evaluateHandle('document.fonts.ready');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const pdfOptions = { ...DEFAULT_PDF_OPTIONS, ...options };
    
    const pdfBuffer = await page.pdf({
      format: pdfOptions.format,
      printBackground: pdfOptions.printBackground,
      displayHeaderFooter: pdfOptions.displayHeaderFooter,
      headerTemplate: pdfOptions.headerTemplate,
      footerTemplate: pdfOptions.footerTemplate,
      margin: pdfOptions.margin,
      preferCSSPageSize: true,
      timeout: 30000
    });
    
    return Buffer.from(pdfBuffer);
    
  } catch (error) {
    console.error('PDF generation from HTML failed:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (page) {
      try {
        await page.close();
      } catch (error) {
        console.error('Error closing page:', error);
      }
    }
  }
}

/**
 * Generate invoice PDF and return as base64 string
 */
export async function generateInvoicePDFBase64(
  templateId: string,
  invoiceData: Partial<InvoiceData>,
  options: PDFOptions = {}
): Promise<string> {
  const pdfBuffer = await generateInvoicePDF(templateId, invoiceData, options);
  return pdfBuffer.toString('base64');
}

/**
 * Take screenshot of invoice for preview
 */
export async function generateInvoiceScreenshot(
  templateId: string,
  invoiceData: Partial<InvoiceData>,
  options: { width?: number; height?: number; quality?: number } = {}
): Promise<Buffer> {
  let page: Page | null = null;
  
  try {
    const data = prepareInvoiceData(invoiceData, templateId);
    const htmlContent = renderTemplate(templateId, data);
    
    const browser = await getBrowser();
    page = await browser.newPage();
    
    const width = options.width || 1200;
    const height = options.height || 1600;
    
    await page.setViewport({
      width,
      height,
      deviceScaleFactor: 2
    });
    
    await page.setContent(htmlContent, { 
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000
    });
    
    await page.evaluateHandle('document.fonts.ready');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const screenshot = await page.screenshot({
      type: 'png',
      quality: options.quality || 90,
      fullPage: true
    });
    
    return Buffer.from(screenshot);
    
  } catch (error) {
    console.error('Screenshot generation failed:', error);
    throw new Error(`Failed to generate screenshot: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (page) {
      try {
        await page.close();
      } catch (error) {
        console.error('Error closing page:', error);
      }
    }
  }
}

/**
 * Close browser instance (cleanup)
 */
export async function closeBrowser(): Promise<void> {
  if (browserInstance) {
    try {
      await browserInstance.close();
      browserInstance = null;
    } catch (error) {
      console.error('Error closing browser:', error);
    }
  }
}

/**
 * Check if browser is available
 */
export async function isBrowserAvailable(): Promise<boolean> {
  try {
    const browser = await getBrowser();
    return browser.isConnected();
  } catch (error) {
    return false;
  }
}

// Cleanup on process exit
process.on('exit', () => {
  if (browserInstance) {
    browserInstance.close().catch(console.error);
  }
});

process.on('SIGINT', () => {
  if (browserInstance) {
    browserInstance.close().catch(console.error);
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  if (browserInstance) {
    browserInstance.close().catch(console.error);
  }
  process.exit(0);
});

export type { PDFOptions };