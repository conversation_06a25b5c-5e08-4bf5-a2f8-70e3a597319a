import type { Metadata } from 'next'
import Link from 'next/link'
import { ArrowLeft, BookOpen, Clock, User, FileText, CreditCard, Settings } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Invoice Guides - Learn How to Create Professional Invoices',
  description: 'Complete guides on creating invoices, customizing templates, processing payments, and managing clients. Expert tips for better invoicing.',
}

export default function GuidesPage() {
  const guides = [
    {
      title: "Getting Started with Template Invoice",
      description: "Learn the basics of creating your first professional invoice",
      icon: BookOpen,
      readTime: "5 min read",
      category: "Beginner",
      href: "/guides/getting-started"
    },
    {
      title: "Customizing Invoice Templates",
      description: "How to personalize templates to match your brand",
      icon: FileText,
      readTime: "7 min read",
      category: "Design",
      href: "/guides/customizing-templates"
    },
    {
      title: "Setting Up Payment Processing",
      description: "Configure Stripe and other payment methods",
      icon: CreditCard,
      readTime: "10 min read",
      category: "Advanced",
      href: "/guides/payment-processing"
    },
    {
      title: "Managing Clients and Contacts",
      description: "Organize your client information efficiently",
      icon: User,
      readTime: "6 min read",
      category: "Organization",
      href: "/guides/managing-clients"
    },
    {
      title: "Account Settings and Preferences",
      description: "Configure your account for optimal workflow",
      icon: Settings,
      readTime: "4 min read",
      category: "Account",
      href: "/guides/account-settings"
    },
    {
      title: "Invoice Automation and Recurring Billing",
      description: "Set up automatic invoicing for recurring clients",
      icon: Clock,
      readTime: "8 min read",
      category: "Advanced",
      href: "/guides/automation"
    }
  ]

  const categories = ["All", "Beginner", "Design", "Advanced", "Organization", "Account"]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <Link 
            href="/" 
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>
        </div>
        
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Help Guides</h1>
          <p className="text-xl text-gray-600">
            Everything you need to know about using Template Invoice
          </p>
        </div>
        
        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              className="px-4 py-2 rounded-full border border-gray-300 text-gray-700 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-colors"
            >
              {category}
            </button>
          ))}
        </div>
        
        {/* Guides Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {guides.map((guide, index) => {
            const Icon = guide.icon
            return (
              <Link
                key={index}
                href={guide.href}
                className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 group"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="bg-blue-100 p-3 rounded-lg group-hover:bg-blue-200 transition-colors">
                    <Icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                    {guide.category}
                  </span>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-700 transition-colors">
                  {guide.title}
                </h3>
                
                <p className="text-gray-600 mb-4 text-sm">
                  {guide.description}
                </p>
                
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="w-4 h-4 mr-1" />
                  {guide.readTime}
                </div>
              </Link>
            )
          })}
        </div>
        
        {/* Quick Start Section */}
        <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Quick Start</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold text-xl">1</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Create Account</h3>
              <p className="text-sm text-gray-600">
                Sign up with Google and set up your business profile
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold text-xl">2</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Choose Template</h3>
              <p className="text-sm text-gray-600">
                Select from our professional invoice templates
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-purple-600 font-bold text-xl">3</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Send Invoice</h3>
              <p className="text-sm text-gray-600">
                Generate PDF and send to your client instantly
              </p>
            </div>
          </div>
          
          <div className="text-center mt-8">
            <Link 
              href="/create"
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-block"
            >
              Create Your First Invoice
            </Link>
          </div>
        </div>
        
        {/* Need More Help */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Need more help?
          </h3>
          <p className="text-gray-600 mb-6">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <div className="flex justify-center space-x-4">
            <Link 
              href="/contact"
              className="bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            >
              Contact Support
            </Link>
            <Link 
              href="/support"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Visit Help Center
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}