import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Server-side imports are safe here
async function trackEventServer(eventData: any) {
  const { trackEvent } = await import('@/lib/analytics-enhanced');
  return trackEvent(
    eventData.category,
    eventData.action,
    {
      userId: eventData.userId,
      label: eventData.label,
      value: eventData.value,
      metadata: eventData.metadata,
      performance: eventData.performance
    }
  );
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const eventData = await request.json();

    // Add user ID from session if not provided
    if (session?.user?.id && !eventData.userId) {
      eventData.userId = session.user.id;
    }

    // Add request context
    eventData.metadata = {
      ...eventData.metadata,
      userAgent: request.headers.get('user-agent'),
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      source: 'frontend_tracking'
    };

    // Track the event using server-side analytics
    await trackEventServer(eventData);

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error tracking analytics event:', error);
    return NextResponse.json(
      { error: 'Failed to track event' },
      { status: 500 }
    );
  }
}