'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface PostUpgradeExperienceProps {
  isVisible: boolean;
  onComplete: () => void;
}

export default function PostUpgradeExperience({ isVisible, onComplete }: PostUpgradeExperienceProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isVisible) {
      // Track the post-upgrade experience
      trackPostUpgradeEvent('post_upgrade_shown');
      
      // Auto-advance through steps
      const timer = setTimeout(() => {
        if (step < 3) {
          setIsAnimating(true);
          setTimeout(() => {
            setStep(step + 1);
            setIsAnimating(false);
          }, 300);
        } else {
          // Auto-complete after showing all steps
          setTimeout(() => {
            handleComplete();
          }, 3000);
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isVisible, step]);

  const trackPostUpgradeEvent = async (event: string) => {
    try {
      await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          event: 'upgrade_completed',
          properties: {
            postUpgradeEvent: event,
            step,
            userPlan: 'pro',
          }
        })
      });
    } catch (error) {
      console.error('Error tracking post-upgrade event:', error);
    }
  };

  const handleCreateInvoice = async () => {
    await trackPostUpgradeEvent('create_invoice_clicked');
    router.push('/create-invoice');
    handleComplete();
  };

  const handleComplete = async () => {
    await trackPostUpgradeEvent('post_upgrade_completed');
    onComplete();
  };

  if (!isVisible) return null;

  const isPro = session?.user?.subscription?.plan === 'pro';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className={`relative bg-white rounded-2xl shadow-2xl max-w-lg w-full mx-auto transform transition-all duration-500 ${
        isAnimating ? 'scale-95 opacity-50' : 'scale-100 opacity-100'
      }`}>
        
        {/* Confetti Animation */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
          <div className="confetti">
            {[...Array(20)].map((_, i) => (
              <div
                key={i}
                className={`absolute w-3 h-3 opacity-80 animate-bounce`}
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][Math.floor(Math.random() * 5)],
                  animationDelay: `${Math.random() * 2}s`,
                  animationDuration: `${2 + Math.random() * 2}s`,
                }}
              />
            ))}
          </div>
        </div>

        {step === 1 && (
          <div className="p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-4xl">🎉</span>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Welcome to Pro!
              </h2>
              <p className="text-gray-600">
                You now have unlimited access to all premium features
              </p>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
              <div className="flex items-center justify-center mb-2">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Pro Plan Active
                </span>
              </div>
              <p className="text-sm text-green-700">
                Your subscription is now active and ready to use!
              </p>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-4xl">🚀</span>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Unlimited Invoices
              </h2>
              <p className="text-gray-600">
                Create as many invoices as you need, whenever you need them
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-blue-600 font-medium">✓ No limits</div>
                <div className="text-blue-800">Create unlimited invoices</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-3">
                <div className="text-purple-600 font-medium">✓ Premium templates</div>
                <div className="text-purple-800">Access all designs</div>
              </div>
            </div>
          </div>
        )}

        {step === 3 && (
          <div className="p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-4xl">📄</span>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Ready to Start?
              </h2>
              <p className="text-gray-600">
                Let's create your first unlimited invoice!
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={handleCreateInvoice}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors transform hover:scale-105"
              >
                Create Your First Pro Invoice
              </button>
              
              <button
                onClick={handleComplete}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-6 rounded-lg transition-colors"
              >
                Continue to Dashboard
              </button>
            </div>
          </div>
        )}

        {/* Progress Indicator */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="flex space-x-2">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className={`w-2 h-2 rounded-full transition-colors ${
                  i === step ? 'bg-blue-600' : i < step ? 'bg-green-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Skip Button */}
        <button
          onClick={handleComplete}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors text-sm"
        >
          Skip ✕
        </button>
      </div>
      
      <style jsx>{`
        .confetti div {
          border-radius: 2px;
        }
        
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0) rotate(0deg);
          }
          40% {
            transform: translateY(-20px) rotate(90deg);
          }
          60% {
            transform: translateY(-10px) rotate(180deg);
          }
        }
      `}</style>
    </div>
  );
}

// Pro Badge Component for showing throughout the app
export function ProBadge({ className = '' }: { className?: string }) {
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200 ${className}`}>
      <span className="w-2 h-2 bg-green-400 rounded-full mr-1.5 animate-pulse"></span>
      Pro
    </span>
  );
}

// Success Toast Component
export function UpgradeSuccessToast({ isVisible, onClose }: { isVisible: boolean; onClose: () => void }) {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onClose();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm w-full">
      <div className="bg-white rounded-lg shadow-lg border border-green-200 p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">✓</span>
            </div>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm font-medium text-gray-900">
              Successfully upgraded to Pro!
            </p>
            <p className="text-xs text-gray-600">
              You now have unlimited access to all features.
            </p>
          </div>
          <button
            onClick={onClose}
            className="ml-2 text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>
      </div>
    </div>
  );
}