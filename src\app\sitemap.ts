import { MetadataRoute } from 'next'
import { siteConfig } from '@/lib/seo-config'

type ChangeFrequency = 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = siteConfig.url
  
  // Static pages
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly' as ChangeFrequency,
      priority: 1.0
    },
    {
      url: `${baseUrl}/templates`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as ChangeFrequency,
      priority: 0.9
    },
    {
      url: `${baseUrl}/create`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as ChangeFrequency,
      priority: 0.9
    },
    {
      url: `${baseUrl}/create-invoice`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as ChangeFrequency,
      priority: 0.9
    },
    {
      url: `${baseUrl}/create/simplified-flow`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as ChangeFrequency,
      priority: 0.8
    },
    {
      url: `${baseUrl}/pricing`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as ChangeFrequency,
      priority: 0.8
    },
    {
      url: `${baseUrl}/dashboard`,
      lastModified: new Date(),
      changeFrequency: 'daily' as ChangeFrequency,
      priority: 0.8
    },
    {
      url: `${baseUrl}/my-invoices`,
      lastModified: new Date(),
      changeFrequency: 'daily' as ChangeFrequency,
      priority: 0.7
    },
    {
      url: `${baseUrl}/settings`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as ChangeFrequency,
      priority: 0.6
    },
    {
      url: `${baseUrl}/upgrade`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as ChangeFrequency,
      priority: 0.7
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as ChangeFrequency,
      priority: 0.5
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as ChangeFrequency,
      priority: 0.5
    },
    {
      url: `${baseUrl}/guides`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as ChangeFrequency,
      priority: 0.7
    },
    {
      url: `${baseUrl}/help`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as ChangeFrequency,
      priority: 0.6
    },
    {
      url: `${baseUrl}/support`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as ChangeFrequency,
      priority: 0.5
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as ChangeFrequency,
      priority: 0.3
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as ChangeFrequency,
      priority: 0.3
    },
    {
      url: `${baseUrl}/auth/signin`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as ChangeFrequency,
      priority: 0.4
    }
  ]
  
  // Guide pages - high SEO value
  const guidePages: MetadataRoute.Sitemap = [
    'getting-started',
    'customizing-templates',
    'payment-processing',
    'managing-clients',
    'automation',
    'account-settings'
  ].map(guide => ({
    url: `${baseUrl}/guides/${guide}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.7,
  }))
  
  // Invoice generator by industry/profession - high SEO value
  const invoiceGeneratorPages: MetadataRoute.Sitemap = [
    'freelancer',
    'photographer', 
    'consultant',
    'developer',
    'designer',
    'contractor',
    'small-business',
    'writer',
    'marketer',
    'videographer',
    'virtual-assistant',
    'lawyer',
    'accountant',
    'coach',
    'therapist',
    'tutor',
    'real-estate',
    'fitness-trainer',
    'musician',
    'artist'
  ].map(industry => ({
    url: `${baseUrl}/invoice-generator/${industry}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.8,
  }))
  
  // Invoice template by industry - high SEO value
  const invoiceTemplatePages: MetadataRoute.Sitemap = [
    'freelancer',
    'photographer', 
    'consultant',
    'developer',
    'designer',
    'contractor',
    'small-business',
    'writer',
    'marketer',
    'videographer',
    'virtual-assistant',
    'lawyer',
    'accountant',
    'coach',
    'therapist',
    'tutor',
    'real-estate',
    'fitness-trainer',
    'musician',
    'artist'
  ].map(industry => ({
    url: `${baseUrl}/invoice-template/${industry}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.8,
  }))
  
  // Template style variations
  const templateStylePages: MetadataRoute.Sitemap = [
    'professional',
    'modern',
    'creative',
    'minimal',
    'classic',
    'elegant',
    'corporate',
    'colorful',
    'simple',
    'detailed'
  ].map(style => ({
    url: `${baseUrl}/templates/${style}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.7,
  }))
  
  // Development and monitoring pages
  const devPages: MetadataRoute.Sitemap = [
    {
      url: `${baseUrl}/monitoring`,
      lastModified: new Date(),
      changeFrequency: 'daily' as ChangeFrequency,
      priority: 0.3
    },
    {
      url: `${baseUrl}/mvp-checklist`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as ChangeFrequency,
      priority: 0.2
    }
  ]
  
  // Combine all pages
  return [
    ...staticPages,
    ...guidePages,
    ...invoiceGeneratorPages,
    ...invoiceTemplatePages,
    ...templateStylePages,
    ...devPages
  ]
}

// Optional: Create a sitemap index if you have multiple sitemaps
export async function generateSitemapIndex(): Promise<string> {
  const baseUrl = siteConfig.url
  const sitemaps = [
    `${baseUrl}/sitemap.xml`,
    `${baseUrl}/sitemap-dashboard.xml`,
    `${baseUrl}/sitemap-templates.xml`
  ]
  
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${sitemaps.map(url => `
  <sitemap>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>`).join('')}
</sitemapindex>`
  
  return xml
}