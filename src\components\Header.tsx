'use client'

import { Menu, X } from 'lucide-react'
import { useState } from 'react'
import Link from 'next/link'

export default function Header() {
  const [open, setOpen] = useState(false)
  
  return (
    <header className="sticky top-0 z-50 bg-white/50 backdrop-blur-lg shadow-sm border-b border-gray-100">
      <nav className="max-w-7xl mx-auto flex items-center justify-between px-4 py-4">
        {/* Logo */}
        <Link href="/" className="font-extrabold text-2xl tracking-tighter text-black">
          <span className="bg-black text-white px-2 py-1 rounded">TI</span> Template Invoice
        </Link>
        
        {/* Desktop Nav */}
        <ul className="hidden md:flex gap-7 text-lg font-medium text-gray-700">
          <li><Link href="/" className="hover:text-black transition-colors">Home</Link></li>
          <li><Link href="/templates" className="hover:text-black transition-colors">Templates</Link></li>
          <li><Link href="/dashboard" className="hover:text-black transition-colors">Dashboard</Link></li>
          <li><Link href="/pricing" className="hover:text-black transition-colors">Pricing</Link></li>
          <li><Link href="/auth/signin" className="hover:text-black transition-colors">Sign In</Link></li>
        </ul>
        
        <Link href="/create" className="hidden md:inline-block bg-black text-white px-6 py-2 rounded-lg font-semibold shadow hover:bg-gray-900 transition">
          Create Invoice
        </Link>
        
        {/* Mobile Menu Button */}
        <button
          className="md:hidden p-2 rounded focus:outline-none focus:ring-2 focus:ring-black"
          aria-label="Toggle Menu"
          onClick={() => setOpen(!open)}
        >
          {open ? <X className="w-7 h-7 text-black" /> : <Menu className="w-7 h-7 text-black" />}
        </button>
      </nav>
      
      {/* Mobile Menu */}
      {open && (
        <div className="fixed inset-0 z-50 bg-black/95 backdrop-blur-lg flex flex-col items-center justify-center space-y-8 text-2xl font-semibold md:hidden">
          <button
            className="absolute top-4 right-4 p-2 text-white"
            onClick={() => setOpen(false)}
          >
            <X className="w-8 h-8" />
          </button>
          <Link href="/" className="text-white hover:text-gray-300 transition-colors" onClick={() => setOpen(false)}>Home</Link>
          <Link href="/templates" className="text-white hover:text-gray-300 transition-colors" onClick={() => setOpen(false)}>Templates</Link>
          <Link href="/dashboard" className="text-white hover:text-gray-300 transition-colors" onClick={() => setOpen(false)}>Dashboard</Link>
          <Link href="/pricing" className="text-white hover:text-gray-300 transition-colors" onClick={() => setOpen(false)}>Pricing</Link>
          <Link href="/auth/signin" className="text-white hover:text-gray-300 transition-colors" onClick={() => setOpen(false)}>Sign In</Link>
          <Link href="/create" className="mt-6 bg-white text-black px-8 py-3 rounded-xl shadow hover:bg-gray-100 transition" onClick={() => setOpen(false)}>
            Create Invoice
          </Link>
        </div>
      )}
    </header>
  )
}
