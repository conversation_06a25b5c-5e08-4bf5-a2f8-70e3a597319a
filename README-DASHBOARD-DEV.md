# Dashboard Development & Mock Data System

This document explains the dashboard development system that allows for continued development even when MongoDB is unavailable.

## Features

### 1. Mock Data System

The system includes a comprehensive mock data implementation that simulates a full dashboard data response:

- Located in: `src/app/mock-data/dashboard-data.ts`
- Provides realistic sample data that matches the DashboardData interface
- Includes all metrics, invoices, and analytics that would be present in real data

### 2. MongoDB Fallback Utilities

Utility functions that handle graceful fallback to mock data when MongoDB is unavailable:

- Located in: `src/lib/client-mongodb-fix.ts`
- `fetchDashboardDataWithFallback()`: Tries to fetch real data, falls back to mock data if needed
- `allowMockData()`: Environment-aware function that determines if mock data should be used

### 3. Development Dashboard

A dedicated dashboard route specifically for development:

- Located at: `/dev/dashboard` 
- Always uses mock data regardless of MongoDB connection status
- Includes clear visual indicators that it's using mock data
- Perfect for UI development and testing

## How to Use

### Option 1: Regular Dashboard with Fallback

The main dashboard (`/dashboard`) will attempt to connect to MongoDB first, but will fall back to mock data if:
- MongoDB is unavailable
- You're in development mode 
- The `NEXT_PUBLIC_ALLOW_MOCK_DATA` environment variable is set to 'true'

```typescript
// This is already implemented in ClientDashboard.tsx
const data = await fetchDashboardDataWithFallback(selectedPeriod);
```

### Option 2: Dedicated Development Dashboard

For UI development without worrying about data sources, use the dedicated development dashboard:

```
http://localhost:3000/dev/dashboard
```

This route always uses mock data and is ideal for:
- UI development
- Component testing
- Layout adjustments
- Responsive design testing

### Environment Variables

To control mock data behavior, you can set:

```
# In .env.local
NEXT_PUBLIC_ALLOW_MOCK_DATA=true
```

This allows the regular dashboard to use mock data in production environments when needed.

## Browser Compatibility

The project uses polyfills for Node.js built-in modules when running in the browser:

- `stream-browserify`: Polyfill for the Node.js 'stream' module
- `crypto-browserify`: Polyfill for the Node.js 'crypto' module
- `buffer`: Polyfill for the Node.js 'buffer' module

These are configured in `next.config.js` and allow the application to use modules that would normally only be available in Node.js.
