// Analytics Service for tracking user behavior and conversions
import { ObjectId } from 'mongodb';
import clientPromise from './mongodb';
import { logActivity } from './user-service';

// Analytics event types
export type AnalyticsEvent = 
  | 'invoice_limit_hit'
  | 'invoice_usage_warning'
  | 'monthly_usage_reset'
  | 'upgrade_modal_shown'
  | 'upgrade_clicked'
  | 'upgrade_completed'
  | 'upgrade_cancelled'
  | 'invoice_created'
  | 'user_signed_up'
  | 'subscription_cancelled'
  | 'billing_failed';

export interface AnalyticsEventData {
  _id?: ObjectId;
  userId: ObjectId;
  sessionId?: string;
  event: AnalyticsEvent;
  properties: Record<string, any>;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
  source?: string; // where the event originated
}

// Get database instance
async function getDatabase() {
  const client = await clientPromise;
  return client.db();
}

// Track an analytics event
export async function trackEvent(
  userId: string | ObjectId,
  event: AnalyticsEvent,
  properties: Record<string, any> = {},
  context?: {
    sessionId?: string;
    userAgent?: string;
    ipAddress?: string;
    source?: string;
  }
): Promise<void> {
  try {
    const db = await getDatabase();
    const analyticsCollection = db.collection<AnalyticsEventData>('analytics_events');
    
    const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    const eventData: AnalyticsEventData = {
      userId: userObjectId,
      event,
      properties,
      timestamp: new Date(),
      sessionId: context?.sessionId,
      userAgent: context?.userAgent,
      ipAddress: context?.ipAddress,
      source: context?.source,
    };

    await analyticsCollection.insertOne(eventData);

    // Also log important events in activity log
    if (['upgrade_completed', 'subscription_cancelled', 'invoice_limit_hit'].includes(event)) {
      await logActivity({
        userId: userObjectId,
        action: event,
        resourceType: 'user',
        details: {
          description: getEventDescription(event),
          metadata: properties,
        },
      });
    }

    console.log(`Analytics: ${event} tracked for user ${userObjectId}`);
  } catch (error) {
    console.error('Error tracking analytics event:', error);
    // Don't throw error - analytics should not break the main flow
  }
}

// Get event description for activity log
function getEventDescription(event: AnalyticsEvent): string {
  switch (event) {
    case 'upgrade_completed':
      return 'User upgraded to Pro plan';
    case 'subscription_cancelled':
      return 'User cancelled Pro subscription';
    case 'invoice_limit_hit':
      return 'User reached invoice limit';
    case 'upgrade_modal_shown':
      return 'Upgrade modal displayed to user';
    default:
      return `User performed action: ${event}`;
  }
}

// Track conversion funnel
export async function trackConversionFunnel(
  userId: string | ObjectId,
  step: 'limit_hit' | 'modal_shown' | 'upgrade_clicked' | 'checkout_started' | 'upgrade_completed',
  properties: Record<string, any> = {}
): Promise<void> {
  const eventMap = {
    limit_hit: 'invoice_limit_hit',
    modal_shown: 'upgrade_modal_shown',
    upgrade_clicked: 'upgrade_clicked',
    checkout_started: 'upgrade_clicked', // Same as upgrade_clicked for now
    upgrade_completed: 'upgrade_completed',
  } as const;

  await trackEvent(userId, eventMap[step], {
    ...properties,
    funnel_step: step,
    timestamp: new Date().toISOString(),
  });
}

// Get conversion metrics
export async function getConversionMetrics(
  startDate?: Date,
  endDate?: Date
): Promise<{
  totalSignups: number;
  limitHits: number;
  modalShown: number;
  upgradeClicks: number;
  upgradeCompletions: number;
  conversionRate: number;
  funnelMetrics: {
    signupToLimitHit: number;
    limitHitToModal: number;
    modalToClick: number;
    clickToCompletion: number;
  };
}> {
  try {
    const db = await getDatabase();
    const analyticsCollection = db.collection<AnalyticsEventData>('analytics_events');
    
    const dateFilter = startDate && endDate ? {
      timestamp: { $gte: startDate, $lte: endDate }
    } : {};

    const [
      totalSignups,
      limitHits,
      modalShown,
      upgradeClicks,
      upgradeCompletions,
    ] = await Promise.all([
      analyticsCollection.countDocuments({ event: 'user_signed_up', ...dateFilter }),
      analyticsCollection.countDocuments({ event: 'invoice_limit_hit', ...dateFilter }),
      analyticsCollection.countDocuments({ event: 'upgrade_modal_shown', ...dateFilter }),
      analyticsCollection.countDocuments({ event: 'upgrade_clicked', ...dateFilter }),
      analyticsCollection.countDocuments({ event: 'upgrade_completed', ...dateFilter }),
    ]);

    const conversionRate = totalSignups > 0 ? (upgradeCompletions / totalSignups) * 100 : 0;

    return {
      totalSignups,
      limitHits,
      modalShown,
      upgradeClicks,
      upgradeCompletions,
      conversionRate,
      funnelMetrics: {
        signupToLimitHit: totalSignups > 0 ? (limitHits / totalSignups) * 100 : 0,
        limitHitToModal: limitHits > 0 ? (modalShown / limitHits) * 100 : 0,
        modalToClick: modalShown > 0 ? (upgradeClicks / modalShown) * 100 : 0,
        clickToCompletion: upgradeClicks > 0 ? (upgradeCompletions / upgradeClicks) * 100 : 0,
      },
    };
  } catch (error) {
    console.error('Error getting conversion metrics:', error);
    return {
      totalSignups: 0,
      limitHits: 0,
      modalShown: 0,
      upgradeClicks: 0,
      upgradeCompletions: 0,
      conversionRate: 0,
      funnelMetrics: {
        signupToLimitHit: 0,
        limitHitToModal: 0,
        modalToClick: 0,
        clickToCompletion: 0,
      },
    };
  }
}

// Get user conversion timeline
export async function getUserConversionTimeline(
  userId: string | ObjectId
): Promise<AnalyticsEventData[]> {
  try {
    const db = await getDatabase();
    const analyticsCollection = db.collection<AnalyticsEventData>('analytics_events');
    
    const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    return await analyticsCollection
      .find({
        userId: userObjectId,
        event: {
          $in: [
            'user_signed_up',
            'invoice_created',
            'invoice_limit_hit',
            'upgrade_modal_shown',
            'upgrade_clicked',
            'upgrade_completed',
            'subscription_cancelled'
          ]
        }
      })
      .sort({ timestamp: 1 })
      .toArray();
  } catch (error) {
    console.error('Error getting user conversion timeline:', error);
    return [];
  }
}

// A/B testing framework
export interface ABTest {
  _id?: ObjectId;
  name: string;
  description: string;
  variants: Array<{
    name: string;
    weight: number; // 0-100
    config: Record<string, any>;
  }>;
  isActive: boolean;
  startDate: Date;
  endDate?: Date;
  targetAudience?: {
    plan?: 'free' | 'pro';
    invoicesUsed?: { min?: number; max?: number };
    signupDate?: { after?: Date; before?: Date };
  };
}

// Get A/B test variant for user
export async function getABTestVariant(
  testName: string,
  userId: string | ObjectId
): Promise<{ variant: string; config: Record<string, any> } | null> {
  try {
    const db = await getDatabase();
    const abTestsCollection = db.collection<ABTest>('ab_tests');
    
    const test = await abTestsCollection.findOne({
      name: testName,
      isActive: true,
      startDate: { $lte: new Date() },
      $or: [
        { endDate: { $exists: false } },
        { endDate: { $gte: new Date() } }
      ]
    });

    if (!test) {
      return null;
    }

    // Simple hash-based assignment for consistent user experience
    const userIdString = typeof userId === 'string' ? userId : userId.toString();
    const hash = simpleHash(userIdString + testName);
    const bucket = hash % 100;

    let currentWeight = 0;
    for (const variant of test.variants) {
      currentWeight += variant.weight;
      if (bucket < currentWeight) {
        // Track assignment
        await trackEvent(userId, 'invoice_limit_hit', {
          ab_test: testName,
          variant: variant.name,
          config: variant.config,
        });
        
        return {
          variant: variant.name,
          config: variant.config,
        };
      }
    }

    // Fallback to first variant
    return {
      variant: test.variants[0].name,
      config: test.variants[0].config,
    };
  } catch (error) {
    console.error('Error getting A/B test variant:', error);
    return null;
  }
}

// Simple hash function for A/B testing
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

// Create or update A/B test
export async function createABTest(test: Omit<ABTest, '_id'>): Promise<ABTest> {
  const db = await getDatabase();
  const abTestsCollection = db.collection<ABTest>('ab_tests');
  
  const result = await abTestsCollection.insertOne(test);
  return { ...test, _id: result.insertedId };
}

// Get A/B test results
export async function getABTestResults(testName: string): Promise<{
  test: ABTest | null;
  results: Array<{
    variant: string;
    participants: number;
    conversions: number;
    conversionRate: number;
  }>;
}> {
  try {
    const db = await getDatabase();
    const abTestsCollection = db.collection<ABTest>('ab_tests');
    const analyticsCollection = db.collection<AnalyticsEventData>('analytics_events');
    
    const test = await abTestsCollection.findOne({ name: testName });
    
    if (!test) {
      return { test: null, results: [] };
    }

    // Get results for each variant
    const results = await Promise.all(
      test.variants.map(async (variant) => {
        const participants = await analyticsCollection.countDocuments({
          'properties.ab_test': testName,
          'properties.variant': variant.name,
        });

        const conversions = await analyticsCollection.countDocuments({
          event: 'upgrade_completed',
          'properties.ab_test': testName,
          'properties.variant': variant.name,
        });

        return {
          variant: variant.name,
          participants,
          conversions,
          conversionRate: participants > 0 ? (conversions / participants) * 100 : 0,
        };
      })
    );

    return { test, results };
  } catch (error) {
    console.error('Error getting A/B test results:', error);
    return { test: null, results: [] };
  }
}