import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Zap, 
  Shield, 
  Users, 
  BarChart3, 
  Cloud, 
  Smartphone,
  Check
} from 'lucide-react';

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  image?: string;
  benefits: string[];
}

export interface FeatureTabsProps {
  features?: Feature[];
  title?: string;
  subtitle?: string;
  className?: string;
}

const defaultFeatures: Feature[] = [
  {
    id: 'automation',
    title: 'Smart Automation',
    description: 'Automate repetitive tasks and workflows to save time and reduce errors. Our AI-powered automation adapts to your business needs.',
    icon: <Zap className="w-6 h-6" />,
    image: 'https://images.unsplash.com/photo-*************-d6fc5c10da5a?w=800&h=600&fit=crop',
    benefits: [
      'Reduce manual work by up to 80%',
      'Eliminate human errors',
      'Scale operations effortlessly',
      '24/7 automated processing',
    ],
  },
  {
    id: 'security',
    title: 'Enterprise Security',
    description: 'Bank-level security with end-to-end encryption, multi-factor authentication, and continuous monitoring to protect your data.',
    icon: <Shield className="w-6 h-6" />,
    image: 'https://images.unsplash.com/photo-*************-322da13575f3?w=800&h=600&fit=crop',
    benefits: [
      '256-bit SSL encryption',
      'SOC 2 Type II certified',
      'GDPR compliant',
      'Regular security audits',
    ],
  },
  {
    id: 'collaboration',
    title: 'Team Collaboration',
    description: 'Work together seamlessly with real-time collaboration tools, shared workspaces, and integrated communication.',
    icon: <Users className="w-6 h-6" />,
    image: 'https://images.unsplash.com/photo-*************-009f0129c71c?w=800&h=600&fit=crop',
    benefits: [
      'Real-time co-editing',
      'Built-in chat and video',
      'Role-based permissions',
      'Activity tracking',
    ],
  },
  {
    id: 'analytics',
    title: 'Advanced Analytics',
    description: 'Get actionable insights with powerful analytics and customizable dashboards that help you make data-driven decisions.',
    icon: <BarChart3 className="w-6 h-6" />,
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop',
    benefits: [
      'Real-time dashboards',
      'Custom report builder',
      'Predictive analytics',
      'Export to any format',
    ],
  },
];

const FeatureTabs: React.FC<FeatureTabsProps> = ({
  features = defaultFeatures,
  title = 'Powerful Features for Modern Teams',
  subtitle = 'Everything you need to run your business efficiently',
  className,
}) => {
  const [activeFeature, setActiveFeature] = useState(features[0]);

  return (
    <section className={cn('py-16 lg:py-24 bg-gray-50', className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && (
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {subtitle}
              </p>
            )}
          </div>
        )}

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Tabs */}
          <div className="lg:col-span-1">
            <div className="space-y-2">
              {features.map((feature) => (
                <motion.button
                  key={feature.id}
                  onClick={() => setActiveFeature(feature)}
                  className={cn(
                    'w-full text-left p-4 rounded-lg transition-all duration-200',
                    activeFeature.id === feature.id
                      ? 'bg-white shadow-lg'
                      : 'hover:bg-white/50'
                  )}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-start space-x-3">
                    <div
                      className={cn(
                        'p-2 rounded-lg transition-colors',
                        activeFeature.id === feature.id
                          ? 'bg-primary text-white'
                          : 'bg-gray-200 text-gray-600'
                      )}
                    >
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {feature.title}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeFeature.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="bg-white rounded-xl shadow-xl overflow-hidden"
              >
                {activeFeature.image && (
                  <div className="aspect-video relative overflow-hidden">
                    <img
                      src={activeFeature.image}
                      alt={activeFeature.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  </div>
                )}
                
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    {activeFeature.title}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {activeFeature.description}
                  </p>

                  <div className="space-y-3">
                    <h4 className="font-semibold text-gray-900">
                      Key Benefits:
                    </h4>
                    {activeFeature.benefits.map((benefit, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-start space-x-3"
                      >
                        <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{benefit}</span>
                      </motion.div>
                    ))}
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="mt-8 inline-flex items-center px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Learn More
                    <svg
                      className="ml-2 w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </motion.button>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>

        {/* Bottom CTAs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-4"
        >
          <div className="flex items-center text-gray-600">
            <Cloud className="w-5 h-5 mr-2" />
            <span>Cloud-based solution</span>
          </div>
          <div className="flex items-center text-gray-600">
            <Smartphone className="w-5 h-5 mr-2" />
            <span>Mobile responsive</span>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export { FeatureTabs };