import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { 
  CheckCircle, 
  ArrowRight, 
  Star, 
  Clock, 
  DollarSign, 
  FileText,
  Zap,
  Shield,
  Users
} from 'lucide-react'
import { industrySEO } from '@/lib/seo-config'
import { BreadcrumbSchema, FAQSchema, ProductSchema } from '@/components/seo/JsonLd'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'

export const metadata: Metadata = {
  title: industrySEO.freelancer.title,
  description: industrySEO.freelancer.description,
  keywords: industrySEO.freelancer.keywords,
  alternates: {
    canonical: industrySEO.freelancer.canonical
  },
  openGraph: {
    title: industrySEO.freelancer.title,
    description: industrySEO.freelancer.description,
    url: industrySEO.freelancer.canonical,
  }
}

const faqItems = [
  {
    question: "What makes a good freelancer invoice template?",
    answer: "A good freelancer invoice template should include your business information, client details, project description, itemized services with rates, payment terms, and a professional design that reflects your brand. It should also be easy to customize for different clients and projects."
  },
  {
    question: "How do I calculate my freelance rates for invoicing?",
    answer: "Calculate your freelance rates by considering your experience level, industry standards, project complexity, and desired annual income. Factor in business expenses, taxes, and non-billable time. Many freelancers use hourly rates between $50-$150 or project-based pricing."
  },
  {
    question: "What payment terms should freelancers use?",
    answer: "Common payment terms for freelancers include Net 15 or Net 30 days, with many requiring a 25-50% deposit upfront. Consider offering early payment discounts (2/10 Net 30) and clearly state late payment fees to encourage timely payments."
  },
  {
    question: "Can I send invoices directly from this tool?",
    answer: "Yes! Our invoice generator allows you to send professional invoices directly to clients via email. You can also download PDFs for your records or share secure links for online viewing and payment."
  },
  {
    question: "Is this invoice generator really free for freelancers?",
    answer: "Yes, freelancers can create up to 5 invoices per month absolutely free. No credit card required. For unlimited invoices and premium features like automatic reminders and payment tracking, affordable upgrade options are available."
  }
]

export default function FreelancerInvoiceTemplatePage() {
  return (
    <>
      {/* Breadcrumb Schema */}
      <BreadcrumbSchema 
        items={[
          { name: 'Home', url: '/' },
          { name: 'Templates', url: '/templates' },
          { name: 'Freelancer Invoice Template', url: '/invoice-template/freelancer' }
        ]}
      />
      
      {/* Product Schema for the template */}
      <ProductSchema
        name="Freelancer Invoice Template"
        description="Professional invoice template designed specifically for freelancers, consultants, and independent contractors. Create beautiful invoices in minutes."
        category="Business Software"
        offers={{
          price: '0',
          priceCurrency: 'USD',
          availability: 'https://schema.org/InStock'
        }}
        aggregateRating={{
          ratingValue: '4.9',
          reviewCount: '2847'
        }}
      />
      
      {/* FAQ Schema */}
      <FAQSchema items={faqItems} />
      
      <main className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
        {/* Hero Section */}
        <section className="relative py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                  Freelancer Invoice Template | Professional & Free
                </h1>
                <p className="text-xl text-gray-600 mb-8">
                  Create professional invoices that get you paid faster. Our freelancer invoice template is designed specifically for independent professionals, consultants, and contractors who need to bill clients efficiently.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 mb-8">
                  <Link href="/create-invoice?template=freelancer">
                    <Button size="lg" className="w-full sm:w-auto">
                      Use This Template Free
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                  <Link href="/templates">
                    <Button variant="outline" size="lg" className="w-full sm:w-auto">
                      View All Templates
                    </Button>
                  </Link>
                </div>
                
                {/* Trust Signals */}
                <div className="flex items-center gap-6 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-500 fill-current" />
                    <span>4.9/5 (2,847 reviews)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    <span>50K+ freelancers</span>
                  </div>
                </div>
              </div>
              
              {/* Template Preview */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 transform rotate-3 rounded-lg opacity-10"></div>
                <Card className="relative bg-white p-8 shadow-xl">
                  <div className="space-y-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900">INVOICE</h2>
                        <p className="text-gray-600">#INV-2024-001</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">Your Business Name</p>
                        <p className="text-sm text-gray-600"><EMAIL></p>
                      </div>
                    </div>
                    
                    <div className="border-t pt-4">
                      <p className="font-semibold mb-2">Bill To:</p>
                      <p className="text-gray-600">Client Company Name</p>
                      <p className="text-sm text-gray-600"><EMAIL></p>
                    </div>
                    
                    <div className="border-t pt-4">
                      <table className="w-full">
                        <thead>
                          <tr className="text-left text-sm text-gray-600">
                            <th className="pb-2">Description</th>
                            <th className="pb-2 text-right">Hours</th>
                            <th className="pb-2 text-right">Rate</th>
                            <th className="pb-2 text-right">Amount</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td className="py-2">Website Development</td>
                            <td className="py-2 text-right">40</td>
                            <td className="py-2 text-right">$75</td>
                            <td className="py-2 text-right font-semibold">$3,000</td>
                          </tr>
                          <tr>
                            <td className="py-2">UI/UX Design</td>
                            <td className="py-2 text-right">20</td>
                            <td className="py-2 text-right">$85</td>
                            <td className="py-2 text-right font-semibold">$1,700</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    
                    <div className="border-t pt-4 text-right">
                      <p className="text-2xl font-bold text-blue-600">Total: $4,700</p>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </section>
        
        {/* Benefits Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Why Freelancers Choose Our Invoice Template
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              <Card className="p-6">
                <Clock className="h-12 w-12 text-blue-600 mb-4" />
                <h3 className="text-xl font-semibold mb-2">Save Time</h3>
                <p className="text-gray-600">
                  Create professional invoices in under 2 minutes. No more wrestling with Word docs or Excel sheets. Focus on your work, not paperwork.
                </p>
              </Card>
              
              <Card className="p-6">
                <DollarSign className="h-12 w-12 text-green-600 mb-4" />
                <h3 className="text-xl font-semibold mb-2">Get Paid Faster</h3>
                <p className="text-gray-600">
                  Professional invoices with clear payment terms get paid 2x faster. Include payment links and automatic reminders to reduce late payments.
                </p>
              </Card>
              
              <Card className="p-6">
                <Shield className="h-12 w-12 text-purple-600 mb-4" />
                <h3 className="text-xl font-semibold mb-2">Look Professional</h3>
                <p className="text-gray-600">
                  Impress clients with polished, branded invoices that reflect your professionalism. Build trust and credibility with every invoice.
                </p>
              </Card>
            </div>
          </div>
        </section>
        
        {/* Features Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Features Designed for Freelance Success
            </h2>
            
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6">
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-green-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold mb-1">Customizable Templates</h3>
                    <p className="text-gray-600">Add your logo, choose colors, and customize fields to match your brand identity.</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-green-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold mb-1">Automatic Calculations</h3>
                    <p className="text-gray-600">No more math errors. Automatically calculate totals, taxes, and discounts.</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-green-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold mb-1">Multiple Currency Support</h3>
                    <p className="text-gray-600">Bill clients worldwide with support for all major currencies.</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-green-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold mb-1">Payment Tracking</h3>
                    <p className="text-gray-600">Track invoice status, payments, and send automatic reminders.</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-green-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold mb-1">Client Management</h3>
                    <p className="text-gray-600">Save client details for quick invoice creation in the future.</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <CheckCircle className="h-6 w-6 text-green-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold mb-1">Secure & Private</h3>
                    <p className="text-gray-600">Your data is encrypted and secure. We never share your information.</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-8">
                <h3 className="text-2xl font-bold mb-6">Perfect for:</h3>
                <div className="space-y-3">
                  <p className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Web Developers & Programmers
                  </p>
                  <p className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Graphic & UI/UX Designers
                  </p>
                  <p className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Content Writers & Copywriters
                  </p>
                  <p className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Marketing Consultants
                  </p>
                  <p className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Virtual Assistants
                  </p>
                  <p className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    Any Independent Professional
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* How It Works */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Create Your First Invoice in 3 Simple Steps
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">1</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Choose Template</h3>
                <p className="text-gray-600">
                  Select the freelancer template or browse other professional designs
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">2</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Add Your Details</h3>
                <p className="text-gray-600">
                  Fill in your business info, client details, and services provided
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">3</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Send & Get Paid</h3>
                <p className="text-gray-600">
                  Email directly to clients or download PDF. Track payments easily
                </p>
              </div>
            </div>
            
            <div className="text-center mt-12">
              <Link href="/create-invoice?template=freelancer">
                <Button size="lg">
                  Start Creating Invoices Free
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </section>
        
        {/* FAQ Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Frequently Asked Questions
            </h2>
            
            <div className="space-y-8">
              {faqItems.map((item, index) => (
                <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                  <h3 className="text-lg font-semibold mb-2">{item.question}</h3>
                  <p className="text-gray-600">{item.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
        
        {/* CTA Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Streamline Your Freelance Invoicing?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of freelancers who save hours on invoicing and get paid faster.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/create-invoice?template=freelancer">
                <Button size="lg" variant="secondary">
                  Create Your First Invoice
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/templates">
                <Button size="lg" variant="outline" className="bg-white/10 text-white border-white hover:bg-white/20">
                  Explore More Templates
                </Button>
              </Link>
            </div>
            
            <p className="text-sm text-blue-100 mt-6">
              No credit card required • Free forever plan available
            </p>
          </div>
        </section>
        
        {/* Related Templates */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              Related Invoice Templates
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              <Link href="/invoice-template/consulting">
                <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
                  <FileText className="h-12 w-12 text-blue-600 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Consulting Invoice</h3>
                  <p className="text-gray-600">
                    Professional template for consultants and advisors
                  </p>
                </Card>
              </Link>
              
              <Link href="/invoice-template/web-development">
                <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
                  <FileText className="h-12 w-12 text-purple-600 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Web Development Invoice</h3>
                  <p className="text-gray-600">
                    Detailed template for developers and programmers
                  </p>
                </Card>
              </Link>
              
              <Link href="/invoice-template/graphic-design">
                <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
                  <FileText className="h-12 w-12 text-green-600 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Design Invoice</h3>
                  <p className="text-gray-600">
                    Creative template for designers and artists
                  </p>
                </Card>
              </Link>
            </div>
          </div>
        </section>
      </main>
    </>
  )
}