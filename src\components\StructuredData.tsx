'use client';

import { siteConfig } from '@/lib/seo-config';

interface StructuredDataProps {
  type?: 'homepage' | 'template' | 'industry' | 'faq';
  pageData?: {
    title?: string;
    description?: string;
    industry?: string;
    templateName?: string;
  };
}

export default function StructuredData({ type = 'homepage', pageData }: StructuredDataProps) {
  const organizationSchema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Template Invoices',
    url: siteConfig.url,
    logo: `${siteConfig.url}/images/logo/logo.png`,
    description: 'Professional invoice generator with AI-powered templates for freelancers and small businesses',
    foundingDate: '2024',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'US',
      addressRegion: 'United States'
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-123-4567',
      contactType: 'customer service',
      areaServed: 'Worldwide',
      availableLanguage: ['English'],
      hoursAvailable: {
        '@type': 'OpeningHoursSpecification',
        dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        opens: '09:00',
        closes: '17:00',
        timeZone: 'America/New_York'
      }
    },
    sameAs: [
      'https://github.com/templateinvoices'
    ],
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '1250',
      bestRating: '5',
      worstRating: '1'
    }
  };

  const webApplicationSchema = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: 'Template Invoices - Free Invoice Generator',
    applicationCategory: 'BusinessApplication',
    operatingSystem: 'Any',
    description: 'Free online invoice generator with professional AI-powered templates for freelancers, small businesses, and contractors',
    url: siteConfig.url,
    creator: {
      '@type': 'Organization',
      name: 'Template Invoices'
    },
    datePublished: '2024-01-01',
    dateModified: new Date().toISOString().split('T')[0],
    version: '2.0',
    browserRequirements: 'Requires JavaScript. Supports Chrome, Firefox, Safari, Edge.',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      description: 'Free invoice generation with premium options available',
      availability: 'https://schema.org/InStock',
      validFrom: '2024-01-01'
    },
    featureList: [
      'AI-powered invoice templates',
      'Professional PDF generation',
      'Email integration and sending',
      'Multiple currency support',
      'Industry-specific designs',
      'Real-time invoice preview',
      'Client management system',
      'Payment tracking',
      'Tax calculations',
      'Mobile-responsive design'
    ],
    screenshot: `${siteConfig.url}/screenshots/invoice-generator.png`,
    softwareVersion: '2.0',
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '1250',
      bestRating: '5',
      worstRating: '1'
    }
  };

  const faqSchema = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: [
      {
        '@type': 'Question',
        name: 'How do I create a professional invoice for free?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Simply click "Create Free Invoice" above, enter your business and client details, add your services or products, and download your professional PDF invoice instantly. No registration required for basic invoices.'
        }
      },
      {
        '@type': 'Question',
        name: 'What invoice templates are available for freelancers?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'We offer specialized templates for freelance writers, web developers, graphic designers, photographers, consultants, and more. Each template is optimized for your specific industry and includes relevant fields and professional formatting.'
        }
      },
      {
        '@type': 'Question',
        name: 'Can I send invoices directly to clients?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes! You can email invoices directly to clients from our platform, or download the PDF and send it yourself. All invoices include professional formatting and your business branding.'
        }
      },
      {
        '@type': 'Question',
        name: 'Is this invoice generator really free?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes, our basic invoice generator is completely free. You can create unlimited invoices with our standard templates. Premium features like advanced customization and client management are available with our Pro plan.'
        }
      },
      {
        '@type': 'Question',
        name: 'What makes a professional invoice?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'A professional invoice includes your business information, client details, unique invoice number, clear itemization of services, payment terms, and total amount due. Our templates automatically format all these elements for a polished, professional look.'
        }
      },
      {
        '@type': 'Question',
        name: 'Can I customize the invoice templates?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Absolutely! All our templates are fully customizable. You can add your logo, change colors, modify fields, and adjust the layout to match your brand identity.'
        }
      },
      {
        '@type': 'Question',
        name: 'What file formats can I download invoices in?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'All invoices can be downloaded as high-quality PDF files, perfect for printing, emailing, or digital storage. PDFs maintain formatting across all devices and platforms.'
        }
      },
      {
        '@type': 'Question',
        name: 'Do you support multiple currencies?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes, our invoice generator supports major currencies including USD, EUR, GBP, CAD, AUD, and many others. Perfect for international freelancers and businesses.'
        }
      }
    ]
  };

  const serviceSchema = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: 'Professional Invoice Generation Service',
    description: 'AI-powered invoice creation service with industry-specific templates',
    provider: {
      '@type': 'Organization',
      name: 'Template Invoices'
    },
    areaServed: 'Worldwide',
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: 'Invoice Templates',
      itemListElement: [
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Freelancer Invoice Templates'
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Photography Invoice Templates'
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Consulting Invoice Templates'
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Developer Invoice Templates'
          }
        }
      ]
    }
  };

  // Industry-specific schema for landing pages
  const getIndustrySchema = (industry: string) => {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: `${industry} Invoice Generator - Professional Templates`,
      description: `Create professional ${industry.toLowerCase()} invoices with specialized templates designed for ${industry.toLowerCase()} professionals.`,
      url: `${siteConfig.url}/invoice-generator/${industry.toLowerCase()}`,
      mainEntity: {
        '@type': 'SoftwareApplication',
        name: `${industry} Invoice Generator`,
        applicationCategory: 'BusinessApplication',
        description: `Specialized invoice generator for ${industry.toLowerCase()} professionals`,
        operatingSystem: 'Any',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD'
        }
      },
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Home',
            item: siteConfig.url
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: 'Invoice Generator',
            item: `${siteConfig.url}/invoice-generator`
          },
          {
            '@type': 'ListItem',
            position: 3,
            name: industry,
            item: `${siteConfig.url}/invoice-generator/${industry.toLowerCase()}`
          }
        ]
      }
    };
  };

  // Template-specific schema
  const getTemplateSchema = (templateName: string) => {
    return {
      '@context': 'https://schema.org',
      '@type': 'CreativeWork',
      name: `${templateName} Invoice Template`,
      description: `Professional ${templateName.toLowerCase()} invoice template with modern design and industry-specific features.`,
      creator: {
        '@type': 'Organization',
        name: 'Template Invoices'
      },
      datePublished: '2024-01-01',
      genre: 'Business Template',
      inLanguage: 'en-US',
      isAccessibleForFree: true,
      license: 'https://creativecommons.org/licenses/by/4.0/',
      audience: {
        '@type': 'Audience',
        audienceType: 'Business Professionals'
      }
    };
  };

  // Render schemas based on page type
  const renderSchemas = () => {
    const schemas: any[] = [organizationSchema, webApplicationSchema, serviceSchema];
    
    if (type === 'faq' || type === 'homepage') {
      schemas.push(faqSchema);
    }
    
    if (type === 'industry' && pageData?.industry) {
      schemas.push(getIndustrySchema(pageData.industry));
    }
    
    if (type === 'template' && pageData?.templateName) {
      schemas.push(getTemplateSchema(pageData.templateName));
    }

    return schemas.map((schema, index) => (
      <script
        key={index}
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
      />
    ));
  };

  return <>{renderSchemas()}</>;
}