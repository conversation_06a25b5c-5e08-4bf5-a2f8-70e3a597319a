import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/mongodb';
import { COLLECTIONS, InvoiceDocument, ClientDocument } from '@/lib/models';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'month'; // month, year, all
    
    const client = await clientPromise;
    const db = client.db();
    const invoicesCollection = db.collection<InvoiceDocument>(COLLECTIONS.INVOICES);
    const clientsCollection = db.collection<ClientDocument>(COLLECTIONS.CLIENTS);
    
    const userId = new ObjectId(session.user.id);
    
    // Calculate date ranges
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    
    let startOfMonth = new Date(currentYear, currentMonth, 1);
    let startOfYear = new Date(currentYear, 0, 1);
    let startOfLastMonth = new Date(currentYear, currentMonth - 1, 1);
    let endOfLastMonth = new Date(currentYear, currentMonth, 0);
    let startOfLastYear = new Date(currentYear - 1, 0, 1);
    let endOfLastYear = new Date(currentYear - 1, 11, 31);

    // Get all invoices for the user
    const allInvoices = await invoicesCollection.find({ userId }).toArray();
    
    // Get all clients for the user
    const allClients = await clientsCollection.find({ userId }).toArray();

    // OVERVIEW METRICS
    const thisMonthInvoices = allInvoices.filter(inv => inv.createdAt >= startOfMonth);
    const thisYearInvoices = allInvoices.filter(inv => inv.createdAt >= startOfYear);
    const lastMonthInvoices = allInvoices.filter(inv => 
      inv.createdAt >= startOfLastMonth && inv.createdAt <= endOfLastMonth
    );
    const lastYearInvoices = allInvoices.filter(inv => 
      inv.createdAt >= startOfLastYear && inv.createdAt <= endOfLastYear
    );

    const overviewMetrics = {
      totalInvoices: {
        thisMonth: thisMonthInvoices.length,
        thisYear: thisYearInvoices.length,
        lastMonth: lastMonthInvoices.length,
        lastYear: lastYearInvoices.length,
        allTime: allInvoices.length,
      },
      totalRevenue: {
        thisMonth: thisMonthInvoices.reduce((sum, inv) => sum + inv.totals.total, 0),
        thisYear: thisYearInvoices.reduce((sum, inv) => sum + inv.totals.total, 0),
        lastMonth: lastMonthInvoices.reduce((sum, inv) => sum + inv.totals.total, 0),
        lastYear: lastYearInvoices.reduce((sum, inv) => sum + inv.totals.total, 0),
        allTime: allInvoices.reduce((sum, inv) => sum + inv.totals.total, 0),
      },
      averageInvoiceAmount: {
        thisMonth: thisMonthInvoices.length > 0 ? 
          thisMonthInvoices.reduce((sum, inv) => sum + inv.totals.total, 0) / thisMonthInvoices.length : 0,
        thisYear: thisYearInvoices.length > 0 ? 
          thisYearInvoices.reduce((sum, inv) => sum + inv.totals.total, 0) / thisYearInvoices.length : 0,
        allTime: allInvoices.length > 0 ? 
          allInvoices.reduce((sum, inv) => sum + inv.totals.total, 0) / allInvoices.length : 0,
      },
      paymentTimeline: calculatePaymentTimeline(allInvoices),
    };

    // RECENT INVOICES (last 10)
    const recentInvoices = allInvoices
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 10)
      .map(inv => ({
        _id: inv._id,
        invoiceNumber: inv.invoiceNumber,
        clientName: inv.clientInfo.name,
        total: inv.totals.total,
        currency: inv.currency,
        status: inv.status,
        createdAt: inv.createdAt,
        dueDate: inv.dueDate,
        isOverdue: inv.status !== 'paid' && new Date(inv.dueDate) < now,
      }));

    // CLIENT INSIGHTS
    const clientInsights = generateClientInsights(allInvoices, allClients);

    // BUSINESS INTELLIGENCE
    const businessIntelligence = generateBusinessIntelligence(allInvoices, currentYear);

    // STATUS BREAKDOWN
    const statusBreakdown = {
      draft: allInvoices.filter(inv => inv.status === 'draft').length,
      sent: allInvoices.filter(inv => inv.status === 'sent').length,
      paid: allInvoices.filter(inv => inv.status === 'paid').length,
      overdue: allInvoices.filter(inv => 
        inv.status !== 'paid' && new Date(inv.dueDate) < now
      ).length,
    };

    // AI INSIGHTS (basic for now)
    const aiInsights = generateAIInsights(allInvoices, allClients, overviewMetrics);

    const dashboardData = {
      overview: overviewMetrics,
      recentInvoices,
      clientInsights,
      businessIntelligence,
      statusBreakdown,
      aiInsights,
      summary: {
        totalClients: allClients.length,
        activeClients: allClients.filter(c => c.status === 'active').length,
        overdueAmount: allInvoices
          .filter(inv => inv.status !== 'paid' && new Date(inv.dueDate) < now)
          .reduce((sum, inv) => sum + inv.totals.total, 0),
        pendingInvoices: allInvoices.filter(inv => inv.status === 'sent').length,
      }
    };

    return NextResponse.json(dashboardData);

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}

// Helper function to calculate payment timeline metrics
function calculatePaymentTimeline(invoices: InvoiceDocument[]) {
  const paidInvoices = invoices.filter(inv => inv.status === 'paid' && inv.paidAt && inv.sentAt);
  
  if (paidInvoices.length === 0) {
    return { averageDaysToPay: 0, fastestPayment: 0, slowestPayment: 0 };
  }

  const paymentDays = paidInvoices.map(inv => {
    const sentDate = new Date(inv.sentAt!);
    const paidDate = new Date(inv.paidAt!);
    return Math.ceil((paidDate.getTime() - sentDate.getTime()) / (1000 * 60 * 60 * 24));
  });

  return {
    averageDaysToPay: Math.round(paymentDays.reduce((sum, days) => sum + days, 0) / paymentDays.length),
    fastestPayment: Math.min(...paymentDays),
    slowestPayment: Math.max(...paymentDays),
  };
}

// Helper function to generate client insights
function generateClientInsights(invoices: InvoiceDocument[], clients: ClientDocument[]) {
  // Top clients by revenue
  const clientRevenue = new Map<string, { name: string; total: number; invoiceCount: number }>();
  
  invoices.forEach(inv => {
    const clientName = inv.clientInfo.name;
    const existing = clientRevenue.get(clientName) || { name: clientName, total: 0, invoiceCount: 0 };
    existing.total += inv.totals.total;
    existing.invoiceCount += 1;
    clientRevenue.set(clientName, existing);
  });

  const topClients = Array.from(clientRevenue.values())
    .sort((a, b) => b.total - a.total)
    .slice(0, 5);

  // New vs returning clients this month
  const thisMonth = new Date();
  thisMonth.setDate(1);
  
  const thisMonthInvoices = invoices.filter(inv => inv.createdAt >= thisMonth);
  const newClientsThisMonth = new Set();
  const returningClientsThisMonth = new Set();
  
  thisMonthInvoices.forEach(inv => {
    const clientName = inv.clientInfo.name;
    const clientHistory = invoices.filter(i => i.clientInfo.name === clientName);
    
    if (clientHistory.length === 1) {
      newClientsThisMonth.add(clientName);
    } else {
      returningClientsThisMonth.add(clientName);
    }
  });

  // Overdue invoices
  const now = new Date();
  const overdueInvoices = invoices.filter(inv => 
    inv.status !== 'paid' && new Date(inv.dueDate) < now
  );

  return {
    topClients,
    newClientsThisMonth: newClientsThisMonth.size,
    returningClientsThisMonth: returningClientsThisMonth.size,
    overdueInvoices: overdueInvoices.map(inv => ({
      invoiceNumber: inv.invoiceNumber,
      clientName: inv.clientInfo.name,
      total: inv.totals.total,
      dueDate: inv.dueDate,
      daysPastDue: Math.ceil((now.getTime() - new Date(inv.dueDate).getTime()) / (1000 * 60 * 60 * 24)),
    })),
  };
}

// Helper function to generate business intelligence
function generateBusinessIntelligence(invoices: InvoiceDocument[], currentYear: number) {
  // Monthly revenue trend
  const monthlyRevenue = Array.from({ length: 12 }, (_, i) => {
    const month = i;
    const monthInvoices = invoices.filter(inv => {
      const invDate = new Date(inv.createdAt);
      return invDate.getFullYear() === currentYear && invDate.getMonth() === month;
    });
    
    return {
      month: new Date(currentYear, month, 1).toLocaleDateString('en-US', { month: 'short' }),
      revenue: monthInvoices.reduce((sum, inv) => sum + inv.totals.total, 0),
      invoiceCount: monthInvoices.length,
    };
  });

  // Service analysis (from line items)
  const serviceRevenue = new Map<string, number>();
  invoices.forEach(inv => {
    inv.lineItems.forEach(item => {
      const existing = serviceRevenue.get(item.description) || 0;
      serviceRevenue.set(item.description, existing + item.amount);
    });
  });

  const topServices = Array.from(serviceRevenue.entries())
    .map(([service, revenue]) => ({ service, revenue }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  return {
    monthlyRevenue,
    topServices,
    seasonalPatterns: calculateSeasonalPatterns(invoices, currentYear),
  };
}

// Helper function to calculate seasonal patterns
function calculateSeasonalPatterns(invoices: InvoiceDocument[], currentYear: number) {
  const quarters = {
    Q1: { months: [0, 1, 2], revenue: 0, invoices: 0 },
    Q2: { months: [3, 4, 5], revenue: 0, invoices: 0 },
    Q3: { months: [6, 7, 8], revenue: 0, invoices: 0 },
    Q4: { months: [9, 10, 11], revenue: 0, invoices: 0 },
  };

  invoices.forEach(inv => {
    const invDate = new Date(inv.createdAt);
    if (invDate.getFullYear() === currentYear) {
      const month = invDate.getMonth();
      
      Object.entries(quarters).forEach(([quarter, data]) => {
        if (data.months.includes(month)) {
          data.revenue += inv.totals.total;
          data.invoices += 1;
        }
      });
    }
  });

  return Object.entries(quarters).map(([quarter, data]) => ({
    quarter,
    revenue: data.revenue,
    invoices: data.invoices,
    averageInvoice: data.invoices > 0 ? data.revenue / data.invoices : 0,
  }));
}

// Helper function to generate AI insights
function generateAIInsights(
  invoices: InvoiceDocument[], 
  clients: ClientDocument[], 
  metrics: any
) {
  const insights = [];

  // Revenue opportunity insights
  if (metrics.totalRevenue.thisMonth < metrics.totalRevenue.lastMonth * 0.8) {
    insights.push({
      type: 'revenue_opportunity',
      priority: 'high',
      title: 'Revenue Down This Month',
      message: 'Revenue is down compared to last month. Consider reaching out to past clients or offering promotions.',
      actionable: true,
      action: 'Contact recent clients for new projects',
    });
  }

  // Client retention insights
  const activeClients = clients.filter(c => c.status === 'active').length;
  const totalClients = clients.length;
  
  if (activeClients / totalClients < 0.7 && totalClients > 5) {
    insights.push({
      type: 'client_retention',
      priority: 'medium',
      title: 'Client Retention Opportunity',
      message: 'Many clients haven\'t been invoiced recently. Consider a follow-up campaign.',
      actionable: true,
      action: 'Create follow-up campaign for inactive clients',
    });
  }

  // Pricing insights
  if (metrics.averageInvoiceAmount.thisMonth > 0 && metrics.averageInvoiceAmount.lastMonth > 0) {
    const pricingTrend = (metrics.averageInvoiceAmount.thisMonth - metrics.averageInvoiceAmount.lastMonth) / metrics.averageInvoiceAmount.lastMonth;
    
    if (pricingTrend < -0.1) {
      insights.push({
        type: 'pricing_optimization',
        priority: 'medium',
        title: 'Average Invoice Amount Declining',
        message: 'Your average invoice amount has decreased. Consider reviewing your pricing strategy.',
        actionable: true,
        action: 'Review and potentially increase service rates',
      });
    }
  }

  // Payment delay predictions
  const recentInvoices = invoices.filter(inv => {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    return inv.createdAt >= oneMonthAgo && inv.status === 'sent';
  });

  if (recentInvoices.length > 3) {
    insights.push({
      type: 'payment_prediction',
      priority: 'low',
      title: 'Payment Follow-up Reminder',
      message: `You have ${recentInvoices.length} invoices awaiting payment. Consider sending follow-up reminders.`,
      actionable: true,
      action: 'Send payment reminders for pending invoices',
    });
  }

  return insights;
}
