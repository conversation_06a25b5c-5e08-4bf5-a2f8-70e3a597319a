import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getConversionMetrics, getABTestResults } from '@/lib/analytics-service';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // For now, only allow admin users to view analytics
    // You can implement proper admin checking here
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') 
      ? new Date(searchParams.get('startDate')!) 
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Last 30 days
    
    const endDate = searchParams.get('endDate') 
      ? new Date(searchParams.get('endDate')!) 
      : new Date();

    // Get conversion metrics
    const conversionMetrics = await getConversionMetrics(startDate, endDate);

    // Get A/B test results (if any tests are running)
    const upgradeModalTest = await getABTestResults('upgrade_modal_messaging');

    const analyticsData = {
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
      conversion: conversionMetrics,
      abTests: {
        upgradeModalTest,
      },
      summary: {
        totalUsers: conversionMetrics.totalSignups,
        upgradeRate: conversionMetrics.conversionRate,
        funnelHealthScore: calculateFunnelHealthScore(conversionMetrics.funnelMetrics),
      },
    };

    return NextResponse.json(analyticsData);

  } catch (error) {
    console.error('Error fetching analytics dashboard:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}

// Calculate a simple health score for the conversion funnel
function calculateFunnelHealthScore(funnelMetrics: any): number {
  const {
    signupToLimitHit,
    limitHitToModal,
    modalToClick,
    clickToCompletion,
  } = funnelMetrics;

  // Weight different stages of the funnel
  const weights = {
    limitHit: 0.2,     // 20% - users hitting limits
    modalShown: 0.25,  // 25% - modal being shown
    clicks: 0.3,       // 30% - users clicking upgrade
    completion: 0.25,  // 25% - completing upgrade
  };

  const score = 
    (signupToLimitHit * weights.limitHit) +
    (limitHitToModal * weights.modalShown) +
    (modalToClick * weights.clicks) +
    (clickToCompletion * weights.completion);

  return Math.round(score);
}