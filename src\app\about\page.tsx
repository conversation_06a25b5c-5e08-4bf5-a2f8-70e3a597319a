import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <Link 
            href="/" 
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">About Template Invoice</h1>
          
          <div className="prose prose-gray max-w-none">
            <p className="text-lg text-gray-600 mb-6">
              Template Invoice is a modern, professional invoice generation platform designed to help businesses, 
              freelancers, and consultants create beautiful invoices quickly and efficiently.
            </p>
            
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Our Mission</h2>
            <p className="text-gray-600 mb-6">
              We believe that creating professional invoices shouldn't be complicated or time-consuming. 
              Our platform provides intuitive tools and beautiful templates that make invoicing simple and elegant.
            </p>
            
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Features</h2>
            <ul className="list-disc list-inside text-gray-600 mb-6 space-y-2">
              <li>Professional invoice templates for every industry</li>
              <li>AI-powered invoice customization</li>
              <li>Quick PDF generation and email sending</li>
              <li>Secure payment processing integration</li>
              <li>Client and invoice management</li>
              <li>Mobile-responsive design</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Get Started</h2>
            <p className="text-gray-600 mb-6">
              Ready to create your first professional invoice? Get started today with our free plan.
            </p>
            
            <div className="flex space-x-4">
              <Link 
                href="/create" 
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Create Invoice
              </Link>
              <Link 
                href="/templates" 
                className="bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                View Templates
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}