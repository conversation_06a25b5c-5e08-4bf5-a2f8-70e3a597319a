'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'

export default function HeroSection() {
  return (
    <section className="relative min-h-[70vh] flex items-center justify-center bg-cover bg-center overflow-hidden" style={{ backgroundImage: "url('/hero-bg.jpg')" }}>
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm z-0" />
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.9, ease: "easeOut" }}
        className="relative z-10 max-w-2xl text-center px-8 py-16 rounded-2xl bg-white/30 backdrop-blur-2xl shadow-xl mx-auto"
      >
        <h1 className="text-5xl sm:text-6xl font-extrabold tracking-tight mb-6 text-white drop-shadow-lg">
          Professional Invoices, Unforgettable Design
        </h1>
        <p className="text-lg sm:text-xl mb-8 text-white/90">
          Create, send, and track invoices with style. Modern templates for every industry. Fast payments. No headaches.
        </p>
        <Link
          href="/create"
          className="inline-block px-8 py-3 bg-black/80 text-white font-semibold rounded-xl shadow-md transition-all duration-300 hover:bg-black hover:scale-105 transform"
        >
          Create Invoice Free
        </Link>
      </motion.div>
    </section>
  )
}