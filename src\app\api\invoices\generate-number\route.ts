import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { generateInvoiceNumber } from '@/lib/invoice-service';

/**
 * API Route: Generate unique invoice number
 * GET /api/invoices/generate-number
 * 
 * Generates a unique invoice number for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Generate unique invoice number
    const invoiceNumber = await generateInvoiceNumber(session.user.id);
    
    return NextResponse.json({ invoiceNumber });

  } catch (error) {
    console.error('Error generating invoice number:', error);
    return NextResponse.json(
      { error: 'Failed to generate invoice number' },
      { status: 500 }
    );
  }
}