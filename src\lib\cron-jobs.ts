/**
 * Cron Jobs for Template Invoice System
 * 
 * This file contains scheduled tasks that should run periodically.
 * In production, these should be scheduled using a cron service like:
 * - Vercel Cron Jobs
 * - AWS EventBridge
 * - Google Cloud Scheduler
 * - Traditional cron
 */

import { processMonthlyResets } from './invoice-limits';

/**
 * Monthly Invoice Usage Reset Job
 * 
 * Schedule: Run daily at 00:00 UTC
 * Cron Expression: 0 0 * * *
 * 
 * This job processes all users whose invoice usage should be reset
 * based on their signup anniversary date.
 */
export async function runMonthlyResetJob(): Promise<{
  success: boolean;
  processed: number;
  errors: number;
  executionTime: number;
}> {
  const startTime = Date.now();
  
  try {
    console.log('[CRON] Starting monthly invoice usage reset job...');
    
    const result = await processMonthlyResets();
    
    const executionTime = Date.now() - startTime;
    
    console.log(`[CRON] Monthly reset job completed in ${executionTime}ms`);
    console.log(`[CRON] Processed: ${result.processed}, Errors: ${result.errors}`);
    
    return {
      success: true,
      processed: result.processed,
      errors: result.errors,
      executionTime
    };
    
  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error('[CRON] Monthly reset job failed:', error);
    
    return {
      success: false,
      processed: 0,
      errors: 1,
      executionTime
    };
  }
}

/**
 * Vercel Cron Job Configuration
 * 
 * Add this to your vercel.json:
 * 
 * {
 *   "crons": [{
 *     "path": "/api/cron/monthly-reset",
 *     "schedule": "0 0 * * *"
 *   }]
 * }
 */

/**
 * Example API Route for Vercel Cron
 * 
 * Create this file at: src/app/api/cron/monthly-reset/route.ts
 * 
 * import { NextRequest, NextResponse } from 'next/server';
 * import { runMonthlyResetJob } from '@/lib/cron-jobs';
 * 
 * export async function GET(request: NextRequest) {
 *   // Verify this is Vercel calling (optional but recommended)
 *   const authHeader = request.headers.get('authorization');
 *   if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
 *     return new NextResponse('Unauthorized', { status: 401 });
 *   }
 *   
 *   const result = await runMonthlyResetJob();
 *   
 *   return NextResponse.json(result);
 * }
 */

/**
 * Local Development Testing
 * 
 * To test the cron job locally:
 * 
 * import { runMonthlyResetJob } from './lib/cron-jobs';
 * 
 * // Run once
 * await runMonthlyResetJob();
 * 
 * // Or simulate periodic execution
 * setInterval(async () => {
 *   await runMonthlyResetJob();
 * }, 24 * 60 * 60 * 1000); // Daily
 */