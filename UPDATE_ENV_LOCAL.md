# Update .env.local Instructions

## Environment Variables Configuration

Update your `.env.local` file with these values:

```env
# NextAuth Configuration
NEXTAUTH_SECRET=GyR5wc479Bc4VlydwpWiPxYxyruJH5KMwOBWoDtOLrU=
NEXTAUTH_URL=http://localhost:3000

# Database
MONGODB_URI=mongodb+srv://sbdinkins:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Application
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Other services (optional)
ANTHROPIC_API_KEY=your-anthropic-api-key
STRIPE_SECRET_KEY=your-stripe-secret
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable
```

## Important Steps After Updating:

1. **Update Google OAuth Console**:
   - Add `http://localhost:3000` to authorized JavaScript origins
   - Add `http://localhost:3000/api/auth/callback/google` to redirect URIs

3. **Restart your development server** after making these changes

## Security Warning ⚠️

The MongoDB URI in your message contains credentials. Please:
1. Change your MongoDB password immediately
2. Never share database credentials publicly
3. Consider using MongoDB Atlas IP whitelist for additional security

## Next Steps:

1. Copy the environment variables above to your `.env.local` file
2. Replace placeholder values with your actual credentials
3. Update the port configuration in other files as needed