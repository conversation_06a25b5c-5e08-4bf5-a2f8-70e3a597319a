'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Menu, X, User, ChevronDown, FileText, Settings, LogOut, Crown, BarChart3 } from 'lucide-react'

interface NavigationProps {
  user?: {
    name: string
    email: string
    avatar?: string
    plan: 'free' | 'pro'
    invoiceCount?: number
    maxInvoices?: number
  }
  onSignIn?: () => void
  onSignOut?: () => void
}

export default function Navigation({ user, onSignIn, onSignOut }: NavigationProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)

  const navigationItems = [
    { name: 'Home', href: '/' },
    { name: 'Templates', href: '/templates' },
    { name: 'Guides', href: '/guides' },
    { name: 'Create Invoice', href: '/create', highlight: true },
    ...(user ? [{ name: 'Dashboard', href: '/dashboard' }] : []),
  ]

  const toggleMobileMenu = () => setMobileMenuOpen(!mobileMenuOpen)
  const toggleUserMenu = () => setUserMenuOpen(!userMenuOpen)

  return (
    <>
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/95 backdrop-blur-lg shadow-sm border-b border-gray-100">
        <nav className="max-w-7xl mx-auto flex items-center justify-between px-4 py-4">
          {/* Logo */}
          <Link href="/" className="font-extrabold text-2xl tracking-tighter text-black">
            <span className="bg-black text-white px-2 py-1 rounded">TI</span> Template Invoice
          </Link>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-6">
            <ul className="flex gap-6 text-lg font-medium text-gray-700">
              {navigationItems.map((item) => (
                <li key={item.name}>
                  {item.highlight ? (
                    <Link 
                      href={item.href}
                      className="bg-black text-white px-4 py-2 rounded-lg font-semibold shadow hover:bg-gray-900 transition"
                    >
                      {item.name}
                    </Link>
                  ) : (
                    <Link 
                      href={item.href}
                      className="hover:text-black transition-colors"
                    >
                      {item.name}
                    </Link>
                  )}
                </li>
              ))}
            </ul>

            {/* Authentication Section */}
            {user ? (
              <div className="relative">
                <button
                  onClick={toggleUserMenu}
                  className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  {user.avatar ? (
                    <img src={user.avatar} alt={user.name} className="w-8 h-8 rounded-full" />
                  ) : (
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-gray-600" />
                    </div>
                  )}
                  <span className="text-sm font-medium text-gray-700">{user.name}</span>
                  <div className="flex items-center gap-1">
                    {user.plan === 'pro' && <Crown className="w-3 h-3 text-yellow-500" />}
                    <span className="text-xs text-gray-500 uppercase">{user.plan}</span>
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  </div>
                </button>

                {/* User Dropdown Menu */}
                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                    <div className="px-4 py-2 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">{user.name}</p>
                      <p className="text-xs text-gray-500">{user.email}</p>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-500 uppercase flex items-center gap-1">
                          {user.plan === 'pro' && <Crown className="w-3 h-3 text-yellow-500" />}
                          {user.plan} Plan
                        </span>
                        {user.plan === 'free' && user.invoiceCount !== undefined && user.maxInvoices && (
                          <span className="text-xs text-gray-500">
                            {user.invoiceCount}/{user.maxInvoices} invoices
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <Link href="/dashboard" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                      <BarChart3 className="w-4 h-4" />
                      Dashboard
                    </Link>
                    
                    <Link href="/my-invoices" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                      <FileText className="w-4 h-4" />
                      My Invoices
                    </Link>
                    
                    <Link href="/settings" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                      <Settings className="w-4 h-4" />
                      Settings
                    </Link>
                    
                    {user.plan === 'free' && (
                      <Link href="/upgrade" className="flex items-center gap-2 px-4 py-2 text-sm text-yellow-600 hover:bg-yellow-50">
                        <Crown className="w-4 h-4" />
                        Upgrade to Pro
                      </Link>
                    )}
                    
                    <hr className="my-1" />
                    
                    <button
                      onClick={onSignOut}
                      className="flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                    >
                      <LogOut className="w-4 h-4" />
                      Sign Out
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={onSignIn}
                className="bg-black text-white px-6 py-2 rounded-lg font-semibold shadow hover:bg-gray-900 transition"
              >
                Sign In
              </button>
            )}
          </div>
          
          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 rounded focus:outline-none focus:ring-2 focus:ring-black"
            aria-label="Toggle Menu"
            onClick={toggleMobileMenu}
          >
            {mobileMenuOpen ? <X className="w-7 h-7 text-black" /> : <Menu className="w-7 h-7 text-black" />}
          </button>
        </nav>
        
        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="fixed inset-0 z-50 bg-black/95 backdrop-blur-lg flex flex-col items-center justify-center space-y-6 text-xl font-semibold md:hidden">
            <button
              className="absolute top-4 right-4 p-2 text-white"
              onClick={toggleMobileMenu}
            >
              <X className="w-8 h-8" />
            </button>
            
            {/* User Info in Mobile Menu */}
            {user && (
              <div className="text-center mb-4">
                {user.avatar ? (
                  <img src={user.avatar} alt={user.name} className="w-16 h-16 rounded-full mx-auto mb-2" />
                ) : (
                  <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-2">
                    <User className="w-8 h-8 text-gray-600" />
                  </div>
                )}
                <p className="text-white text-lg">{user.name}</p>
                <p className="text-gray-300 text-sm flex items-center justify-center gap-1">
                  {user.plan === 'pro' && <Crown className="w-3 h-3 text-yellow-500" />}
                  {user.plan} Plan
                </p>
                {user.plan === 'free' && user.invoiceCount !== undefined && user.maxInvoices && (
                  <p className="text-gray-400 text-xs">
                    {user.invoiceCount}/{user.maxInvoices} invoices used
                  </p>
                )}
              </div>
            )}
            
            {/* Mobile Navigation Items */}
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`text-white hover:text-gray-300 transition-colors touch-manipulation ${
                  item.highlight ? 'bg-white text-black px-6 py-3 rounded-xl' : ''
                }`}
                onClick={toggleMobileMenu}
              >
                {item.name}
              </Link>
            ))}
            
            {/* Mobile Authentication */}
            {user ? (
              <div className="flex flex-col gap-4 mt-6">
                <Link href="/dashboard" className="text-gray-300 hover:text-white transition-colors" onClick={toggleMobileMenu}>
                  Dashboard
                </Link>
                <Link href="/settings" className="text-gray-300 hover:text-white transition-colors" onClick={toggleMobileMenu}>
                  Settings
                </Link>
                {user.plan === 'free' && (
                  <Link href="/upgrade" className="text-yellow-400 hover:text-yellow-300 transition-colors" onClick={toggleMobileMenu}>
                    Upgrade to Pro
                  </Link>
                )}
                <button
                  onClick={() => {
                    onSignOut?.()
                    toggleMobileMenu()
                  }}
                  className="text-red-400 hover:text-red-300 transition-colors"
                >
                  Sign Out
                </button>
              </div>
            ) : (
              <button
                onClick={() => {
                  onSignIn?.()
                  toggleMobileMenu()
                }}
                className="mt-6 bg-white text-black px-8 py-3 rounded-xl shadow hover:bg-gray-100 transition touch-manipulation"
              >
                Sign In
              </button>
            )}
          </div>
        )}
      </header>
    </>
  )
}

// Footer Component (can be used separately)
export function Footer() {
  return (
    <footer className="bg-white/50 backdrop-blur-sm border-t">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8 mb-8">
          <div>
            <h3 className="font-extrabold text-2xl tracking-tighter text-black mb-4">
              <span className="bg-black text-white px-2 py-1 rounded">TI</span> Template Invoice
            </h3>
            <p className="text-gray-600 mb-6">
              Create professional invoices with our simple and powerful tools.
            </p>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-4">Product</h4>
            <ul className="space-y-2">
              <li><Link href="/templates" className="text-gray-600 hover:text-gray-900 transition-colors">Templates</Link></li>
              <li><Link href="/create" className="text-gray-600 hover:text-gray-900 transition-colors">Create Invoice</Link></li>
              <li><Link href="/my-invoices" className="text-gray-600 hover:text-gray-900 transition-colors">My Invoices</Link></li>
              <li><Link href="/pricing" className="text-gray-600 hover:text-gray-900 transition-colors">Pricing</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-4">Company</h4>
            <ul className="space-y-2">
              <li><Link href="/about" className="text-gray-600 hover:text-gray-900 transition-colors">About</Link></li>
              <li><Link href="/contact" className="text-gray-600 hover:text-gray-900 transition-colors">Contact</Link></li>
              <li><Link href="/privacy" className="text-gray-600 hover:text-gray-900 transition-colors">Privacy</Link></li>
              <li><Link href="/terms" className="text-gray-600 hover:text-gray-900 transition-colors">Terms</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-4">Contact</h4>
            <ul className="space-y-3">
              <li className="text-gray-600">
                <strong>Email:</strong><br />
                <EMAIL>
              </li>
              <li className="text-gray-600">
                <strong>Support:</strong><br />
                <EMAIL>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t pt-6 flex flex-col md:flex-row justify-between items-center text-gray-600">
          <p>&copy; {new Date().getFullYear()} Template Invoice. All rights reserved.</p>
          <div className="flex gap-6 mt-4 md:mt-0">
            <Link href="/privacy" className="hover:text-gray-900 transition-colors">Privacy</Link>
            <Link href="/terms" className="hover:text-gray-900 transition-colors">Terms</Link>
            <Link href="/contact" className="hover:text-gray-900 transition-colors">Contact</Link>
          </div>
        </div>
      </div>
    </footer>
  )
}