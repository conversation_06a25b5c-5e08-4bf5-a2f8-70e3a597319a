import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canCreateInvoice, checkInvoiceLimit } from '@/lib/invoice-limits';

/**
 * API Route: Check if user can create an invoice
 * GET /api/invoices/can-create
 * 
 * Returns whether the user can create more invoices based on their plan limits
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user can create invoice
    const result = await canCreateInvoice(session.user.id);
    
    // Get detailed usage statistics
    const usage = await checkInvoiceLimit(session.user.id);
    
    return NextResponse.json({
      canCreate: result.canCreate,
      reason: result.reason,
      remainingInvoices: result.remainingInvoices,
      showUpgradePrompt: result.showUpgradePrompt,
      upgradeMessage: result.upgradeMessage,
      usagePercentage: result.usagePercentage,
      usage: {
        used: usage.used,
        limit: usage.limit,
        remaining: usage.remaining,
        percentage: usage.percentage,
        plan: usage.plan,
        resetDate: usage.resetDate,
        isUnlimited: usage.isUnlimited,
        shouldShowWarning: usage.shouldShowWarning,
        shouldShowUpgrade: usage.shouldShowUpgrade
      }
    });

  } catch (error) {
    console.error('Error checking invoice creation limit:', error);
    return NextResponse.json(
      { error: 'Failed to check invoice limit' },
      { status: 500 }
    );
  }
}