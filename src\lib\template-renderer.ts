// Use our simple template engine instead of Handlebars
import { renderSimpleTemplate, compileSimpleTemplate } from './simple-template-engine';
import { templateDefinitions, type TemplateDefinition, type InvoiceData } from './templates/template-definitions';

// Helper functions to work with templates
export function getAllTemplates(): TemplateDefinition[] {
  return templateDefinitions;
}

export function getTemplateById(id: string): TemplateDefinition | undefined {
  return templateDefinitions.find(t => t.id === id);
}

export { type TemplateDefinition, type InvoiceData };

// Cache compiled templates for performance
const templateCache = new Map<string, (data: any) => string>();

/**
 * Compile and cache a template
 */
function compileTemplate(templateId: string, htmlTemplate: string): (data: any) => string {
  if (templateCache.has(templateId)) {
    return templateCache.get(templateId)!;
  }
  
  const compiled = compileSimpleTemplate(htmlTemplate);
  templateCache.set(templateId, compiled);
  return compiled;
}

/**
 * Render template with provided data
 */
export function renderTemplate(templateId: string, data: Partial<InvoiceData>): string {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template with ID "${templateId}" not found`);
  }
  
  try {
    const compiledTemplate = compileTemplate(templateId, template.htmlTemplate);
    const processedData = prepareTemplateData(data, template);
    return compiledTemplate(processedData);
  } catch (error) {
    console.error(`Error rendering template ${templateId}:`, error);
    throw new Error(`Failed to render template: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate preview with sample data
 */
export function generatePreview(templateId: string): string {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template with ID "${templateId}" not found`);
  }
  
  return renderTemplate(templateId, template.sampleData);
}

/**
 * Generate thumbnail preview (scaled down version)
 */
export function generateThumbnailPreview(templateId: string): string {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template with ID "${templateId}" not found`);
  }
  
  // Add thumbnail-specific styles
  const thumbnailData = {
    ...template.sampleData,
    lineItems: template.sampleData.lineItems.slice(0, 2) // Limit items for thumbnail
  };
  
  const rendered = renderTemplate(templateId, thumbnailData);
  
  // Wrap in thumbnail container with scaling
  return `
    <div style="transform: scale(0.3); transform-origin: top left; width: 333%; height: 333%; overflow: hidden;">
      ${rendered}
    </div>
  `;
}

/**
 * Prepare and validate data for template rendering
 */
export function prepareTemplateData(formData: Partial<InvoiceData>, template: TemplateDefinition): InvoiceData {
  // Merge form data with template defaults
  const processedData: InvoiceData = {
    // Company information
    companyName: formData.companyName || template.sampleData.companyName,
    companyAddress: formData.companyAddress || template.sampleData.companyAddress,
    companyPhone: formData.companyPhone || template.sampleData.companyPhone,
    companyEmail: formData.companyEmail || template.sampleData.companyEmail,
    companyLogo: formData.companyLogo || template.sampleData.companyLogo,
    
    // Invoice details
    invoiceNumber: formData.invoiceNumber || generateInvoiceNumber(),
    invoiceDate: formData.invoiceDate || new Date().toISOString().split('T')[0],
    dueDate: formData.dueDate || getDueDate(30), // Default 30 days
    
    // Client information
    clientName: formData.clientName || template.sampleData.clientName,
    clientAddress: formData.clientAddress || template.sampleData.clientAddress,
    clientPhone: formData.clientPhone || template.sampleData.clientPhone,
    clientEmail: formData.clientEmail || template.sampleData.clientEmail,
    
    // Line items and calculations
    lineItems: formData.lineItems && formData.lineItems.length > 0 
      ? formData.lineItems.map(item => ({
          ...item,
          amount: Number((item.quantity * item.rate).toFixed(2))
        }))
      : template.sampleData.lineItems,
    
    // Calculate totals
    subtotal: 0, // Will be calculated below
    tax: 0, // Will be calculated below
    taxRate: formData.taxRate || template.sampleData.taxRate,
    total: 0, // Will be calculated below
    
    // Additional fields
    notes: formData.notes || template.sampleData.notes,
    paymentTerms: formData.paymentTerms || template.sampleData.paymentTerms
  };
  
  // Calculate financial totals
  const calculations = calculateTotals(processedData.lineItems, processedData.taxRate);
  processedData.subtotal = calculations.subtotal;
  processedData.tax = calculations.tax;
  processedData.total = calculations.total;
  
  return processedData;
}

/**
 * Prepare data specifically for invoice generation from form data
 */
export function prepareInvoiceData(formData: any, templateId: string): InvoiceData {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template with ID "${templateId}" not found`);
  }
  
  return prepareTemplateData(formData, template);
}

/**
 * Calculate invoice totals
 */
function calculateTotals(lineItems: any[], taxRate: number = 0) {
  const subtotal = calculateSubtotal(lineItems);
  const tax = calculateTax(subtotal, taxRate);
  const total = subtotal + tax;
  
  return {
    subtotal: Number(subtotal.toFixed(2)),
    tax: Number(tax.toFixed(2)),
    total: Number(total.toFixed(2))
  };
}

/**
 * Calculate subtotal from line items
 */
function calculateSubtotal(lineItems: any[]): number {
  return lineItems.reduce((sum, item) => {
    const amount = Number(item.quantity) * Number(item.rate);
    return sum + (isNaN(amount) ? 0 : amount);
  }, 0);
}

/**
 * Calculate tax amount
 */
function calculateTax(subtotal: number, taxRate: number): number {
  const rate = Number(taxRate) || 0;
  return subtotal * (rate / 100);
}

/**
 * Calculate total amount
 */
function calculateTotal(lineItems: any[], taxRate: number): number {
  const subtotal = calculateSubtotal(lineItems);
  const tax = calculateTax(subtotal, taxRate);
  return subtotal + tax;
}

/**
 * Generate unique invoice number
 */
function generateInvoiceNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `INV-${timestamp.slice(-6)}${random}`;
}

/**
 * Generate due date based on days from now
 */
function getDueDate(daysFromNow: number = 30): string {
  const date = new Date();
  date.setDate(date.getDate() + daysFromNow);
  return date.toISOString().split('T')[0];
}

/**
 * Validate template data before rendering
 */
export function validateTemplateData(data: Partial<InvoiceData>): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Required company information
  if (!data.companyName?.trim()) {
    errors.push('Company name is required');
  }
  
  if (!data.companyEmail?.trim()) {
    errors.push('Company email is required');
  }
  
  // Required client information
  if (!data.clientName?.trim()) {
    errors.push('Client name is required');
  }
  
  // Required invoice details
  if (!data.invoiceNumber?.trim()) {
    errors.push('Invoice number is required');
  }
  
  if (!data.invoiceDate) {
    errors.push('Invoice date is required');
  }
  
  if (!data.dueDate) {
    errors.push('Due date is required');
  }
  
  // Validate line items
  if (!data.lineItems || data.lineItems.length === 0) {
    errors.push('At least one line item is required');
  } else {
    data.lineItems.forEach((item, index) => {
      if (!item.description?.trim()) {
        errors.push(`Line item ${index + 1}: Description is required`);
      }
      
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
      }
      
      if (!item.rate || item.rate < 0) {
        errors.push(`Line item ${index + 1}: Rate must be 0 or greater`);
      }
    });
  }
  
  // Validate email format
  if (data.companyEmail && !isValidEmail(data.companyEmail)) {
    errors.push('Company email format is invalid');
  }
  
  if (data.clientEmail && !isValidEmail(data.clientEmail)) {
    errors.push('Client email format is invalid');
  }
  
  // Validate dates
  if (data.invoiceDate && data.dueDate) {
    const invoiceDate = new Date(data.invoiceDate);
    const dueDate = new Date(data.dueDate);
    
    if (dueDate < invoiceDate) {
      errors.push('Due date cannot be before invoice date');
    }
  }
  
  // Validate tax rate
  if (data.taxRate && (data.taxRate < 0 || data.taxRate > 100)) {
    errors.push('Tax rate must be between 0 and 100');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate email format
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Get list of available templates with metadata
 */
export function getAvailableTemplates() {
  return getAllTemplates().map(template => ({
    id: template.id,
    name: template.name,
    category: template.category,
    description: template.description,
    thumbnail: template.thumbnail,
    styling: template.styling
  }));
}

/**
 * Clear template cache (useful for development)
 */
export function clearTemplateCache(): void {
  templateCache.clear();
}

/**
 * Get template categories
 */
export function getTemplateCategories(): string[] {
  const templates = getAllTemplates();
  const categories = new Set(templates.map(t => t.category));
  return Array.from(categories);
}

/**
 * Search templates by name or description
 */
export function searchTemplates(query: string): TemplateDefinition[] {
  const templates = getAllTemplates();
  const searchTerm = query.toLowerCase().trim();
  
  if (!searchTerm) {
    return templates;
  }
  
  return templates.filter(template => 
    template.name.toLowerCase().includes(searchTerm) ||
    template.description.toLowerCase().includes(searchTerm) ||
    template.category.toLowerCase().includes(searchTerm)
  );
}

// Export calculation functions for use in other modules
export {
  calculateSubtotal,
  calculateTax,
  calculateTotal,
  generateInvoiceNumber,
  getDueDate
};