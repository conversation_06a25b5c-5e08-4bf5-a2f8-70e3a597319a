'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  <PERSON>rkles,
  Send,
  FileText,
  Zap,
  Eye,
  MessageSquare,
  Clock,
  Shield,
  Globe,
  CreditCard,
  BarChart3,
  CheckCircle,
  ArrowRight,
  Play,
  Users,
  Palette,
  Link2,
  <PERSON>freshCw,
  DollarSign,
  TrendingUp
} from 'lucide-react'
import { Button } from '@/components/ui/Button'

export default function FeaturesSection() {
  const [activeFeature, setActiveFeature] = useState(0)
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  const features = [
    {
      icon: Sparkles,
      title: 'AI Writes Your Invoices',
      problem: 'Struggling with invoice descriptions?',
      solution: 'Professional language that gets you paid',
      description: 'Our AI analyzes your business type and automatically generates professional invoice descriptions that clients take seriously.',
      stats: '94% faster invoice creation',
      visual: {
        before: 'Generic, unclear descriptions',
        after: 'Professional, detailed breakdowns'
      },
      testimonial: {
        quote: "The AI descriptions are so professional, clients never question my rates anymore",
        author: "<PERSON>",
        role: "UX Designer"
      }
    },
    {
      icon: Send,
      title: 'Automatic Payment Reminders',
      problem: 'Awkward follow-up emails?',
      solution: 'Professional reminders that maintain relationships',
      description: 'Set it and forget it. Our system sends perfectly timed, professional reminders that get you paid without damaging client relationships.',
      stats: '3x faster payments',
      visual: {
        before: 'Manual, awkward follow-ups',
        after: 'Automated, professional sequences'
      },
      testimonial: {
        quote: "No more chasing payments. The automated reminders do all the work for me",
        author: "Mike Johnson",
        role: "Freelance Developer"
      }
    },
    {
      icon: FileText,
      title: 'Industry-Specific Templates',
      problem: 'Generic invoices that look amateur?',
      solution: 'Templates that make you look established',
      description: 'Choose from hundreds of templates designed specifically for your industry. Each one tested to maximize payment speed.',
      stats: '87% higher client satisfaction',
      visual: {
        before: 'Basic, unprofessional invoices',
        after: 'Branded, industry-specific designs'
      },
      testimonial: {
        quote: "My photography invoices finally match the quality of my work",
        author: "Emma Wilson",
        role: "Wedding Photographer"
      }
    },
    {
      icon: CreditCard,
      title: 'One-Click Payments',
      problem: 'Waiting weeks for checks?',
      solution: 'Get paid in minutes with payment links',
      description: 'Add payment buttons to every invoice. Clients can pay instantly with credit card, ACH, or digital wallets.',
      stats: '82% same-day payment rate',
      visual: {
        before: 'Complex payment instructions',
        after: 'Simple "Pay Now" button'
      },
      testimonial: {
        quote: "Got paid in 10 minutes instead of 10 weeks. Game changer!",
        author: "David Park",
        role: "Marketing Consultant"
      }
    },
    {
      icon: BarChart3,
      title: 'Track Everything',
      problem: 'Lost invoices and missed payments?',
      solution: 'Know exactly who owes what and when',
      description: 'Real-time dashboard shows payment status, overdue invoices, and cash flow projections. Never lose track again.',
      stats: '$47K average recovered revenue',
      visual: {
        before: 'Scattered spreadsheets',
        after: 'Unified dashboard'
      },
      testimonial: {
        quote: "Found $12,000 in unpaid invoices I had completely forgotten about",
        author: "Lisa Martinez",
        role: "Interior Designer"
      }
    }
  ]

  const additionalFeatures = [
    { icon: RefreshCw, title: "Recurring Invoices", description: "Set up once, bill automatically" },
    { icon: Globe, title: "Multi-Currency", description: "Bill clients worldwide" },
    { icon: Palette, title: "Custom Branding", description: "Your logo, your colors" },
    { icon: Link2, title: "Integrations", description: "Connects with your tools" },
    { icon: Shield, title: "Bank-Level Security", description: "Your data is safe" },
    { icon: Users, title: "Team Collaboration", description: "Work together seamlessly" }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Everything you need to{' '}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              get paid like a pro
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Stop using tools that make you look amateur. Start using features that get you paid faster.
          </p>
        </motion.div>

        {/* Main Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                onMouseEnter={() => setHoveredCard(index)}
                onMouseLeave={() => setHoveredCard(null)}
                className="relative bg-gray-50 rounded-xl p-8 cursor-pointer transition-all hover:shadow-xl hover:scale-[1.02]"
                onClick={() => setActiveFeature(index)}
              >
                {/* Feature Icon */}
                <motion.div
                  animate={{
                    scale: hoveredCard === index ? 1.1 : 1,
                    rotate: hoveredCard === index ? 5 : 0
                  }}
                  className={`w-14 h-14 rounded-lg flex items-center justify-center mb-6 ${
                    index === 0 ? 'bg-blue-100' :
                    index === 1 ? 'bg-green-100' :
                    index === 2 ? 'bg-purple-100' :
                    index === 3 ? 'bg-pink-100' :
                    'bg-yellow-100'
                  }`}
                >
                  <Icon className={`w-7 h-7 ${
                    index === 0 ? 'text-blue-600' :
                    index === 1 ? 'text-green-600' :
                    index === 2 ? 'text-purple-600' :
                    index === 3 ? 'text-pink-600' :
                    'text-yellow-600'
                  }`} />
                </motion.div>

                {/* Content */}
                <h3 className="text-xl font-bold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-sm text-red-600 mb-2">{feature.problem}</p>
                <p className="text-gray-700 mb-4">{feature.solution}</p>
                
                {/* Stats */}
                <div className="flex items-center gap-2 mb-4">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-semibold text-green-600">{feature.stats}</span>
                </div>

                {/* Visual Comparison */}
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="bg-red-50 rounded p-2">
                    <p className="text-red-600 font-medium mb-1">Before:</p>
                    <p className="text-gray-600">{feature.visual.before}</p>
                  </div>
                  <div className="bg-green-50 rounded p-2">
                    <p className="text-green-600 font-medium mb-1">After:</p>
                    <p className="text-gray-600">{feature.visual.after}</p>
                  </div>
                </div>

                {/* Hover Effect */}
                <AnimatePresence>
                  {hoveredCard === index && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl p-8 flex flex-col justify-between"
                    >
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
                        <p className="text-white/90 mb-4">{feature.description}</p>
                      </div>
                      <div>
                        <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 mb-4">
                          <p className="text-white/90 italic mb-2">"{feature.testimonial.quote}"</p>
                          <p className="text-white/80 text-sm">
                            - {feature.testimonial.author}, {feature.testimonial.role}
                          </p>
                        </div>
                        <Button
                          variant="secondary"
                          className="bg-white text-gray-900 hover:bg-gray-100"
                          size="sm"
                        >
                          Learn More
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            )
          })}
        </div>

        {/* Feature Deep Dive */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-8 md:p-12 mb-16"
        >
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-3xl font-bold text-white mb-4">
                {features[activeFeature].title}
              </h3>
              <p className="text-gray-300 mb-6">
                {features[activeFeature].description}
              </p>
              
              {/* Feature Benefits */}
              <div className="space-y-3 mb-6">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
                  <p className="text-white">Saves an average of 4 hours per week</p>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
                  <p className="text-white">Reduces payment delays by 67%</p>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
                  <p className="text-white">Improves client satisfaction scores</p>
                </div>
              </div>

              <Button
                className="bg-white text-gray-900 hover:bg-gray-100"
              >
                <Play className="w-4 h-4 mr-2" />
                Watch Demo (30s)
              </Button>
            </div>

            {/* Interactive Visual */}
            <div className="relative">
              <motion.div
                animate={{
                  y: [0, -10, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                className="bg-white rounded-lg shadow-2xl p-6"
              >
                <div className="space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-3/4" />
                  <div className="h-4 bg-gray-200 rounded w-full" />
                  <div className="h-4 bg-gray-200 rounded w-2/3" />
                  <div className="mt-6 flex gap-3">
                    <div className="h-10 bg-blue-600 rounded flex-1 flex items-center justify-center">
                      <span className="text-white font-medium">Pay Now</span>
                    </div>
                  </div>
                </div>
              </motion.div>
              
              {/* Floating elements */}
              <motion.div
                animate={{
                  x: [0, 20, 0],
                  y: [0, -10, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                className="absolute -top-4 -right-4 bg-green-100 rounded-lg p-3"
              >
                <DollarSign className="w-6 h-6 text-green-600" />
              </motion.div>
              
              <motion.div
                animate={{
                  x: [0, -20, 0],
                  y: [0, 10, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse",
                  delay: 1
                }}
                className="absolute -bottom-4 -left-4 bg-blue-100 rounded-lg p-3"
              >
                <Zap className="w-6 h-6 text-blue-600" />
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Additional Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Plus everything else you need
          </h3>
          <div className="grid md:grid-cols-3 gap-6">
            {additionalFeatures.map((feature, index) => {
              const Icon = feature.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-start gap-4"
                >
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Icon className="w-5 h-5 text-gray-700" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{feature.title}</h4>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-lg text-gray-600 mb-6">
            Ready to stop looking amateur and start getting paid like a pro?
          </p>
          <Button
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            Start Free Trial
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </motion.div>
      </div>
    </section>
  )
}