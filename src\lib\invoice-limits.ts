/**
 * Invoice Usage Tracking System
 * 
 * Manages invoice creation limits for freemium model:
 * - Free users: 3 invoices per month
 * - Pro users: unlimited invoices
 * - Monthly reset based on signup anniversary date
 */

import { ObjectId } from 'mongodb';
import clientPromise from './mongodb';
import { UserDocument, COLLECTIONS } from './models';
import { trackEvent, trackConversionFunnel } from './analytics-service';

// Constants for invoice limits
export const INVOICE_LIMITS = {
  FREE_TIER_LIMIT: 3,
  PRO_TIER_LIMIT: Infinity,
  GRACE_PERIOD_DAYS: 1, // 1 day grace period for edge cases
} as const;

// Get database instance
async function getDatabase() {
  const client = await clientPromise;
  return client.db();
}

/**
 * Check if monthly reset is needed based on signup anniversary
 * @param user - User document
 * @returns boolean indicating if reset is needed
 */
function shouldResetMonthlyUsage(user: UserDocument): boolean {
  const now = new Date();
  const resetDate = new Date(user.subscription.resetDate);
  
  // Check if we've passed the reset date
  if (now >= resetDate) {
    return true;
  }
  
  // Grace period handling: Allow reset up to 1 day early for edge cases
  const gracePeriodDate = new Date(resetDate);
  gracePeriodDate.setDate(gracePeriodDate.getDate() - INVOICE_LIMITS.GRACE_PERIOD_DAYS);
  
  return now >= gracePeriodDate;
}

/**
 * Calculate next reset date (first day of next month from current date)
 * @param fromDate - Date to calculate from (defaults to now)
 * @returns Next reset date
 */
function calculateNextResetDate(fromDate: Date = new Date()): Date {
  const nextReset = new Date(fromDate);
  
  // Set to first day of next month
  if (nextReset.getMonth() === 11) {
    nextReset.setFullYear(nextReset.getFullYear() + 1);
    nextReset.setMonth(0);
  } else {
    nextReset.setMonth(nextReset.getMonth() + 1);
  }
  
  nextReset.setDate(1);
  nextReset.setHours(0, 0, 0, 0);
  
  return nextReset;
}

/**
 * Get invoice limit for user based on subscription plan
 * @param plan - User's subscription plan
 * @returns Invoice limit number
 */
function getInvoiceLimitForPlan(plan: 'free' | 'pro'): number {
  return plan === 'pro' ? INVOICE_LIMITS.PRO_TIER_LIMIT : INVOICE_LIMITS.FREE_TIER_LIMIT;
}

/**
 * Check current invoice usage and remaining count
 * @param userId - User ID to check
 * @returns Object with usage details
 */
export async function checkInvoiceLimit(userId: string | ObjectId): Promise<{
  used: number;
  limit: number;
  remaining: number;
  percentage: number;
  plan: 'free' | 'pro';
  resetDate: Date;
  isUnlimited: boolean;
  shouldShowWarning: boolean;
  shouldShowUpgrade: boolean;
}> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  const user = await usersCollection.findOne({ _id: objectId });
  
  if (!user) {
    throw new Error('User not found');
  }
  
  // Check if monthly reset is needed
  if (shouldResetMonthlyUsage(user)) {
    await resetMonthlyCount(userId);
    // Refetch user after reset
    const updatedUser = await usersCollection.findOne({ _id: objectId });
    if (updatedUser) {
      user.subscription = updatedUser.subscription;
    }
  }
  
  const plan = user.subscription.plan;
  const limit = getInvoiceLimitForPlan(plan);
  const used = user.subscription.invoicesUsed || 0;
  const remaining = Math.max(0, limit - used);
  const percentage = limit === Infinity ? 0 : (used / limit) * 100;
  const isUnlimited = plan === 'pro';
  
  // Warning thresholds
  const shouldShowWarning = !isUnlimited && used >= Math.floor(limit * 0.67); // Show warning at 67% usage
  const shouldShowUpgrade = !isUnlimited && used >= limit; // Show upgrade when limit reached
  
  // Track usage analytics
  if (shouldShowWarning && used === Math.floor(limit * 0.67)) {
    await trackEvent(objectId, 'invoice_usage_warning', {
      used,
      limit,
      percentage,
      plan
    });
  }
  
  return {
    used,
    limit,
    remaining,
    percentage,
    plan,
    resetDate: user.subscription.resetDate,
    isUnlimited,
    shouldShowWarning,
    shouldShowUpgrade
  };
}

/**
 * Increment invoice count for user (atomic operation)
 * @param userId - User ID to increment
 * @returns Updated usage count
 */
export async function incrementInvoiceCount(userId: string | ObjectId): Promise<number> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // First check if reset is needed
  const user = await usersCollection.findOne({ _id: objectId });
  if (!user) {
    throw new Error('User not found');
  }
  
  if (shouldResetMonthlyUsage(user)) {
    await resetMonthlyCount(userId);
  }
  
  // Atomic increment operation
  const result = await usersCollection.findOneAndUpdate(
    { _id: objectId },
    { 
      $inc: { 'subscription.invoicesUsed': 1 },
      $set: { updatedAt: new Date() }
    },
    { returnDocument: 'after' }
  );
  
  if (!result.value) {
    throw new Error('Failed to increment invoice count');
  }
  
  const newCount = result.value.subscription.invoicesUsed;
  
  // Track analytics for usage milestones
  await trackEvent(objectId, 'invoice_created', {
    invoiceNumber: newCount,
    plan: result.value.subscription.plan,
    monthlyUsage: newCount,
    limit: getInvoiceLimitForPlan(result.value.subscription.plan)
  });
  
  // Track conversion funnel if approaching limit
  const limit = getInvoiceLimitForPlan(result.value.subscription.plan);
  if (result.value.subscription.plan === 'free' && newCount === limit) {
    await trackConversionFunnel(objectId, 'limit_hit', {
      invoicesUsed: newCount,
      limit,
      source: 'invoice_creation'
    });
  }
  
  return newCount;
}

/**
 * Reset monthly invoice count (called on anniversary date)
 * @param userId - User ID to reset
 * @returns Success boolean
 */
export async function resetMonthlyCount(userId: string | ObjectId): Promise<boolean> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Get current user data for analytics
  const user = await usersCollection.findOne({ _id: objectId });
  if (!user) {
    throw new Error('User not found');
  }
  
  const previousUsage = user.subscription.invoicesUsed;
  const nextResetDate = calculateNextResetDate();
  
  // Reset invoice count and update reset date
  const result = await usersCollection.updateOne(
    { _id: objectId },
    { 
      $set: { 
        'subscription.invoicesUsed': 0,
        'subscription.resetDate': nextResetDate,
        updatedAt: new Date()
      }
    }
  );
  
  if (result.modifiedCount > 0) {
    // Track reset analytics
    await trackEvent(objectId, 'monthly_usage_reset', {
      previousUsage,
      plan: user.subscription.plan,
      resetDate: nextResetDate.toISOString(),
      daysInCycle: 30
    });
    
    console.log(`Monthly invoice count reset for user ${objectId}: ${previousUsage} → 0`);
    return true;
  }
  
  return false;
}

/**
 * Check if user can create an invoice (main validation function)
 * @param userId - User ID to check
 * @returns Object with permission and reason
 */
export async function canCreateInvoice(userId: string | ObjectId): Promise<{
  canCreate: boolean;
  reason?: string;
  remainingInvoices?: number;
  showUpgradePrompt?: boolean;
  upgradeMessage?: string;
  usagePercentage?: number;
}> {
  try {
    const usage = await checkInvoiceLimit(userId);
    
    // Pro users always can create
    if (usage.isUnlimited) {
      return {
        canCreate: true,
        remainingInvoices: Infinity,
        usagePercentage: 0
      };
    }
    
    // Free users: check against limit
    if (usage.remaining <= 0) {
      return {
        canCreate: false,
        reason: `You've reached your monthly limit of ${usage.limit} invoices. Upgrade to Pro for unlimited invoices.`,
        remainingInvoices: 0,
        showUpgradePrompt: true,
        upgradeMessage: 'Unlock unlimited invoices with Pro for just $9.99/month',
        usagePercentage: 100
      };
    }
    
    // Warning when approaching limit
    const warningMessage = usage.shouldShowWarning 
      ? `You have ${usage.remaining} invoice${usage.remaining === 1 ? '' : 's'} left this month. Consider upgrading to Pro for unlimited access.`
      : undefined;
    
    return {
      canCreate: true,
      reason: warningMessage,
      remainingInvoices: usage.remaining,
      showUpgradePrompt: usage.shouldShowWarning,
      upgradeMessage: warningMessage,
      usagePercentage: usage.percentage
    };
    
  } catch (error) {
    console.error('Error checking invoice limit:', error);
    // In case of error, allow creation but log the issue
    return {
      canCreate: true,
      reason: 'Unable to verify invoice limit',
      remainingInvoices: undefined
    };
  }
}

/**
 * Get usage statistics for analytics dashboard
 * @param userId - User ID
 * @returns Usage statistics
 */
export async function getUsageStatistics(userId: string | ObjectId): Promise<{
  currentMonth: {
    used: number;
    limit: number;
    percentage: number;
    daysUntilReset: number;
  };
  lifetime: {
    totalInvoices: number;
    monthsActive: number;
    averagePerMonth: number;
  };
  subscription: {
    plan: 'free' | 'pro';
    status: string;
    nextResetDate: Date;
  };
}> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  const invoicesCollection = db.collection(COLLECTIONS.INVOICES);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  const user = await usersCollection.findOne({ _id: objectId });
  
  if (!user) {
    throw new Error('User not found');
  }
  
  // Current month stats
  const usage = await checkInvoiceLimit(userId);
  const now = new Date();
  const daysUntilReset = Math.ceil((usage.resetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  // Lifetime stats
  const totalInvoices = await invoicesCollection.countDocuments({ userId: objectId });
  const accountAge = now.getTime() - user.createdAt.getTime();
  const monthsActive = Math.max(1, Math.floor(accountAge / (30 * 24 * 60 * 60 * 1000)));
  const averagePerMonth = totalInvoices / monthsActive;
  
  return {
    currentMonth: {
      used: usage.used,
      limit: usage.limit,
      percentage: usage.percentage,
      daysUntilReset
    },
    lifetime: {
      totalInvoices,
      monthsActive,
      averagePerMonth: Math.round(averagePerMonth * 10) / 10
    },
    subscription: {
      plan: user.subscription.plan,
      status: user.subscription.subscriptionStatus || 'active',
      nextResetDate: usage.resetDate
    }
  };
}

/**
 * Batch check multiple users for approaching limits (for notifications)
 * @param userIds - Array of user IDs
 * @returns Users approaching or at limit
 */
export async function checkUsersApproachingLimit(userIds: ObjectId[]): Promise<Array<{
  userId: ObjectId;
  email: string;
  used: number;
  limit: number;
  remaining: number;
  shouldNotify: boolean;
}>> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const users = await usersCollection.find({
    _id: { $in: userIds },
    'subscription.plan': 'free'
  }).toArray();
  
  const results = [];
  
  for (const user of users) {
    const usage = await checkInvoiceLimit(user._id!);
    
    // Notify at 67% usage or when limit reached
    const shouldNotify = usage.percentage >= 67;
    
    if (shouldNotify) {
      results.push({
        userId: user._id!,
        email: user.email,
        used: usage.used,
        limit: usage.limit,
        remaining: usage.remaining,
        shouldNotify
      });
    }
  }
  
  return results;
}

/**
 * Automated monthly reset job (to be called by cron/scheduler)
 * Resets all users whose reset date has passed
 */
export async function processMonthlyResets(): Promise<{
  processed: number;
  errors: number;
}> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const now = new Date();
  let processed = 0;
  let errors = 0;
  
  // Find all users whose reset date has passed
  const usersToReset = await usersCollection.find({
    'subscription.resetDate': { $lte: now }
  }).toArray();
  
  console.log(`Processing monthly resets for ${usersToReset.length} users`);
  
  for (const user of usersToReset) {
    try {
      await resetMonthlyCount(user._id!);
      processed++;
    } catch (error) {
      console.error(`Error resetting usage for user ${user._id}:`, error);
      errors++;
    }
  }
  
  console.log(`Monthly reset complete: ${processed} processed, ${errors} errors`);
  
  return { processed, errors };
}

// Export helper functions
export {
  shouldResetMonthlyUsage,
  calculateNextResetDate,
  getInvoiceLimitForPlan
};