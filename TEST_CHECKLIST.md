# Template Invoice - Test Checklist for localhost:3000

## Pre-Launch Setup

### 1. Environment Variables (.env.local)
Create `.env.local` file with these values updated to port 3000:
```
NEXTAUTH_URL=http://localhost:3000
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

### 2. Google OAuth Console Configuration
Update your Google Cloud Console OAuth settings:
- **Authorized JavaScript origins**: Add `http://localhost:3000`
- **Authorized redirect URIs**: Add `http://localhost:3000/api/auth/callback/google`

## Starting the Server

### Option 1: Use npm script (recommended)
```bash
npm run dev
```

### Option 2: Use the test script
```bash
./test-site.sh
```

### Option 3: Manual port specification
```bash
npx next dev -p 3000
```

## Testing Checklist

### Basic Functionality
- [ ] Server starts on http://localhost:3000
- [ ] Homepage loads without errors
- [ ] Navigation menu works properly
- [ ] Footer links are functional

### Template Features
- [ ] Templates page shows all templates
- [ ] Template preview works
- [ ] Template comparison feature works
- [ ] AI template generation shows demo

### Authentication
- [ ] Google Sign-in button appears
- [ ] OAuth redirect works (check redirect URI)
- [ ] User session persists after sign-in
- [ ] Sign-out functionality works

### Invoice Creation
- [ ] Create invoice page loads
- [ ] Form validation works
- [ ] Template selection works
- [ ] Preview updates in real-time

### API Routes
- [ ] `/api/auth/[...nextauth]` - Authentication endpoints
- [ ] `/api/invoices` - Invoice CRUD operations
- [ ] `/api/templates/preview` - Template preview generation
- [ ] `/api/clients` - Client management

### Common Issues & Solutions

#### Port 3000 Already in Use
```bash
# Find process using port
lsof -i :3000

# Kill process
lsof -ti:3000 | xargs kill -9
```

#### Google OAuth Error: redirect_uri_mismatch
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 Client ID
4. Add `http://localhost:3000/api/auth/callback/google` to redirect URIs

#### NextAuth Errors
- Verify `NEXTAUTH_SECRET` is set in `.env.local`
- Check `NEXTAUTH_URL=http://localhost:3000`
- Restart server after changing environment variables

#### MongoDB Connection Issues
- Verify `MONGODB_URI` is correct
- Check MongoDB Atlas IP whitelist includes your IP
- Ensure database user has correct permissions

## Browser Console Checks
Open Developer Tools (F12) and check:
- [ ] No 404 errors for static assets
- [ ] No CORS errors
- [ ] No authentication errors
- [ ] API calls use correct base URL (http://localhost:3000)

## Performance Checks
- [ ] Page load time < 3 seconds
- [ ] Templates load quickly
- [ ] No memory leaks in console
- [ ] Images load properly

## Mobile Responsiveness
- [ ] Test in mobile view (F12 > Toggle device)
- [ ] Navigation menu works on mobile
- [ ] Forms are usable on mobile
- [ ] Templates display correctly

## Final Verification
- [ ] All environment variables match port 3000
- [ ] No TypeScript errors in terminal
- [ ] No build warnings
- [ ] Application is ready for use