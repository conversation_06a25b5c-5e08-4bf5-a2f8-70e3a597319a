'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  <PERSON>R<PERSON>,
  ArrowLeft,
  Zap,
  Upload,
  Mail,
  Phone,
  MapPin,
  User,
  Building,
  DollarSign,
  Clock,
  Send,
  CheckCircle,
  Sparkles,
  Download,
  Copy
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { TARGET_INDUSTRIES } from '@/components/templates/TargetIndustries'

interface QuickInvoiceData {
  // Step 1 - Quick Setup
  businessName: string
  industry: string
  logo?: File
  email: string
  phone: string
  paymentTerms: string
  
  // Step 2 - Invoice Details  
  clientName: string
  clientEmail: string
  serviceDescription: string
  pricingType: 'hourly' | 'flat' | 'per_item'
  rate: number
  quantity: number
  dueDate: string
  
  // Calculated
  total: number
}

const initialData: QuickInvoiceData = {
  businessName: '',
  industry: '',
  email: '',
  phone: '',
  paymentTerms: '15 days',
  clientName: '',
  clientEmail: '',
  serviceDescription: '',
  pricingType: 'flat',
  rate: 0,
  quantity: 1,
  dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  total: 0
}

export default function SimplifiedFlow() {
  const [currentStep, setCurrentStep] = useState(1)
  const [invoiceData, setInvoiceData] = useState<QuickInvoiceData>(initialData)
  const [isGenerating, setIsGenerating] = useState(false)

  // Auto-calculate total
  useEffect(() => {
    const total = invoiceData.rate * invoiceData.quantity
    setInvoiceData(prev => ({ ...prev, total }))
  }, [invoiceData.rate, invoiceData.quantity])

  // Auto-suggest template based on industry
  const getIndustryTemplate = (industry: string) => {
    const templates: { [key: string]: { services: string[], defaultRate: number, suggestedTerms: string } } = {
      freelancers: {
        services: ['Content Writing', 'Web Development', 'Graphic Design', 'Virtual Assistant'],
        defaultRate: 75,
        suggestedTerms: '15 days'
      },
      creative: {
        services: ['Logo Design', 'Photography Session', 'Video Production', 'Website Design'],
        defaultRate: 1500,
        suggestedTerms: '50% upfront, 50% on delivery'
      },
      trades: {
        services: ['Home Repair', 'Cleaning Service', 'Landscaping', 'Personal Training Session'],
        defaultRate: 50,
        suggestedTerms: 'Due on completion'
      },
      local_services: {
        services: ['Tutoring Session', 'Pet Sitting', 'Event Planning', 'Catering'],
        defaultRate: 40,
        suggestedTerms: '7 days'
      }
    }
    return templates[industry] || templates.freelancers
  }

  const handleIndustryChange = (industry: string) => {
    const template = getIndustryTemplate(industry)
    setInvoiceData(prev => ({
      ...prev,
      industry,
      rate: template.defaultRate,
      paymentTerms: template.suggestedTerms
    }))
  }

  const generateInvoice = async () => {
    setIsGenerating(true)
    // Simulate AI generation delay
    await new Promise(resolve => setTimeout(resolve, 1500))
    setIsGenerating(false)
    setCurrentStep(3)
  }

  const steps = [
    { number: 1, title: 'Quick Setup', duration: '30 seconds' },
    { number: 2, title: 'Invoice Details', duration: '2 minutes' },
    { number: 3, title: 'Send & Get Paid', duration: '30 seconds' }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Create Professional Invoice
          </h1>
          <p className="text-xl text-gray-600">
            From setup to sent in under 3 minutes
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-12">
          <div className="flex items-center justify-between max-w-2xl mx-auto">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className={`flex flex-col items-center ${
                  currentStep >= step.number ? 'text-blue-600' : 'text-gray-400'
                }`}>
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 ${
                    currentStep >= step.number 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 text-gray-500'
                  }`}>
                    {currentStep > step.number ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      step.number
                    )}
                  </div>
                  <div className="text-center">
                    <p className="font-medium text-sm">{step.title}</p>
                    <p className="text-xs opacity-75">{step.duration}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`h-px w-24 mx-4 ${
                    currentStep > step.number ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Quick Setup */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="max-w-2xl mx-auto"
            >
              <Card className="p-8">
                <div className="text-center mb-8">
                  <Zap className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Quick Setup
                  </h2>
                  <p className="text-gray-600">
                    Just the essentials to get you started
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Business Name */}
                  <Input
                    label="Business Name *"
                    leftIcon={<Building className="w-4 h-4" />}
                    value={invoiceData.businessName}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, businessName: e.target.value }))}
                    placeholder="Your business or personal name"
                  />

                  {/* Industry Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      What type of business are you? *
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      {Object.entries(TARGET_INDUSTRIES).map(([key, industry]) => {
                        const Icon = industry.icon
                        return (
                          <button
                            key={key}
                            onClick={() => handleIndustryChange(key)}
                            className={`p-4 rounded-lg border-2 transition-all text-left ${
                              invoiceData.industry === key
                                ? 'border-blue-600 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <div className="flex items-center gap-3">
                              <Icon className="w-5 h-5 text-gray-600" />
                              <div>
                                <p className="font-medium text-gray-900">{industry.title}</p>
                                <p className="text-xs text-gray-500">{industry.description.split(',')[0]}</p>
                              </div>
                            </div>
                          </button>
                        )
                      })}
                    </div>
                  </div>

                  {/* Contact Info */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <Input
                      label="Email Address *"
                      leftIcon={<Mail className="w-4 h-4" />}
                      type="email"
                      value={invoiceData.email}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                    <Input
                      label="Phone Number"
                      leftIcon={<Phone className="w-4 h-4" />}
                      value={invoiceData.phone}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="(*************"
                    />
                  </div>

                  {/* Payment Terms */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Terms
                    </label>
                    <select
                      value={invoiceData.paymentTerms}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, paymentTerms: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="Due immediately">Due Immediately</option>
                      <option value="7 days">7 Days</option>
                      <option value="15 days">15 Days</option>
                      <option value="30 days">30 Days</option>
                      <option value="50% upfront, 50% on delivery">50% Upfront, 50% on Delivery</option>
                    </select>
                  </div>

                  <Button
                    onClick={() => setCurrentStep(2)}
                    disabled={!invoiceData.businessName || !invoiceData.industry || !invoiceData.email}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    size="lg"
                  >
                    Continue to Invoice Details
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Invoice Details */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="max-w-3xl mx-auto"
            >
              <Card className="p-8">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Invoice Details
                  </h2>
                  <p className="text-gray-600">
                    Tell us about this specific project or service
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  {/* Left Column - Client & Service */}
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <User className="w-5 h-5" />
                      Client Information
                    </h3>

                    <Input
                      label="Client Name *"
                      value={invoiceData.clientName}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, clientName: e.target.value }))}
                      placeholder="Client or company name"
                    />

                    <Input
                      label="Client Email *"
                      type="email"
                      value={invoiceData.clientEmail}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, clientEmail: e.target.value }))}
                      placeholder="<EMAIL>"
                    />

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Service Description *
                      </label>
                      <textarea
                        value={invoiceData.serviceDescription}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, serviceDescription: e.target.value }))}
                        placeholder="Describe the work you're billing for..."
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        rows={4}
                      />
                    </div>

                    {/* Smart suggestions based on industry */}
                    {invoiceData.industry && (
                      <div className="bg-blue-50 rounded-lg p-4">
                        <p className="text-sm font-medium text-blue-900 mb-2">
                          💡 Common {(TARGET_INDUSTRIES as any)[invoiceData.industry]?.title} services:
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {getIndustryTemplate(invoiceData.industry).services.map((service) => (
                            <button
                              key={service}
                              onClick={() => setInvoiceData(prev => ({ ...prev, serviceDescription: service }))}
                              className="px-3 py-1 bg-white text-blue-700 rounded-full text-sm hover:bg-blue-100 transition-colors"
                            >
                              {service}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Right Column - Pricing */}
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <DollarSign className="w-5 h-5" />
                      Pricing Details
                    </h3>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Pricing Type
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {[
                          { value: 'hourly', label: 'Hourly' },
                          { value: 'flat', label: 'Flat Rate' },
                          { value: 'per_item', label: 'Per Item' }
                        ].map((type) => (
                          <button
                            key={type.value}
                            onClick={() => setInvoiceData(prev => ({ ...prev, pricingType: type.value as any }))}
                            className={`p-3 rounded-lg border-2 transition-all ${
                              invoiceData.pricingType === type.value
                                ? 'border-blue-600 bg-blue-50 text-blue-700'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            {type.label}
                          </button>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        label={
                          invoiceData.pricingType === 'hourly' ? 'Hourly Rate' :
                          invoiceData.pricingType === 'flat' ? 'Total Amount' :
                          'Price Per Item'
                        }
                        type="number"
                        value={invoiceData.rate}
                        onChange={(e) => setInvoiceData(prev => ({ ...prev, rate: parseFloat(e.target.value) || 0 }))}
                        placeholder="0.00"
                      />

                      {invoiceData.pricingType !== 'flat' && (
                        <Input
                          label={
                            invoiceData.pricingType === 'hourly' ? 'Hours' : 'Quantity'
                          }
                          type="number"
                          value={invoiceData.quantity}
                          onChange={(e) => setInvoiceData(prev => ({ ...prev, quantity: parseFloat(e.target.value) || 1 }))}
                          placeholder="1"
                        />
                      )}
                    </div>

                    <Input
                      label="Due Date"
                      type="date"
                      value={invoiceData.dueDate}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, dueDate: e.target.value }))}
                    />

                    {/* Total Preview */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-medium text-gray-700">Total Amount:</span>
                        <span className="text-2xl font-bold text-green-600">
                          ${invoiceData.total.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Navigation */}
                <div className="flex gap-4 mt-8">
                  <Button
                    onClick={() => setCurrentStep(1)}
                    variant="outline"
                    className="flex-1"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Setup
                  </Button>
                  <Button
                    onClick={generateInvoice}
                    disabled={!invoiceData.clientName || !invoiceData.clientEmail || !invoiceData.serviceDescription || invoiceData.rate <= 0}
                    className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                  >
                    {isGenerating ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="mr-2"
                        >
                          <Sparkles className="w-4 h-4" />
                        </motion.div>
                        Generating Invoice...
                      </>
                    ) : (
                      <>
                        Generate Invoice
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </>
                    )}
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}

          {/* Step 3: Success */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="max-w-2xl mx-auto"
            >
              <Card className="p-8 text-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                  className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
                >
                  <CheckCircle className="w-12 h-12 text-green-600" />
                </motion.div>

                <h2 className="text-3xl font-bold text-gray-900 mb-2">
                  Invoice Ready!
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  Your professional invoice is generated and ready to send
                </p>

                {/* Invoice Preview */}
                <div className="bg-gray-50 rounded-lg p-6 mb-8 text-left">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-bold text-gray-900">INVOICE</h3>
                      <p className="text-gray-600">From: {invoiceData.businessName}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-green-600">${invoiceData.total.toFixed(2)}</p>
                      <p className="text-sm text-gray-600">Due: {invoiceData.dueDate}</p>
                    </div>
                  </div>
                  <div className="border-t pt-4">
                    <p className="font-medium text-gray-900">Bill To: {invoiceData.clientName}</p>
                    <p className="text-gray-600 mt-2">{invoiceData.serviceDescription}</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <Button
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                      size="lg"
                    >
                      <Send className="w-5 h-5 mr-2" />
                      Send to Client
                    </Button>
                    <Button
                      variant="outline"
                      size="lg"
                    >
                      <Download className="w-5 h-5 mr-2" />
                      Download PDF
                    </Button>
                  </div>

                  <Button
                    onClick={() => {
                      setInvoiceData(initialData)
                      setCurrentStep(1)
                    }}
                    variant="outline"
                    className="w-full"
                  >
                    Create Another Invoice
                  </Button>
                </div>

                {/* Time Saved */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="mt-8 pt-8 border-t border-gray-200"
                >
                  <div className="flex items-center justify-center gap-4 text-green-600">
                    <Clock className="w-5 h-5" />
                    <span className="font-semibold">Completed in 2 minutes 30 seconds</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-2">
                    You saved 25+ minutes compared to manual invoice creation
                  </p>
                </motion.div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}