import Link from 'next/link'
import { ArrowLeft, HelpCircle, MessageSquare, Mail, Book, Video, Users } from 'lucide-react'

export default function SupportPage() {
  const faqs = [
    {
      question: "How do I create my first invoice?",
      answer: "Simply click on 'Create Invoice' button, choose a template, fill in your business and client details, add line items, and generate your professional invoice."
    },
    {
      question: "Can I customize the invoice templates?",
      answer: "Yes! Our templates are fully customizable. You can change colors, fonts, add your logo, and modify the layout to match your brand."
    },
    {
      question: "How do I send invoices to clients?",
      answer: "After creating your invoice, you can send it directly via email from our platform or download the PDF to send through your preferred method."
    },
    {
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards, PayPal, and bank transfers. For Pro and Business plans, we also support Stripe integration for client payments."
    },
    {
      question: "Is my data secure?",
      answer: "Absolutely. We use industry-standard encryption and security measures to protect your data. All information is stored securely and never shared with third parties."
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <Link 
            href="/" 
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>
        </div>
        
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">How can we help you?</h1>
          <p className="text-xl text-gray-600">
            Find answers to common questions or get in touch with our support team
          </p>
        </div>
        
        {/* Support Options */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="bg-white rounded-lg shadow-sm p-6 text-center">
            <MessageSquare className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Live Chat</h3>
            <p className="text-gray-600 mb-4">
              Get instant help from our support team
            </p>
            <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Start Chat
            </button>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 text-center">
            <Mail className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Email Support</h3>
            <p className="text-gray-600 mb-4">
              Send us a detailed message
            </p>
            <Link 
              href="/contact"
              className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors inline-block"
            >
              Send Email
            </Link>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 text-center">
            <Book className="w-12 h-12 text-purple-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Documentation</h3>
            <p className="text-gray-600 mb-4">
              Browse our comprehensive guides
            </p>
            <Link 
              href="/guides"
              className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors inline-block"
            >
              View Docs
            </Link>
          </div>
        </div>
        
        {/* Quick Links */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Links</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link href="/create" className="flex items-center space-x-3 text-gray-700 hover:text-blue-600 transition-colors">
              <Video className="w-5 h-5" />
              <span>Getting Started</span>
            </Link>
            <Link href="/templates" className="flex items-center space-x-3 text-gray-700 hover:text-blue-600 transition-colors">
              <Book className="w-5 h-5" />
              <span>Template Guide</span>
            </Link>
            <Link href="/pricing" className="flex items-center space-x-3 text-gray-700 hover:text-blue-600 transition-colors">
              <Users className="w-5 h-5" />
              <span>Billing Help</span>
            </Link>
            <Link href="/contact" className="flex items-center space-x-3 text-gray-700 hover:text-blue-600 transition-colors">
              <HelpCircle className="w-5 h-5" />
              <span>Technical Issues</span>
            </Link>
          </div>
        </div>
        
        {/* FAQ Section */}
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="border-b border-gray-200 pb-6 last:border-b-0 last:pb-0">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                  <HelpCircle className="w-5 h-5 text-blue-600 mr-2" />
                  {faq.question}
                </h3>
                <p className="text-gray-600 ml-7">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
        
        {/* Contact Section */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Still need help?
          </h3>
          <p className="text-gray-600 mb-6">
            Our support team is here to help you succeed with your invoicing needs.
          </p>
          <Link 
            href="/contact"
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-block"
          >
            Contact Support
          </Link>
        </div>
      </div>
    </div>
  )
}