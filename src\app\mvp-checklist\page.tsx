'use client'

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  CheckCircle2, 
  Circle, 
  AlertCircle, 
  XCircle,
  RefreshCw,
  PlayCircle,
  FileText,
  Mail,
  CreditCard,
  Smartphone,
  Zap,
  Users,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestResult {
  status: 'pending' | 'testing' | 'passed' | 'failed' | 'warning';
  message?: string;
  details?: string;
}

interface TestCategory {
  name: string;
  icon: React.ReactNode;
  tests: {
    id: string;
    name: string;
    description: string;
    critical: boolean;
    test: () => Promise<TestResult>;
  }[];
}

export default function MVPChecklistPage() {
  const { data: session } = useSession();
  const [results, setResults] = useState<Record<string, TestResult>>({});
  const [testing, setTesting] = useState(false);
  const [currentTest, setCurrentTest] = useState<string | null>(null);

  const testCategories: TestCategory[] = [
    {
      name: 'User Authentication',
      icon: <Users className="w-5 h-5" />,
      tests: [
        {
          id: 'auth-signup',
          name: 'User can sign up',
          description: 'Test user registration flow',
          critical: true,
          test: async () => {
            try {
              const response = await fetch('/api/auth/providers');
              if (response.ok) {
                return { status: 'passed', message: 'Auth providers available' };
              }
              return { status: 'failed', message: 'Auth providers not configured' };
            } catch (error) {
              return { status: 'failed', message: 'Auth system error', details: error?.toString() };
            }
          }
        },
        {
          id: 'auth-session',
          name: 'Session management works',
          description: 'Verify session persistence',
          critical: true,
          test: async () => {
            if (session?.user) {
              return { status: 'passed', message: 'Session active' };
            }
            return { status: 'warning', message: 'No active session (sign in to test)' };
          }
        }
      ]
    },
    {
      name: 'AI Template Generation',
      icon: <Zap className="w-5 h-5" />,
      tests: [
        {
          id: 'ai-health',
          name: 'AI service is accessible',
          description: 'Check AI service connectivity',
          critical: true,
          test: async () => {
            try {
              // Test with a simple prompt
              const testData = {
                businessDescription: 'Test web development company',
                targetIndustry: 'technology',
                invoiceType: 'service'
              };

              const response = await fetch('/api/ai/test', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(testData)
              });

              if (response.ok) {
                return { status: 'passed', message: 'AI service responding' };
              } else if (response.status === 404) {
                // If test endpoint doesn't exist, check for API key
                if (process.env.NEXT_PUBLIC_AI_CONFIGURED === 'true') {
                  return { status: 'passed', message: 'AI configured (assumed working)' };
                }
                return { status: 'warning', message: 'AI test endpoint not found' };
              }
              return { status: 'failed', message: `AI service error: ${response.status}` };
            } catch (error) {
              return { status: 'failed', message: 'AI service unreachable', details: error?.toString() };
            }
          }
        },
        {
          id: 'ai-generation',
          name: 'Template generation works',
          description: 'Verify AI can generate templates',
          critical: true,
          test: async () => {
            // This would be tested through the actual invoice creation flow
            return { 
              status: 'warning', 
              message: 'Manual test required',
              details: 'Create an invoice to test AI generation'
            };
          }
        }
      ]
    },
    {
      name: 'Invoice Creation',
      icon: <FileText className="w-5 h-5" />,
      tests: [
        {
          id: 'invoice-form',
          name: 'Invoice form is accessible',
          description: 'Check invoice creation page loads',
          critical: true,
          test: async () => {
            try {
              const response = await fetch('/create-invoice', { method: 'HEAD' });
              if (response.ok || response.status === 308) {
                return { status: 'passed', message: 'Invoice form accessible' };
              }
              return { status: 'failed', message: `Form not accessible: ${response.status}` };
            } catch (error) {
              return { status: 'failed', message: 'Cannot access invoice form' };
            }
          }
        },
        {
          id: 'invoice-save',
          name: 'Invoices can be saved',
          description: 'Test invoice persistence',
          critical: true,
          test: async () => {
            if (!session?.user) {
              return { status: 'warning', message: 'Sign in required to test' };
            }

            try {
              const response = await fetch('/api/invoices');
              if (response.ok) {
                const data = await response.json();
                return { 
                  status: 'passed', 
                  message: 'Invoice API working',
                  details: `${data.total || 0} invoices found`
                };
              }
              return { status: 'failed', message: 'Invoice API error' };
            } catch (error) {
              return { status: 'failed', message: 'Cannot access invoice API' };
            }
          }
        }
      ]
    },
    {
      name: 'PDF Generation',
      icon: <Download className="w-5 h-5" />,
      tests: [
        {
          id: 'pdf-service',
          name: 'PDF service is configured',
          description: 'Check PDF generation capability',
          critical: true,
          test: async () => {
            // Check if puppeteer is installed
            try {
              const hasPuppeteer = typeof window === 'undefined'; // Server-side check
              if (hasPuppeteer) {
                return { status: 'passed', message: 'PDF service available' };
              }
              return { status: 'warning', message: 'Client-side test - verify on server' };
            } catch (error) {
              return { status: 'failed', message: 'PDF service check failed' };
            }
          }
        },
        {
          id: 'pdf-generation',
          name: 'PDFs generate correctly',
          description: 'Test PDF output quality',
          critical: true,
          test: async () => {
            return { 
              status: 'warning', 
              message: 'Manual test required',
              details: 'Generate and download a PDF from an invoice'
            };
          }
        }
      ]
    },
    {
      name: 'Email Functionality',
      icon: <Mail className="w-5 h-5" />,
      tests: [
        {
          id: 'email-config',
          name: 'Email service is configured',
          description: 'Check SMTP configuration',
          critical: true,
          test: async () => {
            try {
              const response = await fetch('/api/email/test', { method: 'POST' });
              if (response.ok) {
                return { status: 'passed', message: 'Email service configured' };
              } else if (response.status === 404) {
                // Check env variable as fallback
                if (process.env.NEXT_PUBLIC_EMAIL_CONFIGURED === 'true') {
                  return { status: 'passed', message: 'Email configured (assumed working)' };
                }
                return { status: 'warning', message: 'Email test endpoint not found' };
              }
              return { status: 'failed', message: 'Email service not configured' };
            } catch (error) {
              return { status: 'failed', message: 'Email service check failed' };
            }
          }
        },
        {
          id: 'email-sending',
          name: 'Emails send successfully',
          description: 'Verify email delivery',
          critical: true,
          test: async () => {
            return { 
              status: 'warning', 
              message: 'Manual test required',
              details: 'Send an invoice via email to test'
            };
          }
        }
      ]
    },
    {
      name: 'Payment Links',
      icon: <CreditCard className="w-5 h-5" />,
      tests: [
        {
          id: 'stripe-config',
          name: 'Stripe is configured',
          description: 'Check payment processor setup',
          critical: true,
          test: async () => {
            try {
              const response = await fetch('/api/stripe/config');
              if (response.ok) {
                const data = await response.json();
                if (data.configured) {
                  return { status: 'passed', message: 'Stripe configured' };
                }
              } else if (response.status === 404) {
                if (process.env.NEXT_PUBLIC_STRIPE_CONFIGURED === 'true') {
                  return { status: 'passed', message: 'Stripe configured (assumed working)' };
                }
              }
              return { status: 'warning', message: 'Stripe not configured (optional for MVP)' };
            } catch (error) {
              return { status: 'warning', message: 'Cannot verify Stripe config' };
            }
          }
        },
        {
          id: 'payment-links',
          name: 'Payment links generate',
          description: 'Test payment link creation',
          critical: false,
          test: async () => {
            return { 
              status: 'warning', 
              message: 'Manual test required',
              details: 'Create invoice with payment link to test'
            };
          }
        }
      ]
    },
    {
      name: 'Mobile Experience',
      icon: <Smartphone className="w-5 h-5" />,
      tests: [
        {
          id: 'mobile-responsive',
          name: 'UI is mobile responsive',
          description: 'Check responsive design',
          critical: true,
          test: async () => {
            if (typeof window !== 'undefined' && window.innerWidth < 768) {
              return { status: 'passed', message: 'Currently on mobile device' };
            }
            return { 
              status: 'warning', 
              message: 'Test on mobile device',
              details: 'Resize browser or use mobile to verify'
            };
          }
        },
        {
          id: 'mobile-invoice',
          name: 'Invoice creation works on mobile',
          description: 'Test mobile invoice flow',
          critical: true,
          test: async () => {
            return { 
              status: 'warning', 
              message: 'Manual test required',
              details: 'Create invoice on mobile device'
            };
          }
        }
      ]
    }
  ];

  const runTest = async (testId: string) => {
    setCurrentTest(testId);
    setResults(prev => ({ ...prev, [testId]: { status: 'testing' } }));

    // Find the test
    let testToRun: any;
    for (const category of testCategories) {
      const test = category.tests.find(t => t.id === testId);
      if (test) {
        testToRun = test;
        break;
      }
    }

    if (testToRun) {
      const result = await testToRun.test();
      setResults(prev => ({ ...prev, [testId]: result }));
    }

    setCurrentTest(null);
  };

  const runAllTests = async () => {
    setTesting(true);
    
    for (const category of testCategories) {
      for (const test of category.tests) {
        await runTest(test.id);
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    setTesting(false);
  };

  const getOverallStatus = () => {
    const allTests = testCategories.flatMap(c => c.tests);
    const criticalTests = allTests.filter(t => t.critical);
    
    const criticalResults = criticalTests.map(t => results[t.id]);
    const failedCritical = criticalResults.filter(r => r?.status === 'failed').length;
    const passedCritical = criticalResults.filter(r => r?.status === 'passed').length;
    
    if (failedCritical > 0) return 'failed';
    if (passedCritical === criticalTests.length) return 'passed';
    return 'pending';
  };

  const getStatusIcon = (status?: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle2 className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'testing':
        return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <Circle className="w-5 h-5 text-gray-400" />;
    }
  };

  const overallStatus = getOverallStatus();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">MVP Launch Checklist</h1>
              <p className="text-gray-600 mt-2">Verify all core features are working before launch</p>
            </div>
            <button
              onClick={runAllTests}
              disabled={testing}
              className={cn(
                "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors",
                testing
                  ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                  : "bg-purple-600 text-white hover:bg-purple-700"
              )}
            >
              <PlayCircle className="w-5 h-5" />
              <span>{testing ? 'Testing...' : 'Run All Tests'}</span>
            </button>
          </div>

          {/* Overall Status */}
          <div className={cn(
            "p-4 rounded-lg",
            overallStatus === 'passed' ? "bg-green-50 text-green-800" :
            overallStatus === 'failed' ? "bg-red-50 text-red-800" :
            "bg-gray-50 text-gray-800"
          )}>
            <div className="flex items-center space-x-3">
              {overallStatus === 'passed' ? <CheckCircle2 className="w-6 h-6" /> :
               overallStatus === 'failed' ? <XCircle className="w-6 h-6" /> :
               <AlertCircle className="w-6 h-6" />}
              <div>
                <p className="font-semibold">
                  {overallStatus === 'passed' ? 'All critical tests passed!' :
                   overallStatus === 'failed' ? 'Some critical tests failed' :
                   'Tests not yet completed'}
                </p>
                <p className="text-sm">
                  {overallStatus === 'passed' ? 'Your MVP is ready for launch' :
                   overallStatus === 'failed' ? 'Fix critical issues before launching' :
                   'Run tests to verify MVP readiness'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Test Categories */}
        <div className="space-y-6">
          {testCategories.map((category) => (
            <div key={category.name} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <span className="text-gray-600">{category.icon}</span>
                  <h2 className="text-lg font-semibold text-gray-900">{category.name}</h2>
                </div>
              </div>
              
              <div className="divide-y divide-gray-200">
                {category.tests.map((test) => (
                  <div key={test.id} className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(results[test.id]?.status)}
                          <h3 className="font-medium text-gray-900">
                            {test.name}
                            {test.critical && (
                              <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                Critical
                              </span>
                            )}
                          </h3>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{test.description}</p>
                        
                        {results[test.id] && results[test.id].status !== 'pending' && (
                          <div className="mt-3">
                            <p className={cn(
                              "text-sm font-medium",
                              results[test.id].status === 'passed' ? "text-green-600" :
                              results[test.id].status === 'failed' ? "text-red-600" :
                              results[test.id].status === 'warning' ? "text-yellow-600" :
                              "text-gray-600"
                            )}>
                              {results[test.id].message}
                            </p>
                            {results[test.id].details && (
                              <p className="text-xs text-gray-500 mt-1">
                                {results[test.id].details}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <button
                        onClick={() => runTest(test.id)}
                        disabled={testing || currentTest === test.id}
                        className={cn(
                          "ml-4 p-2 rounded-lg transition-colors",
                          testing || currentTest === test.id
                            ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                            : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                        )}
                      >
                        <RefreshCw className={cn(
                          "w-4 h-4",
                          currentTest === test.id && "animate-spin"
                        )} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold mb-4">Launch Readiness Summary</h2>
          <div className="space-y-3">
            <SummaryItem
              label="Critical Tests"
              passed={testCategories.flatMap(c => c.tests.filter(t => t.critical)).filter(t => results[t.id]?.status === 'passed').length}
              total={testCategories.flatMap(c => c.tests.filter(t => t.critical)).length}
            />
            <SummaryItem
              label="Optional Tests"
              passed={testCategories.flatMap(c => c.tests.filter(t => !t.critical)).filter(t => results[t.id]?.status === 'passed').length}
              total={testCategories.flatMap(c => c.tests.filter(t => !t.critical)).length}
            />
            <SummaryItem
              label="Manual Tests Required"
              passed={0}
              total={testCategories.flatMap(c => c.tests).filter(t => results[t.id]?.status === 'warning').length}
              isWarning
            />
          </div>
        </div>
      </div>
    </div>
  );
}

function SummaryItem({ label, passed, total, isWarning }: { 
  label: string; 
  passed: number; 
  total: number; 
  isWarning?: boolean;
}) {
  const percentage = total > 0 ? (passed / total) * 100 : 0;
  
  return (
    <div>
      <div className="flex justify-between mb-1">
        <span className="text-sm text-gray-600">{label}</span>
        <span className={cn(
          "text-sm font-medium",
          isWarning ? "text-yellow-600" :
          percentage === 100 ? "text-green-600" :
          percentage === 0 ? "text-red-600" :
          "text-yellow-600"
        )}>
          {isWarning ? `${total} tests need manual verification` : `${passed} / ${total}`}
        </span>
      </div>
      {!isWarning && (
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={cn(
              "h-2 rounded-full transition-all",
              percentage === 100 ? "bg-green-500" :
              percentage === 0 ? "bg-red-500" :
              "bg-yellow-500"
            )}
            style={{ width: `${percentage}%` }}
          />
        </div>
      )}
    </div>
  );
}