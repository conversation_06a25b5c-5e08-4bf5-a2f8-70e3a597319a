import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserById } from '@/lib/user-service';
import { trackEvent, trackConversionFunnel } from '@/lib/analytics-service';
import { stripe, stripePriceId, isStripeConfigured, getStripeMode, isUsingTestKeys } from '@/lib/stripe-config';

export async function POST(request: NextRequest) {
  try {
    // Check if Stripe is configured
    if (!stripe || !isStripeConfigured()) {
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });
    }

    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await getUserById(session.user.id);
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is already Pro
    if (user.subscription.plan === 'pro') {
      return NextResponse.json({ error: 'User already has Pro subscription' }, { status: 400 });
    }

    const { priceId, successUrl, cancelUrl, source } = await request.json();

    // Debug logging for Stripe configuration
    console.log('🔍 Stripe Checkout Debug:', {
      mode: getStripeMode(),
      isTestMode: isUsingTestKeys(),
      priceIds: {
        fromRequest: priceId,
        fromConfig: stripePriceId,
        testPriceId: process.env.STRIPE_TEST_PRICE_ID,
        livePriceId: process.env.STRIPE_PRO_PRICE_ID,
        finalSelection: priceId || stripePriceId
      }
    });

    const userAgent = request.headers.get('user-agent') || undefined;
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     undefined;

    // Track upgrade attempt
    await trackConversionFunnel(session.user.id, 'upgrade_clicked', {
      source: source || 'unknown',
      userPlan: user.subscription.plan,
      invoicesUsed: user.subscription.invoicesUsed,
      priceId: priceId || stripePriceId,
    });

    await trackEvent(session.user.id, 'upgrade_clicked', {
      source: source || 'unknown',
      userPlan: user.subscription.plan,
      invoicesUsed: user.subscription.invoicesUsed,
      priceId: priceId || stripePriceId,
      attemptedCheckout: true,
    }, {
      userAgent,
      ipAddress,
      source: 'stripe_checkout_api',
    });

    // Create Stripe checkout session
    const checkoutSession = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId || stripePriceId,
          quantity: 1,
        },
      ],
      customer_email: user.email,
      client_reference_id: user._id?.toString(),
      success_url: successUrl || `${process.env.NEXTAUTH_URL}/dashboard?upgraded=true`,
      cancel_url: cancelUrl || `${process.env.NEXTAUTH_URL}/pricing`,
      metadata: {
        userId: user._id?.toString() || '',
        plan: 'pro',
        source: source || 'unknown',
      },
      subscription_data: {
        metadata: {
          userId: user._id?.toString() || '',
          plan: 'pro',
          source: source || 'unknown',
        },
      },
      allow_promotion_codes: true,
    });

    return NextResponse.json({
      sessionId: checkoutSession.id,
      url: checkoutSession.url,
    });

  } catch (error) {
    console.error('Stripe checkout error:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}