'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';

interface UsageIndicatorProps {
  variant?: 'compact' | 'detailed' | 'card';
  showUpgradeButton?: boolean;
  onUpgradeClick?: () => void;
  className?: string;
}

export default function UsageIndicator({ 
  variant = 'detailed', 
  showUpgradeButton = true,
  onUpgradeClick,
  className = '' 
}: UsageIndicatorProps) {
  const { data: session } = useSession();
  const [isHovered, setIsHovered] = useState(false);

  if (!session?.user?.subscription) return null;

  const { plan, invoicesUsed, resetDate } = session.user.subscription;
  const isPro = plan === 'pro';

  if (isPro && variant === 'compact') {
    return (
      <div className={`flex items-center text-sm ${className}`}>
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
          Pro
        </span>
        <span className="ml-2 text-gray-600">Unlimited</span>
      </div>
    );
  }

  if (isPro) {
    return (
      <div className={`bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">✓</span>
              </div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-900">Pro Plan Active</h3>
              <p className="text-sm text-green-700">Unlimited invoices & premium features</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-xs text-green-600">Next billing</p>
            <p className="text-sm font-medium text-green-900">
              {resetDate ? new Date(resetDate).toLocaleDateString() : 'Unknown'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Free plan logic
  const remaining = Math.max(0, 3 - invoicesUsed);
  const percentage = (invoicesUsed / 3) * 100;
  const isAtLimit = remaining === 0;
  const isNearLimit = remaining === 1;

  if (variant === 'compact') {
    return (
      <div className={`flex items-center text-sm ${className}`}>
        <div className={`w-2 h-2 rounded-full mr-2 ${
          isAtLimit ? 'bg-red-500' : isNearLimit ? 'bg-yellow-500' : 'bg-blue-500'
        }`}></div>
        <span className="text-gray-600">{remaining}/{3} left</span>
        {showUpgradeButton && isAtLimit && (
          <button
            onClick={onUpgradeClick}
            className="ml-2 text-xs text-blue-600 hover:text-blue-800 underline"
          >
            Upgrade
          </button>
        )}
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <div className={`bg-white border rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-900">Invoice Usage</h3>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            isAtLimit ? 'bg-red-100 text-red-800' :
            isNearLimit ? 'bg-yellow-100 text-yellow-800' :
            'bg-blue-100 text-blue-800'
          }`}>
            Free Plan
          </span>
        </div>
        
        <div className="mb-3">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>This month</span>
            <span>{invoicesUsed}/3 used</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                isAtLimit ? 'bg-red-500' : isNearLimit ? 'bg-yellow-500' : 'bg-blue-500'
              }`}
              style={{ width: `${Math.min(percentage, 100)}%` }}
            />
          </div>
        </div>

        {isAtLimit ? (
          <div className="text-center">
            <p className="text-sm text-red-600 mb-3">You've reached your monthly limit</p>
            {showUpgradeButton && (
              <button
                onClick={onUpgradeClick}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-3 rounded-md transition-colors"
              >
                Upgrade to Pro - $9.99/month
              </button>
            )}
          </div>
        ) : (
          <div>
            <p className={`text-sm mb-2 ${
              isNearLimit ? 'text-yellow-700' : 'text-gray-600'
            }`}>
              {remaining} invoice{remaining !== 1 ? 's' : ''} remaining this month
            </p>
            {isNearLimit && showUpgradeButton && (
              <button
                onClick={onUpgradeClick}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                className="w-full border border-gray-300 hover:border-blue-500 text-gray-700 hover:text-blue-700 text-sm font-medium py-2 px-3 rounded-md transition-colors"
              >
                {isHovered ? 'Upgrade to Pro' : 'Getting close?'}
              </button>
            )}
          </div>
        )}
        
        <div className="mt-3 pt-3 border-t border-gray-100">
          <p className="text-xs text-gray-500">
            Resets on {resetDate ? new Date(resetDate).toLocaleDateString() : 'Unknown'}
          </p>
        </div>
      </div>
    );
  }

  // Detailed variant (default)
  return (
    <div className={`bg-white border rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Monthly Usage</h3>
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
          isAtLimit ? 'bg-red-100 text-red-800' :
          isNearLimit ? 'bg-yellow-100 text-yellow-800' :
          'bg-blue-100 text-blue-800'
        }`}>
          Free Plan
        </span>
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Invoices</span>
          <span className={`text-sm font-medium ${
            isAtLimit ? 'text-red-600' : isNearLimit ? 'text-yellow-600' : 'text-blue-600'
          }`}>
            {invoicesUsed} / 3 used
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-500 ${
              isAtLimit ? 'bg-red-500' : isNearLimit ? 'bg-yellow-500' : 'bg-blue-500'
            }`}
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        </div>
        
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>0</span>
          <span>3</span>
        </div>
      </div>

      {isAtLimit ? (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="w-5 h-5 text-red-400">⚠️</div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Limit Reached</h3>
              <p className="text-sm text-red-700 mt-1">
                You've used all 3 invoices for this month. Upgrade to Pro for unlimited invoices.
              </p>
            </div>
          </div>
        </div>
      ) : isNearLimit ? (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="w-5 h-5 text-yellow-400">⚡</div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Almost There</h3>
              <p className="text-sm text-yellow-700 mt-1">
                You have {remaining} invoice remaining this month. Consider upgrading for unlimited access.
              </p>
            </div>
          </div>
        </div>
      ) : null}

      {showUpgradeButton && (
        <div className="space-y-3">
          <button
            onClick={onUpgradeClick}
            className={`w-full font-medium py-3 px-4 rounded-md transition-colors ${
              isAtLimit 
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'border border-gray-300 hover:border-blue-500 text-gray-700 hover:text-blue-700'
            }`}
          >
            {isAtLimit ? 'Upgrade to Pro - $9.99/month' : 'Upgrade for Unlimited Invoices'}
          </button>
          
          {!isAtLimit && (
            <div className="bg-blue-50 rounded-md p-3">
              <h4 className="text-sm font-medium text-blue-900 mb-1">Pro Benefits:</h4>
              <ul className="text-xs text-blue-800 space-y-1">
                <li>• Unlimited invoices</li>
                <li>• Premium templates</li>
                <li>• Priority support</li>
                <li>• Advanced analytics</li>
              </ul>
            </div>
          )}
        </div>
      )}

      <div className="mt-4 pt-4 border-t border-gray-100">
        <p className="text-sm text-gray-500">
          Usage resets on <span className="font-medium">
            {resetDate ? new Date(resetDate).toLocaleDateString() : 'Unknown'}
          </span>
        </p>
      </div>
    </div>
  );
}