'use client'

import { Mail, Phone, MapPin } from 'lucide-react'
import Link from 'next/link'

export default function Footer() {
  return (
    <footer className="bg-white/50 backdrop-blur-sm border-t">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid md:grid-cols-4 gap-8 mb-8">
          <div>
            <h3 className="font-extrabold text-2xl tracking-tighter text-black mb-4">
              <span className="bg-black text-white px-2 py-1 rounded">TI</span> Template Invoice
            </h3>
            <p className="text-gray-600">
              Making invoicing beautiful and simple for businesses worldwide.
            </p>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-4">Product</h4>
            <ul className="space-y-2">
              <li><Link href="/templates" className="text-gray-600 hover:text-gray-900 transition-colors">Templates</Link></li>
              <li><Link href="/pricing" className="text-gray-600 hover:text-gray-900 transition-colors">Pricing</Link></li>
              <li><Link href="/create" className="text-gray-600 hover:text-gray-900 transition-colors">Create Invoice</Link></li>
              <li><Link href="/my-invoices" className="text-gray-600 hover:text-gray-900 transition-colors">My Invoices</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-4">Company</h4>
            <ul className="space-y-2">
              <li><Link href="/about" className="text-gray-600 hover:text-gray-900 transition-colors">About</Link></li>
              <li><Link href="/contact" className="text-gray-600 hover:text-gray-900 transition-colors">Contact</Link></li>
              <li><Link href="/privacy" className="text-gray-600 hover:text-gray-900 transition-colors">Privacy</Link></li>
              <li><Link href="/terms" className="text-gray-600 hover:text-gray-900 transition-colors">Terms</Link></li>
              <li><Link href="/support" className="text-gray-600 hover:text-gray-900 transition-colors">Support</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-4">Contact</h4>
            <ul className="space-y-3">
              <li className="flex items-center gap-3 text-gray-600">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-center gap-3 text-gray-600">
                <Phone className="w-4 h-4" />
                <span>+****************</span>
              </li>
              <li className="flex items-start gap-3 text-gray-600">
                <MapPin className="w-4 h-4 mt-1" />
                <span>123 Business St.<br />San Francisco, CA 94107</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t pt-8 flex flex-col md:flex-row justify-between items-center text-gray-600">
          <p>&copy; {new Date().getFullYear()} Template Invoice. All rights reserved.</p>
          <div className="flex gap-6 mt-4 md:mt-0">
            <Link href="/terms" className="hover:text-gray-900 transition-colors">Terms</Link>
            <Link href="/privacy" className="hover:text-gray-900 transition-colors">Privacy</Link>
            <Link href="/contact" className="hover:text-gray-900 transition-colors">Contact</Link>
          </div>
        </div>
      </div>
    </footer>
  )
}