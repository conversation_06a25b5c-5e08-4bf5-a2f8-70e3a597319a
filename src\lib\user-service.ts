// User Service for Template Invoice System
import { ObjectId } from 'mongodb';
import clientPromise from './mongodb';
import { UserDocument, CreateUserInput, COLLECTIONS, ActivityLogDocument } from './models';

// Get database instance
async function getDatabase() {
  const client = await clientPromise;
  return client.db();
}

// Create or update user
export async function createUser(input: CreateUserInput): Promise<UserDocument> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  // Check if user already exists
  const existingUser = await usersCollection.findOne({ email: input.email });
  
  if (existingUser) {
    // Update existing user with new Google ID if needed
    const updateData: Partial<UserDocument> = {
      name: input.name,
      image: input.image,
      lastLogin: new Date()
    };
    
    if (existingUser.googleId !== input.googleId) {
      updateData.googleId = input.googleId;
    }
    
    await usersCollection.updateOne(
      { _id: existingUser._id },
      { $set: updateData }
    );
    
    return { ...existingUser, ...updateData };
  }
  
  // Create new user with free subscription
  const now = new Date();
  const resetDate = new Date();
  resetDate.setMonth(resetDate.getMonth() + 1, 1); // First day of next month
  
  const newUser: UserDocument = {
    email: input.email,
    name: input.name,
    googleId: input.googleId,
    image: input.image,
    subscription: {
      plan: 'free',
      invoicesUsed: 0,
      resetDate,
      ...input.subscription
    },
    createdAt: now,
    lastLogin: now,
    preferences: {
      defaultCurrency: 'USD',
      timezone: 'UTC'
    }
  };
  
  const result = await usersCollection.insertOne(newUser);
  
  // Log user creation
  await logActivity({
    userId: result.insertedId,
    action: 'user_created',
    resourceType: 'user',
    details: {
      description: 'New user account created',
      metadata: { provider: 'google' }
    }
  });
  
  return { ...newUser, _id: result.insertedId };
}

// Update user last login
export async function updateUserLastLogin(email: string): Promise<UserDocument | null> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const result = await usersCollection.findOneAndUpdate(
    { email },
    { 
      $set: { lastLogin: new Date() }
    },
    { returnDocument: 'after' }
  );
  
  return (result as any)?.value as UserDocument || null;
}

// Get user by ID
export async function getUserById(userId: string | ObjectId): Promise<UserDocument | null> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  return await usersCollection.findOne({ _id: objectId });
}

// Get user by email
export async function getUserByEmail(email: string): Promise<UserDocument | null> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  return await usersCollection.findOne({ email });
}

// Check if user can create invoice (freemium logic)
export async function canCreateInvoice(userId: string | ObjectId): Promise<{ canCreate: boolean; reason?: string; remainingInvoices?: number }> {
  const user = await getUserById(userId);
  
  if (!user) {
    return { canCreate: false, reason: 'User not found' };
  }
  
  // Pro users can always create invoices
  if (user.subscription.plan === 'pro') {
    return { canCreate: true };
  }
  
  // Check if reset date has passed for free users
  const now = new Date();
  if (now >= user.subscription.resetDate) {
    // Reset invoice count for new month
    await resetUserInvoiceCount(userId);
    return { canCreate: true, remainingInvoices: 2 }; // 3 total - 1 used = 2 remaining
  }
  
  // Check remaining invoices for free users
  const remainingInvoices = 3 - user.subscription.invoicesUsed;
  
  if (remainingInvoices <= 0) {
    return { 
      canCreate: false, 
      reason: 'Free plan limit reached. Upgrade to Pro for unlimited invoices.',
      remainingInvoices: 0
    };
  }
  
  return { canCreate: true, remainingInvoices };
}

// Increment user invoice count
export async function incrementUserInvoiceCount(userId: string | ObjectId): Promise<void> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  await usersCollection.updateOne(
    { _id: objectId },
    { $inc: { 'subscription.invoicesUsed': 1 } }
  );
  
  // Log invoice creation
  await logActivity({
    userId: objectId,
    action: 'invoice_created',
    resourceType: 'invoice',
    details: {
      description: 'Invoice created and count incremented'
    }
  });
}

// Reset user invoice count (monthly reset for free users)
export async function resetUserInvoiceCount(userId: string | ObjectId): Promise<void> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  const nextResetDate = new Date();
  nextResetDate.setMonth(nextResetDate.getMonth() + 1, 1);
  
  await usersCollection.updateOne(
    { _id: objectId },
    { 
      $set: { 
        'subscription.invoicesUsed': 0,
        'subscription.resetDate': nextResetDate
      } 
    }
  );
  
  // Log reset
  await logActivity({
    userId: objectId,
    action: 'invoice_count_reset',
    resourceType: 'user',
    details: {
      description: 'Monthly invoice count reset',
      metadata: { nextResetDate }
    }
  });
}

// Upgrade user to Pro
export async function upgradeUserToPro(userId: string | ObjectId, stripeCustomerId: string, stripeSubscriptionId: string): Promise<void> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  await usersCollection.updateOne(
    { _id: objectId },
    { 
      $set: { 
        'subscription.plan': 'pro',
        'subscription.stripeCustomerId': stripeCustomerId,
        'subscription.stripeSubscriptionId': stripeSubscriptionId,
        'subscription.subscriptionStatus': 'active'
      } 
    }
  );
  
  // Log upgrade
  await logActivity({
    userId: objectId,
    action: 'subscription_upgraded',
    resourceType: 'user',
    details: {
      description: 'User upgraded to Pro plan',
      metadata: { 
        plan: 'pro',
        stripeCustomerId,
        stripeSubscriptionId 
      }
    }
  });
}

// Downgrade user to Free (on subscription cancellation)
export async function downgradeUserToFree(userId: string | ObjectId): Promise<void> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Reset to free plan with current month's usage intact
  await usersCollection.updateOne(
    { _id: objectId },
    { 
      $set: { 
        'subscription.plan': 'free',
        'subscription.subscriptionStatus': 'cancelled'
      },
      $unset: {
        'subscription.stripeCustomerId': '',
        'subscription.stripeSubscriptionId': ''
      }
    }
  );
  
  // Log downgrade
  await logActivity({
    userId: objectId,
    action: 'subscription_downgraded',
    resourceType: 'user',
    details: {
      description: 'User downgraded to Free plan'
    }
  });
}

// Update user preferences
export async function updateUserPreferences(userId: string | ObjectId, preferences: Partial<UserDocument['preferences']>): Promise<void> {
  const db = await getDatabase();
  const usersCollection = db.collection<UserDocument>(COLLECTIONS.USERS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  await usersCollection.updateOne(
    { _id: objectId },
    { 
      $set: { 
        ...Object.fromEntries(
          Object.entries(preferences || {}).map(([key, value]) => [`preferences.${key}`, value])
        )
      } 
    }
  );
}

// Get user subscription status
export async function getUserSubscriptionStatus(userId: string | ObjectId) {
  const user = await getUserById(userId);
  
  if (!user) {
    return null;
  }
  
  const now = new Date();
  const resetDate = user.subscription.resetDate;
  const remainingInvoices = user.subscription.plan === 'pro' ? 'unlimited' : Math.max(0, 3 - user.subscription.invoicesUsed);
  
  return {
    plan: user.subscription.plan,
    invoicesUsed: user.subscription.invoicesUsed,
    remainingInvoices,
    resetDate,
    isResetDue: now >= resetDate,
    stripeCustomerId: user.subscription.stripeCustomerId,
    stripeSubscriptionId: user.subscription.stripeSubscriptionId,
    subscriptionStatus: user.subscription.subscriptionStatus
  };
}

// Log user activity
export async function logActivity(activity: Omit<ActivityLogDocument, '_id' | 'createdAt'>): Promise<void> {
  const db = await getDatabase();
  const activityCollection = db.collection<ActivityLogDocument>(COLLECTIONS.ACTIVITY_LOGS);
  
  await activityCollection.insertOne({
    ...activity,
    createdAt: new Date()
  });
}

// Get user activity log
export async function getUserActivityLog(userId: string | ObjectId, limit: number = 50, offset: number = 0) {
  const db = await getDatabase();
  const activityCollection = db.collection<ActivityLogDocument>(COLLECTIONS.ACTIVITY_LOGS);
  
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  return await activityCollection
    .find({ userId: objectId })
    .sort({ createdAt: -1 })
    .skip(offset)
    .limit(limit)
    .toArray();
}

// Delete user and all associated data (GDPR compliance)
export async function deleteUser(userId: string | ObjectId): Promise<void> {
  const db = await getDatabase();
  const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Delete in order to maintain referential integrity
  await Promise.all([
    db.collection(COLLECTIONS.ACTIVITY_LOGS).deleteMany({ userId: objectId }),
    db.collection(COLLECTIONS.PAYMENTS).deleteMany({ userId: objectId }),
    db.collection(COLLECTIONS.TEMPLATES).deleteMany({ userId: objectId }),
    db.collection(COLLECTIONS.CLIENTS).deleteMany({ userId: objectId }),
    db.collection(COLLECTIONS.INVOICES).deleteMany({ userId: objectId }),
    db.collection(COLLECTIONS.SUBSCRIPTIONS).deleteMany({ userId: objectId }),
  ]);
  
  // Finally delete the user
  await db.collection(COLLECTIONS.USERS).deleteOne({ _id: objectId });
}
