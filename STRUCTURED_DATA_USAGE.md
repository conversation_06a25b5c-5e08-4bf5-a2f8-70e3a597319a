# StructuredData Component Usage Guide

The `StructuredData` component provides comprehensive JSON-LD structured data for better SEO and search engine understanding.

## Usage Examples

### 1. Homepage
```tsx
import StructuredData from '@/components/StructuredData';

export default function HomePage() {
  return (
    <>
      <StructuredData type="homepage" />
      {/* Page content */}
    </>
  );
}
```

### 2. Industry-Specific Pages
```tsx
import StructuredData from '@/components/StructuredData';

export default function FreelancerInvoicePage() {
  return (
    <>
      <StructuredData 
        type="industry" 
        pageData={{ 
          industry: 'Freelancer',
          title: 'Freelancer Invoice Generator',
          description: 'Create professional freelancer invoices with specialized templates'
        }} 
      />
      {/* Page content */}
    </>
  );
}
```

### 3. Template Pages
```tsx
import StructuredData from '@/components/StructuredData';

export default function TemplatePage() {
  return (
    <>
      <StructuredData 
        type="template" 
        pageData={{ 
          templateName: 'Professional',
          description: 'Clean corporate design perfect for established businesses'
        }} 
      />
      {/* Page content */}
    </>
  );
}
```

### 4. FAQ Pages
```tsx
import StructuredData from '@/components/StructuredData';

export default function FAQPage() {
  return (
    <>
      <StructuredData type="faq" />
      {/* FAQ content */}
    </>
  );
}
```

## Included Schema Types

### Organization Schema
- Company information
- Contact details
- Social media profiles
- Aggregate ratings

### WebApplication Schema
- App details and features
- Pricing information
- Operating system compatibility
- User ratings

### Service Schema
- Service catalog
- Offers and pricing
- Service areas

### FAQ Schema
- Common questions and answers
- Structured for rich snippets

### Industry-Specific Schema
- Custom schemas for each industry page
- Breadcrumb navigation
- Industry-specific descriptions

### Template Schema
- Creative work schema
- Template details
- Licensing information

## SEO Benefits

1. **Rich Snippets**: Enhanced search results with additional information
2. **Knowledge Graph**: Better understanding by search engines
3. **Voice Search**: Optimized for voice assistants
4. **Local SEO**: Location-based search improvements
5. **Business Listings**: Enhanced business information display

## Validation

Test your structured data using:
- [Google Rich Results Test](https://search.google.com/test/rich-results)
- [Schema.org Validator](https://validator.schema.org/)
- [Google Search Console](https://search.google.com/search-console)