# Google OAuth App Publishing Guide

## How to Check Publishing Status

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com
   - Select your project from the dropdown

2. **Navigate to OAuth Consent Screen**
   - In the left sidebar, go to: APIs & Services → OAuth consent screen
   - Look at the top of the page for "Publishing status"

## Current Status Indicators

### If Status Shows "Testing":
- Your app is in testing mode
- Only test users can sign in
- Limited to 100 test users

### If Status Shows "In Production":
- Your app is published
- Any Google user can sign in
- No user restrictions

## How to Publish Your App

### Option 1: Publish App (Recommended)
1. On the OAuth consent screen page
2. Look for the **"PUBLISH APP"** button
3. Click it and confirm
4. Status will change to "In Production"

### Option 2: Stay in Testing Mode
1. Click "ADD USERS" under Test users
2. Add these emails:
   - <EMAIL>
   - Any other emails you want to test with
3. Save changes

## Verification Requirements

For apps requesting sensitive scopes, Google may require verification:
- Non-sensitive scopes (basic profile, email): No verification needed
- Sensitive scopes: May need to submit for verification

Your app only requests basic profile info, so it should NOT require verification.

## Quick Checklist

- [ ] Go to: https://console.cloud.google.com
- [ ] Navigate to: APIs & Services → OAuth consent screen
- [ ] Check current publishing status
- [ ] If "Testing", click "PUBLISH APP"
- [ ] Verify redirect URIs include:
  - http://localhost:3000/api/auth/callback/google (for development)
  - https://yourdomain.com/api/auth/callback/google (for production)

## Common Issues

### "Publish App" Button Not Visible
- Make sure all required fields are filled in OAuth consent screen
- Check that you have at least one authorized domain

### Still Getting Auth Errors After Publishing
- Clear browser cookies
- Try incognito window
- Check redirect URIs match exactly
- Ensure NEXTAUTH_URL is correct in .env.local

## Production Deployment Note

When deploying to production (Vercel):
- Add production redirect URI: https://yourdomain.com/api/auth/callback/google
- Update NEXTAUTH_URL in Vercel environment variables
- Keep localhost URIs for development