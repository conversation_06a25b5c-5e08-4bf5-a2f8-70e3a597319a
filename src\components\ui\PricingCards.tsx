import React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { Check, X, Zap } from 'lucide-react';
import { Button } from './Button';

export interface PricingFeature {
  text: string;
  included: boolean;
}

export interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: number | string;
  currency?: string;
  period?: string;
  features: PricingFeature[];
  highlighted?: boolean;
  badge?: string;
  cta?: string;
}

export interface PricingCardsProps {
  plans?: PricingPlan[];
  title?: string;
  subtitle?: string;
  className?: string;
  columns?: 2 | 3 | 4;
}

const defaultPlans: PricingPlan[] = [
  {
    id: 'starter',
    name: 'Starter',
    description: 'Perfect for individuals and small projects',
    price: 9,
    currency: '$',
    period: 'month',
    features: [
      { text: 'Up to 5 users', included: true },
      { text: '10 GB storage', included: true },
      { text: 'Basic support', included: true },
      { text: 'Core features', included: true },
      { text: 'Advanced analytics', included: false },
      { text: 'Custom integrations', included: false },
      { text: 'Priority support', included: false },
    ],
    cta: 'Start Free Trial',
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'Ideal for growing teams and businesses',
    price: 29,
    currency: '$',
    period: 'month',
    features: [
      { text: 'Up to 20 users', included: true },
      { text: '100 GB storage', included: true },
      { text: 'Priority support', included: true },
      { text: 'All core features', included: true },
      { text: 'Advanced analytics', included: true },
      { text: 'Custom integrations', included: true },
      { text: 'API access', included: false },
    ],
    highlighted: true,
    badge: 'Most Popular',
    cta: 'Start Free Trial',
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'For large organizations with custom needs',
    price: 'Custom',
    features: [
      { text: 'Unlimited users', included: true },
      { text: 'Unlimited storage', included: true },
      { text: 'Dedicated support', included: true },
      { text: 'All features', included: true },
      { text: 'Advanced analytics', included: true },
      { text: 'Custom integrations', included: true },
      { text: 'Full API access', included: true },
    ],
    cta: 'Contact Sales',
  },
];

const PricingCards: React.FC<PricingCardsProps> = ({
  plans = defaultPlans,
  title = 'Simple, Transparent Pricing',
  subtitle = 'Choose the perfect plan for your needs',
  className,
  columns = 3,
}) => {
  const gridCols = {
    2: 'md:grid-cols-2',
    3: 'md:grid-cols-2 lg:grid-cols-3',
    4: 'md:grid-cols-2 lg:grid-cols-4',
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section className={cn('py-16 lg:py-24', className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && (
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {subtitle}
              </p>
            )}
          </div>
        )}

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: '-100px' }}
          className={cn('grid grid-cols-1 gap-8', gridCols[columns])}
        >
          {plans.map((plan) => (
            <motion.div
              key={plan.id}
              variants={cardVariants}
              whileHover={{ y: -5 }}
              className={cn(
                'relative bg-white rounded-2xl shadow-lg overflow-hidden',
                plan.highlighted && 'ring-2 ring-primary shadow-xl'
              )}
            >
              {plan.badge && (
                <div className="absolute top-0 right-0">
                  <div className="bg-primary text-white text-sm font-medium px-4 py-1 rounded-bl-lg">
                    {plan.badge}
                  </div>
                </div>
              )}

              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  {plan.name}
                </h3>
                <p className="text-gray-600 mb-6">{plan.description}</p>

                <div className="mb-8">
                  {typeof plan.price === 'number' ? (
                    <div className="flex items-baseline">
                      <span className="text-4xl font-bold text-gray-900">
                        {plan.currency}{plan.price}
                      </span>
                      {plan.period && (
                        <span className="ml-2 text-gray-600">/{plan.period}</span>
                      )}
                    </div>
                  ) : (
                    <div className="text-4xl font-bold text-gray-900">
                      {plan.price}
                    </div>
                  )}
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, index) => (
                    <li
                      key={index}
                      className={cn(
                        'flex items-start',
                        !feature.included && 'opacity-50'
                      )}
                    >
                      {feature.included ? (
                        <Check className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                      ) : (
                        <X className="w-5 h-5 text-gray-400 mt-0.5 mr-3 flex-shrink-0" />
                      )}
                      <span className="text-gray-700">{feature.text}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  variant={plan.highlighted ? 'primary' : 'outline'}
                  className="w-full"
                  size="lg"
                >
                  {plan.cta || 'Get Started'}
                </Button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="mt-12 text-center"
        >
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-gray-600">
            <div className="flex items-center">
              <Zap className="w-5 h-5 mr-2 text-primary" />
              <span>No setup fees</span>
            </div>
            <div className="flex items-center">
              <Check className="w-5 h-5 mr-2 text-green-500" />
              <span>Cancel anytime</span>
            </div>
            <div className="flex items-center">
              <Check className="w-5 h-5 mr-2 text-green-500" />
              <span>14-day free trial</span>
            </div>
          </div>
          
          <p className="mt-6 text-sm text-gray-500">
            All plans include SSL certificate, 24/7 monitoring, and regular backups
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export { PricingCards };