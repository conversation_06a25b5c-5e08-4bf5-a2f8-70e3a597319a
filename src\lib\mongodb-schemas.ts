/**
 * MongoDB Collections and Schemas for Template Invoice System
 * 
 * This file defines all MongoDB collection schemas with TypeScript interfaces,
 * validation rules, indexes, and comprehensive CRUD operations.
 */

import { ObjectId, Collection, Db, Document } from 'mongodb';
import clientPromise from './mongodb';

// =============================================================================
// SCHEMA DEFINITIONS
// =============================================================================

/**
 * USER COLLECTION SCHEMA
 * Stores user profiles, authentication data, and subscription information
 */
export interface UserSchema {
  _id?: ObjectId;
  email: string;                    // Unique email from Google OAuth
  name: string;                     // Full name from Google
  googleId: string;                 // Google OAuth provider ID
  image?: string;                   // Profile picture URL
  
  // Subscription Management
  subscription: {
    plan: 'free' | 'pro';          // Current subscription tier
    invoicesUsed: number;          // Monthly invoice count
    resetDate: Date;               // Next billing cycle reset
    stripeCustomerId?: string;     // Stripe customer ID
    stripeSubscriptionId?: string; // Stripe subscription ID
    subscriptionStatus?: 'active' | 'cancelled' | 'past_due';
  };
  
  // Business Profile (optional for Pro users)
  businessProfile?: {
    companyName?: string;          // Company/business name
    address?: string;              // Business address
    city?: string;                 // Business city
    country?: string;              // Business country
    phone?: string;                // Business phone
    website?: string;              // Business website
    logo?: string;                 // Logo URL/path
    taxId?: string;                // Tax identification number
  };
  
  // User Preferences
  preferences?: {
    defaultTemplate?: string;      // Default invoice template ID
    defaultCurrency?: string;      // Preferred currency (USD, EUR, etc.)
    timezone?: string;             // User timezone
    language?: string;             // Preferred language
    theme?: 'light' | 'dark';      // UI theme preference
  };
  
  // Audit Fields
  createdAt: Date;                 // Account creation timestamp
  lastLogin: Date;                 // Last login timestamp
  updatedAt?: Date;                // Last profile update
}

/**
 * INVOICES COLLECTION SCHEMA
 * Stores all invoice data, line items, and financial calculations
 */
export interface InvoiceSchema {
  _id?: ObjectId;
  userId: ObjectId;                // Reference to user who created invoice
  invoiceNumber: string;           // Unique invoice number (INV-2024-0001)
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  
  // Business Information (from user's business profile)
  businessInfo: {
    name: string;                  // Business name
    address: string;               // Business address
    city: string;                  // Business city
    country?: string;              // Business country
    email: string;                 // Business email
    phone?: string;                // Business phone
    website?: string;              // Business website
    logo?: string;                 // Logo URL
    taxId?: string;                // Tax identification
  };
  
  // Client Information
  clientInfo: {
    name: string;                  // Client/company name
    email: string;                 // Client email
    address: string;               // Client address
    city: string;                  // Client city
    country?: string;              // Client country
    contactPerson?: string;        // Contact person name
    phone?: string;                // Client phone
    taxId?: string;                // Client tax ID
  };
  
  // Invoice Dates and Terms
  invoiceDate: Date;               // Invoice issue date
  dueDate: Date;                   // Payment due date
  terms: string;                   // Payment terms and conditions
  
  // Line Items Array
  lineItems: Array<{
    id: string;                    // Unique line item ID
    description: string;           // Item/service description
    quantity: number;              // Quantity of items
    rate: number;                  // Rate per item
    amount: number;                // Total amount (quantity * rate)
    taxable?: boolean;             // Whether item is taxable
    category?: string;             // Item category
  }>;
  
  // Financial Totals
  totals: {
    subtotal: number;              // Sum of all line items
    taxRate?: number;              // Tax rate percentage
    taxAmount: number;             // Calculated tax amount
    discount?: number;             // Discount amount
    discountType?: 'fixed' | 'percentage'; // Discount type
    total: number;                 // Final total amount
  };
  
  // Template and Customization
  templateId: string;              // Selected template ID
  customStyling?: {
    primaryColor?: string;         // Custom brand color
    secondaryColor?: string;       // Secondary color
    font?: string;                 // Font family
    logoPosition?: 'left' | 'center' | 'right';
  };
  
  // File Management
  pdfUrl?: string;                 // Generated PDF file URL
  pdfGeneratedAt?: Date;           // PDF generation timestamp
  
  // Additional Fields
  notes?: string;                  // Public notes visible to client
  internalNotes?: string;          // Private internal notes
  currency: string;                // Invoice currency (USD, EUR, etc.)
  language?: string;               // Invoice language
  
  // Audit Fields
  createdAt: Date;                 // Invoice creation timestamp
  updatedAt: Date;                 // Last modification timestamp
  sentAt?: Date;                   // When invoice was sent to client
  paidAt?: Date;                   // When payment was received
}

/**
 * CLIENTS COLLECTION SCHEMA
 * Stores client/customer information and invoice history
 */
export interface ClientSchema {
  _id?: ObjectId;
  userId: ObjectId;                // Reference to user who owns this client
  
  // Basic Client Information
  name: string;                    // Client/company name
  email: string;                   // Primary email address
  phone?: string;                  // Phone number
  website?: string;                // Client website
  
  // Address Information
  address: {
    street: string;                // Street address
    city: string;                  // City
    state?: string;                // State/province
    zipCode?: string;              // ZIP/postal code
    country: string;               // Country
  };
  
  // Contact Information
  contactPerson?: {
    name?: string;                 // Primary contact name
    email?: string;                // Contact email
    phone?: string;                // Contact phone
    title?: string;                // Contact job title
  };
  
  // Business Information
  businessInfo?: {
    type?: string;                 // Business type/industry
    taxId?: string;                // Tax identification number
    size?: 'small' | 'medium' | 'large' | 'enterprise';
    annualRevenue?: number;        // Annual revenue estimate
  };
  
  // Invoice History and Analytics
  invoiceHistory: {
    lastInvoiceDate?: Date;        // Date of most recent invoice
    totalInvoiced: number;         // Total amount invoiced to date
    invoiceCount: number;          // Total number of invoices
    averageInvoiceAmount: number;  // Average invoice amount
    totalPaid: number;             // Total amount paid
    totalOutstanding: number;      // Total outstanding amount
  };
  
  // Client Status and Settings
  status: 'active' | 'inactive' | 'blocked';
  paymentTerms?: string;           // Default payment terms
  preferredCurrency?: string;      // Preferred currency
  creditLimit?: number;            // Credit limit if applicable
  
  // Additional Information
  notes?: string;                  // Internal notes about client
  tags?: string[];                 // Client tags for organization
  customFields?: Record<string, any>; // Custom client fields
  
  // Audit Fields
  createdAt: Date;                 // Client creation timestamp
  updatedAt: Date;                 // Last modification timestamp
}

// =============================================================================
// COLLECTION NAMES CONSTANTS
// =============================================================================

export const COLLECTION_NAMES = {
  USERS: 'users',
  INVOICES: 'invoices',
  CLIENTS: 'clients',
  TEMPLATES: 'templates',
  PAYMENTS: 'payments',
  SUBSCRIPTIONS: 'subscriptions',
  ACTIVITY_LOGS: 'activity_logs',
  // NextAuth collections
  ACCOUNTS: 'accounts',
  SESSIONS: 'sessions',
  VERIFICATION_TOKENS: 'verificationtokens'
} as const;

// =============================================================================
// DATABASE CONNECTION AND HELPERS
// =============================================================================

/**
 * Get MongoDB database instance
 */
async function getDatabase(): Promise<Db> {
  const client = await clientPromise;
  return client.db();
}

/**
 * Get typed collection helper
 */
async function getCollection<T extends Document = any>(name: string): Promise<Collection<T>> {
  const db = await getDatabase();
  return db.collection<T>(name);
}

// =============================================================================
// USER COLLECTION CRUD OPERATIONS
// =============================================================================

export class UserRepository {
  private static async getCollection(): Promise<Collection<UserSchema>> {
    return getCollection<UserSchema>(COLLECTION_NAMES.USERS);
  }

  /**
   * Create a new user account
   */
  static async create(userData: Omit<UserSchema, '_id' | 'createdAt' | 'lastLogin'>): Promise<UserSchema> {
    const collection = await this.getCollection();
    const now = new Date();
    
    const user: UserSchema = {
      ...userData,
      createdAt: now,
      lastLogin: now,
      updatedAt: now
    };
    
    const result = await collection.insertOne(user);
    return { ...user, _id: result.insertedId };
  }

  /**
   * Find user by email
   */
  static async findByEmail(email: string): Promise<UserSchema | null> {
    const collection = await this.getCollection();
    return collection.findOne({ email });
  }

  /**
   * Find user by ID
   */
  static async findById(id: string | ObjectId): Promise<UserSchema | null> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    return collection.findOne({ _id: objectId });
  }

  /**
   * Update user profile
   */
  static async update(id: string | ObjectId, updates: Partial<UserSchema>): Promise<UserSchema | null> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    
    const result = await collection.findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value;
  }

  /**
   * Update user's last login timestamp
   */
  static async updateLastLogin(email: string): Promise<UserSchema | null> {
    const collection = await this.getCollection();
    
    const result = await collection.findOneAndUpdate(
      { email },
      { $set: { lastLogin: new Date() } },
      { returnDocument: 'after' }
    );
    
    return result.value;
  }

  /**
   * Update subscription information
   */
  static async updateSubscription(id: string | ObjectId, subscriptionUpdates: Partial<UserSchema['subscription']>): Promise<UserSchema | null> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    // Build $set object with subscription fields
    const setFields: any = { updatedAt: new Date() };
    
    Object.entries(subscriptionUpdates).forEach(([key, value]) => {
      setFields[`subscription.${key}`] = value;
    });
    
    const result = await collection.findOneAndUpdate(
      { _id: objectId },
      { $set: setFields },
      { returnDocument: 'after' }
    );
    
    return result.value;
  }

  /**
   * Increment monthly invoice usage
   */
  static async incrementInvoiceUsage(id: string | ObjectId): Promise<void> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    await collection.updateOne(
      { _id: objectId },
      { 
        $inc: { 'subscription.invoicesUsed': 1 },
        $set: { updatedAt: new Date() }
      }
    );
  }

  /**
   * Reset monthly invoice usage (called monthly)
   */
  static async resetMonthlyUsage(id: string | ObjectId): Promise<void> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1, 1);
    
    await collection.updateOne(
      { _id: objectId },
      { 
        $set: { 
          'subscription.invoicesUsed': 0,
          'subscription.resetDate': nextMonth,
          updatedAt: new Date()
        }
      }
    );
  }

  /**
   * Delete user account
   */
  static async delete(id: string | ObjectId): Promise<boolean> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    const result = await collection.deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }

  /**
   * Get user statistics
   */
  static async getStats(id: string | ObjectId) {
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    const [invoicesCollection, clientsCollection] = await Promise.all([
      getCollection<InvoiceSchema>(COLLECTION_NAMES.INVOICES),
      getCollection<ClientSchema>(COLLECTION_NAMES.CLIENTS)
    ]);
    
    const [invoiceStats, clientCount] = await Promise.all([
      invoicesCollection.aggregate([
        { $match: { userId: objectId } },
        {
          $group: {
            _id: null,
            totalInvoices: { $sum: 1 },
            totalRevenue: { $sum: '$totals.total' },
            paidAmount: {
              $sum: {
                $cond: [{ $eq: ['$status', 'paid'] }, '$totals.total', 0]
              }
            },
            pendingAmount: {
              $sum: {
                $cond: [{ $in: ['$status', ['sent', 'overdue']] }, '$totals.total', 0]
              }
            }
          }
        }
      ]).toArray(),
      clientsCollection.countDocuments({ userId: objectId })
    ]);
    
    const stats = invoiceStats[0] || {
      totalInvoices: 0,
      totalRevenue: 0,
      paidAmount: 0,
      pendingAmount: 0
    };
    
    return {
      ...stats,
      clientCount
    };
  }
}

// =============================================================================
// INVOICE COLLECTION CRUD OPERATIONS
// =============================================================================

export class InvoiceRepository {
  private static async getCollection(): Promise<Collection<InvoiceSchema>> {
    return getCollection<InvoiceSchema>(COLLECTION_NAMES.INVOICES);
  }

  /**
   * Generate unique invoice number
   */
  static async generateInvoiceNumber(userId: string | ObjectId): Promise<string> {
    const collection = await this.getCollection();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    const count = await collection.countDocuments({ userId: objectId });
    const year = new Date().getFullYear();
    const sequential = (count + 1).toString().padStart(4, '0');
    
    return `INV-${year}-${sequential}`;
  }

  /**
   * Create a new invoice
   */
  static async create(invoiceData: Omit<InvoiceSchema, '_id' | 'createdAt' | 'updatedAt'>): Promise<InvoiceSchema> {
    const collection = await this.getCollection();
    const now = new Date();
    
    // Generate invoice number if not provided
    if (!invoiceData.invoiceNumber) {
      invoiceData.invoiceNumber = await this.generateInvoiceNumber(invoiceData.userId);
    }
    
    const invoice: InvoiceSchema = {
      ...invoiceData,
      createdAt: now,
      updatedAt: now
    };
    
    const result = await collection.insertOne(invoice);
    
    // Increment user's invoice usage
    await UserRepository.incrementInvoiceUsage(invoiceData.userId);
    
    return { ...invoice, _id: result.insertedId };
  }

  /**
   * Find invoice by ID
   */
  static async findById(id: string | ObjectId): Promise<InvoiceSchema | null> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    return collection.findOne({ _id: objectId });
  }

  /**
   * Find invoices by user ID
   */
  static async findByUserId(userId: string | ObjectId, options?: {
    limit?: number;
    skip?: number;
    status?: InvoiceSchema['status'];
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<InvoiceSchema[]> {
    const collection = await this.getCollection();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    const query: any = { userId: objectId };
    if (options?.status) {
      query.status = options.status;
    }
    
    const sort: any = {};
    if (options?.sortBy) {
      sort[options.sortBy] = options.sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1; // Default sort by creation date, newest first
    }
    
    let cursor = collection.find(query).sort(sort);
    
    if (options?.skip) {
      cursor = cursor.skip(options.skip);
    }
    
    if (options?.limit) {
      cursor = cursor.limit(options.limit);
    }
    
    return cursor.toArray();
  }

  /**
   * Update invoice
   */
  static async update(id: string | ObjectId, updates: Partial<InvoiceSchema>): Promise<InvoiceSchema | null> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    
    const result = await collection.findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value;
  }

  /**
   * Update invoice status
   */
  static async updateStatus(id: string | ObjectId, status: InvoiceSchema['status']): Promise<InvoiceSchema | null> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    const updateData: any = {
      status,
      updatedAt: new Date()
    };
    
    // Set timestamp fields based on status
    switch (status) {
      case 'sent':
        updateData.sentAt = new Date();
        break;
      case 'paid':
        updateData.paidAt = new Date();
        break;
    }
    
    const result = await collection.findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value;
  }

  /**
   * Delete invoice
   */
  static async delete(id: string | ObjectId): Promise<boolean> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    const result = await collection.deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }

  /**
   * Get invoice statistics for a user
   */
  static async getStatsByUserId(userId: string | ObjectId) {
    const collection = await this.getCollection();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    const stats = await collection.aggregate([
      { $match: { userId: objectId } },
      {
        $group: {
          _id: null,
          totalInvoices: { $sum: 1 },
          totalRevenue: { $sum: '$totals.total' },
          draftCount: {
            $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
          },
          sentCount: {
            $sum: { $cond: [{ $eq: ['$status', 'sent'] }, 1, 0] }
          },
          paidCount: {
            $sum: { $cond: [{ $eq: ['$status', 'paid'] }, 1, 0] }
          },
          overdueCount: {
            $sum: { $cond: [{ $eq: ['$status', 'overdue'] }, 1, 0] }
          },
          paidAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$totals.total', 0]
            }
          },
          pendingAmount: {
            $sum: {
              $cond: [{ $in: ['$status', ['sent', 'overdue']] }, '$totals.total', 0]
            }
          }
        }
      }
    ]).toArray();
    
    return stats[0] || {
      totalInvoices: 0,
      totalRevenue: 0,
      draftCount: 0,
      sentCount: 0,
      paidCount: 0,
      overdueCount: 0,
      paidAmount: 0,
      pendingAmount: 0
    };
  }

  /**
   * Search invoices
   */
  static async search(userId: string | ObjectId, searchTerm: string, options?: {
    limit?: number;
    skip?: number;
  }): Promise<InvoiceSchema[]> {
    const collection = await this.getCollection();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    const searchRegex = new RegExp(searchTerm, 'i');
    
    const query = {
      userId: objectId,
      $or: [
        { invoiceNumber: searchRegex },
        { 'clientInfo.name': searchRegex },
        { 'clientInfo.email': searchRegex },
        { 'businessInfo.name': searchRegex }
      ]
    };
    
    let cursor = collection.find(query).sort({ createdAt: -1 });
    
    if (options?.skip) {
      cursor = cursor.skip(options.skip);
    }
    
    if (options?.limit) {
      cursor = cursor.limit(options.limit);
    }
    
    return cursor.toArray();
  }
}

// =============================================================================
// CLIENT COLLECTION CRUD OPERATIONS
// =============================================================================

export class ClientRepository {
  private static async getCollection(): Promise<Collection<ClientSchema>> {
    return getCollection<ClientSchema>(COLLECTION_NAMES.CLIENTS);
  }

  /**
   * Create a new client
   */
  static async create(clientData: Omit<ClientSchema, '_id' | 'createdAt' | 'updatedAt' | 'invoiceHistory'>): Promise<ClientSchema> {
    const collection = await this.getCollection();
    const now = new Date();
    
    const client: ClientSchema = {
      ...clientData,
      invoiceHistory: {
        totalInvoiced: 0,
        invoiceCount: 0,
        averageInvoiceAmount: 0,
        totalPaid: 0,
        totalOutstanding: 0
      },
      status: clientData.status || 'active',
      createdAt: now,
      updatedAt: now
    };
    
    const result = await collection.insertOne(client);
    return { ...client, _id: result.insertedId };
  }

  /**
   * Find client by ID
   */
  static async findById(id: string | ObjectId): Promise<ClientSchema | null> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    return collection.findOne({ _id: objectId });
  }

  /**
   * Find clients by user ID
   */
  static async findByUserId(userId: string | ObjectId, options?: {
    limit?: number;
    skip?: number;
    status?: ClientSchema['status'];
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<ClientSchema[]> {
    const collection = await this.getCollection();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    const query: any = { userId: objectId };
    if (options?.status) {
      query.status = options.status;
    }
    
    const sort: any = {};
    if (options?.sortBy) {
      sort[options.sortBy] = options.sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.name = 1; // Default sort by name alphabetically
    }
    
    let cursor = collection.find(query).sort(sort);
    
    if (options?.skip) {
      cursor = cursor.skip(options.skip);
    }
    
    if (options?.limit) {
      cursor = cursor.limit(options.limit);
    }
    
    return cursor.toArray();
  }

  /**
   * Find client by email
   */
  static async findByEmail(userId: string | ObjectId, email: string): Promise<ClientSchema | null> {
    const collection = await this.getCollection();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    return collection.findOne({ 
      userId: objectId, 
      email: email.toLowerCase() 
    });
  }

  /**
   * Update client
   */
  static async update(id: string | ObjectId, updates: Partial<ClientSchema>): Promise<ClientSchema | null> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    
    const result = await collection.findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value;
  }

  /**
   * Update client invoice history (called when invoice is created/updated)
   */
  static async updateInvoiceHistory(clientId: string | ObjectId, invoiceAmount: number, isNewInvoice: boolean = true): Promise<void> {
    const collection = await this.getCollection();
    const objectId = typeof clientId === 'string' ? new ObjectId(clientId) : clientId;
    
    const client = await collection.findOne({ _id: objectId });
    if (!client) return;
    
    const updateData: any = {
      'invoiceHistory.lastInvoiceDate': new Date(),
      updatedAt: new Date()
    };
    
    if (isNewInvoice) {
      const newTotalInvoiced = client.invoiceHistory.totalInvoiced + invoiceAmount;
      const newInvoiceCount = client.invoiceHistory.invoiceCount + 1;
      const newAverageAmount = newTotalInvoiced / newInvoiceCount;
      
      updateData['invoiceHistory.totalInvoiced'] = newTotalInvoiced;
      updateData['invoiceHistory.invoiceCount'] = newInvoiceCount;
      updateData['invoiceHistory.averageInvoiceAmount'] = newAverageAmount;
    }
    
    await collection.updateOne(
      { _id: objectId },
      { $set: updateData }
    );
  }

  /**
   * Update client payment history (called when payment is received)
   */
  static async updatePaymentHistory(clientId: string | ObjectId, paidAmount: number): Promise<void> {
    const collection = await this.getCollection();
    const objectId = typeof clientId === 'string' ? new ObjectId(clientId) : clientId;
    
    await collection.updateOne(
      { _id: objectId },
      { 
        $inc: { 
          'invoiceHistory.totalPaid': paidAmount,
          'invoiceHistory.totalOutstanding': -paidAmount
        },
        $set: { updatedAt: new Date() }
      }
    );
  }

  /**
   * Delete client
   */
  static async delete(id: string | ObjectId): Promise<boolean> {
    const collection = await this.getCollection();
    const objectId = typeof id === 'string' ? new ObjectId(id) : id;
    
    const result = await collection.deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }

  /**
   * Search clients
   */
  static async search(userId: string | ObjectId, searchTerm: string, options?: {
    limit?: number;
    skip?: number;
  }): Promise<ClientSchema[]> {
    const collection = await this.getCollection();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    const searchRegex = new RegExp(searchTerm, 'i');
    
    const query = {
      userId: objectId,
      $or: [
        { name: searchRegex },
        { email: searchRegex },
        { 'contactPerson.name': searchRegex },
        { 'address.city': searchRegex }
      ]
    };
    
    let cursor = collection.find(query).sort({ name: 1 });
    
    if (options?.skip) {
      cursor = cursor.skip(options.skip);
    }
    
    if (options?.limit) {
      cursor = cursor.limit(options.limit);
    }
    
    return cursor.toArray();
  }

  /**
   * Get top clients by revenue
   */
  static async getTopClients(userId: string | ObjectId, limit: number = 10): Promise<ClientSchema[]> {
    const collection = await this.getCollection();
    const objectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    return collection.find({ userId: objectId })
      .sort({ 'invoiceHistory.totalInvoiced': -1 })
      .limit(limit)
      .toArray();
  }
}

// =============================================================================
// DATABASE INDEXES AND VALIDATION
// =============================================================================

/**
 * Create database indexes for optimal performance
 */
export async function createIndexes(): Promise<void> {
  const db = await getDatabase();
  
  // Users Collection Indexes
  const usersCollection = db.collection(COLLECTION_NAMES.USERS);
  await Promise.all([
    usersCollection.createIndex({ email: 1 }, { unique: true }),
    usersCollection.createIndex({ googleId: 1 }, { unique: true }),
    usersCollection.createIndex({ 'subscription.plan': 1 }),
    usersCollection.createIndex({ createdAt: 1 }),
    usersCollection.createIndex({ lastLogin: 1 })
  ]);
  
  // Invoices Collection Indexes
  const invoicesCollection = db.collection(COLLECTION_NAMES.INVOICES);
  await Promise.all([
    invoicesCollection.createIndex({ userId: 1 }),
    invoicesCollection.createIndex({ invoiceNumber: 1 }, { unique: true }),
    invoicesCollection.createIndex({ status: 1 }),
    invoicesCollection.createIndex({ 'clientInfo.email': 1 }),
    invoicesCollection.createIndex({ createdAt: -1 }),
    invoicesCollection.createIndex({ dueDate: 1 }),
    invoicesCollection.createIndex({ userId: 1, status: 1 }),
    invoicesCollection.createIndex({ userId: 1, createdAt: -1 })
  ]);
  
  // Clients Collection Indexes
  const clientsCollection = db.collection(COLLECTION_NAMES.CLIENTS);
  await Promise.all([
    clientsCollection.createIndex({ userId: 1 }),
    clientsCollection.createIndex({ userId: 1, email: 1 }, { unique: true }),
    clientsCollection.createIndex({ name: 1 }),
    clientsCollection.createIndex({ status: 1 }),
    clientsCollection.createIndex({ userId: 1, name: 1 }),
    clientsCollection.createIndex({ 'invoiceHistory.totalInvoiced': -1 }),
    clientsCollection.createIndex({ createdAt: 1 })
  ]);
  
  console.log('✅ Database indexes created successfully');
}

/**
 * Validate database collections and schemas
 */
export async function validateSchemas(): Promise<void> {
  const db = await getDatabase();
  
  // Create validation rules for each collection
  const validationRules = {
    [COLLECTION_NAMES.USERS]: {
      $jsonSchema: {
        bsonType: 'object',
        required: ['email', 'name', 'googleId', 'subscription', 'createdAt', 'lastLogin'],
        properties: {
          email: { bsonType: 'string', pattern: '^.+@.+\..+$' },
          name: { bsonType: 'string', minLength: 1 },
          googleId: { bsonType: 'string', minLength: 1 },
          subscription: {
            bsonType: 'object',
            required: ['plan', 'invoicesUsed', 'resetDate'],
            properties: {
              plan: { enum: ['free', 'pro'] },
              invoicesUsed: { bsonType: 'number', minimum: 0 },
              resetDate: { bsonType: 'date' }
            }
          }
        }
      }
    },
    [COLLECTION_NAMES.INVOICES]: {
      $jsonSchema: {
        bsonType: 'object',
        required: ['userId', 'invoiceNumber', 'status', 'businessInfo', 'clientInfo', 'lineItems', 'totals', 'templateId', 'createdAt'],
        properties: {
          status: { enum: ['draft', 'sent', 'paid', 'overdue', 'cancelled'] },
          lineItems: {
            bsonType: 'array',
            minItems: 1,
            items: {
              bsonType: 'object',
              required: ['description', 'quantity', 'rate', 'amount'],
              properties: {
                quantity: { bsonType: 'number', minimum: 0 },
                rate: { bsonType: 'number', minimum: 0 },
                amount: { bsonType: 'number', minimum: 0 }
              }
            }
          }
        }
      }
    },
    [COLLECTION_NAMES.CLIENTS]: {
      $jsonSchema: {
        bsonType: 'object',
        required: ['userId', 'name', 'email', 'address', 'invoiceHistory', 'status', 'createdAt'],
        properties: {
          email: { bsonType: 'string', pattern: '^.+@.+\..+$' },
          status: { enum: ['active', 'inactive', 'blocked'] }
        }
      }
    }
  };
  
  // Apply validation rules to collections
  for (const [collectionName, validation] of Object.entries(validationRules)) {
    try {
      await db.command({
        collMod: collectionName,
        validator: validation
      });
      console.log(`✅ Validation rules applied to ${collectionName} collection`);
    } catch (error) {
      console.log(`ℹ️  Collection ${collectionName} validation rules updated or already exist`);
    }
  }
}

// =============================================================================
// EXPORT ALL REPOSITORIES AND UTILITIES
// =============================================================================

export {
  UserRepository as Users,
  InvoiceRepository as Invoices,
  ClientRepository as Clients
};

// Type exports for external use
export type {
  UserSchema as User,
  InvoiceSchema as Invoice,
  ClientSchema as Client
};

// Input types for creating records
export type CreateUserInput = Omit<UserSchema, '_id' | 'createdAt' | 'lastLogin' | 'updatedAt'>;
export type CreateInvoiceInput = Omit<InvoiceSchema, '_id' | 'createdAt' | 'updatedAt'>;
export type CreateClientInput = Omit<ClientSchema, '_id' | 'createdAt' | 'updatedAt' | 'invoiceHistory'>;

// Update types for modifying records
export type UpdateUserInput = Partial<Omit<UserSchema, '_id' | 'createdAt'>>;
export type UpdateInvoiceInput = Partial<Omit<InvoiceSchema, '_id' | 'userId' | 'createdAt'>>;
export type UpdateClientInput = Partial<Omit<ClientSchema, '_id' | 'userId' | 'createdAt'>>;