'use client'

import { useState, useEffect } from 'react'
import {
  Building,
  User,
  Hash,
  Calculator,
  DollarSign,
  Plus,
  Trash2,
  Upload,
  X
} from 'lucide-react'
import { Input } from '../ui/Input'
import { Button } from '../ui/Button'
import { Card } from '../ui/Card'

interface LineItem {
  description: string
  quantity: number
  rate: number
  amount: number
}

interface InvoiceData {
  businessName: string
  businessAddress: string
  businessPhone: string
  businessEmail: string
  businessLogo: string
  clientName: string
  clientAddress: string
  clientPhone: string
  clientEmail: string
  invoiceNumber: string
  invoiceDate: string
  dueDate: string
  lineItems: LineItem[]
  taxRate: number
  notes: string
  paymentTerms: string
  subtotal?: number
  tax?: number
  total?: number
}

interface InvoiceFormProps {
  data: InvoiceData
  onChange: (data: InvoiceData) => void
  template?: any
}

export function InvoiceForm({ data, onChange, template }: InvoiceFormProps) {
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  
  // Calculate totals helper functions
  const calculateSubtotal = (items: LineItem[]) => {
    return items.reduce((sum, item) => sum + item.amount, 0)
  }
  
  const calculateTax = (subtotal: number, taxRate: number) => {
    return subtotal * (taxRate / 100)
  }
  
  // Handle form field changes
  const handleFieldChange = (field: keyof InvoiceData, value: any) => {
    const updatedData = { ...data, [field]: value }
    
    // Recalculate totals when line items or tax rate change
    if (field === 'lineItems' || field === 'taxRate') {
      const subtotal = calculateSubtotal(updatedData.lineItems)
      const tax = calculateTax(subtotal, updatedData.taxRate)
      updatedData.subtotal = subtotal
      updatedData.tax = tax
      updatedData.total = subtotal + tax
    }
    
    onChange(updatedData)
  }
  
  // Handle line item changes
  const handleLineItemChange = (index: number, field: keyof LineItem, value: any) => {
    const newLineItems = [...data.lineItems]
    newLineItems[index] = { ...newLineItems[index], [field]: value }
    
    // Auto-calculate amount
    if (field === 'quantity' || field === 'rate') {
      newLineItems[index].amount = newLineItems[index].quantity * newLineItems[index].rate
    }
    
    handleFieldChange('lineItems', newLineItems)
  }
  
  // Add new line item
  const addLineItem = () => {
    const newLineItems = [...data.lineItems, { description: '', quantity: 1, rate: 0, amount: 0 }]
    handleFieldChange('lineItems', newLineItems)
  }
  
  // Remove line item
  const removeLineItem = (index: number) => {
    if (data.lineItems.length > 1) {
      const newLineItems = data.lineItems.filter((_, i) => i !== index)
      handleFieldChange('lineItems', newLineItems)
    }
  }
  
  // Handle logo upload
  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setLogoPreview(result)
        handleFieldChange('businessLogo', result)
      }
      reader.readAsDataURL(file)
    }
  }
  
  // Remove logo
  const removeLogo = () => {
    setLogoPreview(null)
    handleFieldChange('businessLogo', '')
  }
  
  // Calculate totals on mount and when data changes
  useEffect(() => {
    const subtotal = calculateSubtotal(data.lineItems)
    const tax = calculateTax(subtotal, data.taxRate)
    onChange({
      ...data,
      subtotal,
      tax,
      total: subtotal + tax
    })
  }, [data.lineItems.length]) // Only recalculate when items are added/removed
  
  return (
    <form className="invoice-form space-y-6">
      {/* Business Information Section */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Building className="w-5 h-5 mr-2" />
          Business Information
        </h3>
        
        <div className="space-y-4">
          {/* Logo Upload */}
          <div className="form-group">
            <label className="block text-sm font-medium text-gray-700 mb-2">Company Logo</label>
            {!logoPreview ? (
              <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                <Upload className="w-8 h-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-500">Click to upload logo</span>
                <input 
                  type="file" 
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />
              </label>
            ) : (
              <div className="relative inline-block">
                <img src={logoPreview} alt="Logo preview" className="h-32 object-contain" />
                <button
                  type="button"
                  onClick={removeLogo}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
          
          <div className="grid md:grid-cols-2 gap-4">
            <Input
              label="Business Name *"
              value={data.businessName}
              onChange={(e) => handleFieldChange('businessName', e.target.value)}
              placeholder="Your Business Name"
              required
            />
            <Input
              label="Email *"
              type="email"
              value={data.businessEmail}
              onChange={(e) => handleFieldChange('businessEmail', e.target.value)}
              placeholder="<EMAIL>"
              required
            />
            <Input
              label="Phone"
              type="tel"
              value={data.businessPhone}
              onChange={(e) => handleFieldChange('businessPhone', e.target.value)}
              placeholder="(*************"
            />
            <Input
              label="Address"
              value={data.businessAddress}
              onChange={(e) => handleFieldChange('businessAddress', e.target.value)}
              placeholder="123 Main St, City, State 12345"
            />
          </div>
        </div>
      </Card>
      
      {/* Client Information Section */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <User className="w-5 h-5 mr-2" />
          Client Information
        </h3>
        
        <div className="grid md:grid-cols-2 gap-4">
          <Input
            label="Client Name *"
            value={data.clientName}
            onChange={(e) => handleFieldChange('clientName', e.target.value)}
            placeholder="Client or Company Name"
            required
          />
          <Input
            label="Client Email *"
            type="email"
            value={data.clientEmail}
            onChange={(e) => handleFieldChange('clientEmail', e.target.value)}
            placeholder="<EMAIL>"
            required
          />
          <Input
            label="Phone"
            type="tel"
            value={data.clientPhone}
            onChange={(e) => handleFieldChange('clientPhone', e.target.value)}
            placeholder="(*************"
          />
          <Input
            label="Address"
            value={data.clientAddress}
            onChange={(e) => handleFieldChange('clientAddress', e.target.value)}
            placeholder="123 Client St, City, State 12345"
          />
        </div>
      </Card>
      
      {/* Invoice Details Section */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Hash className="w-5 h-5 mr-2" />
          Invoice Details
        </h3>
        
        <div className="grid md:grid-cols-3 gap-4">
          <Input
            label="Invoice Number"
            value={data.invoiceNumber}
            onChange={(e) => handleFieldChange('invoiceNumber', e.target.value)}
            placeholder="INV-001"
          />
          <Input
            label="Invoice Date"
            type="date"
            value={data.invoiceDate}
            onChange={(e) => handleFieldChange('invoiceDate', e.target.value)}
          />
          <Input
            label="Due Date *"
            type="date"
            value={data.dueDate}
            onChange={(e) => handleFieldChange('dueDate', e.target.value)}
            required
          />
        </div>
      </Card>
      
      {/* Line Items Section */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Calculator className="w-5 h-5 mr-2" />
            Items & Services
          </h3>
          <Button onClick={addLineItem} variant="outline" size="sm" type="button">
            <Plus className="w-4 h-4 mr-2" />
            Add Item
          </Button>
        </div>
        
        <div className="space-y-3">
          {/* Table Header */}
          <div className="grid grid-cols-12 gap-3 text-sm font-medium text-gray-700">
            <div className="col-span-5">Description</div>
            <div className="col-span-2 text-center">Qty</div>
            <div className="col-span-2 text-right">Rate</div>
            <div className="col-span-2 text-right">Amount</div>
            <div className="col-span-1"></div>
          </div>
          
          {/* Line Items */}
          {data.lineItems.map((item, index) => (
            <div key={index} className="grid grid-cols-12 gap-3 items-center">
              <div className="col-span-5">
                <input 
                  type="text"
                  placeholder="Description of service/product"
                  value={item.description}
                  onChange={(e) => handleLineItemChange(index, 'description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div className="col-span-2">
                <input 
                  type="number"
                  min="0"
                  step="0.01"
                  value={item.quantity}
                  onChange={(e) => handleLineItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-center"
                />
              </div>
              
              <div className="col-span-2">
                <input 
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  value={item.rate}
                  onChange={(e) => handleLineItemChange(index, 'rate', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-right"
                />
              </div>
              
              <div className="col-span-2 text-right font-medium">
                ${item.amount.toFixed(2)}
              </div>
              
              <div className="col-span-1 text-center">
                <button 
                  type="button"
                  onClick={() => removeLineItem(index)}
                  className="p-1.5 text-red-600 hover:bg-red-50 rounded"
                  disabled={data.lineItems.length === 1}
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
        
        {/* Totals Section */}
        <div className="mt-6 pt-6 border-t">
          <div className="max-w-xs ml-auto space-y-2">
            <div className="flex justify-between text-sm">
              <span>Subtotal:</span>
              <span className="font-medium">${data.subtotal?.toFixed(2) || '0.00'}</span>
            </div>
            
            <div className="flex justify-between items-center text-sm">
              <label className="flex items-center">
                <span>Tax (</span>
                <input 
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={data.taxRate}
                  onChange={(e) => handleFieldChange('taxRate', parseFloat(e.target.value) || 0)}
                  className="w-16 mx-1 px-2 py-1 border border-gray-300 rounded text-center"
                />
                <span>%):</span>
              </label>
              <span className="font-medium">${data.tax?.toFixed(2) || '0.00'}</span>
            </div>
            
            <div className="flex justify-between text-lg font-bold pt-2 border-t">
              <span>Total:</span>
              <span>${data.total?.toFixed(2) || '0.00'}</span>
            </div>
          </div>
        </div>
      </Card>
      
      {/* Additional Information */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <DollarSign className="w-5 h-5 mr-2" />
          Additional Information
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
            <textarea 
              placeholder="Thank you for your business!"
              value={data.notes}
              onChange={(e) => handleFieldChange('notes', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Payment Terms</label>
            <select 
              value={data.paymentTerms}
              onChange={(e) => handleFieldChange('paymentTerms', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="Due on receipt">Due on receipt</option>
              <option value="Net 15 days">Net 15 days</option>
              <option value="Net 30 days">Net 30 days</option>
              <option value="Net 60 days">Net 60 days</option>
              <option value="Custom">Custom</option>
            </select>
          </div>
        </div>
      </Card>
    </form>
  )
}

export default InvoiceForm