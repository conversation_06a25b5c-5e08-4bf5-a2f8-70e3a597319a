# =================================================================
# TEMPLATE INVOICES - ENVIRONMENT VARIABLES
# =================================================================
# Copy this file to .env.local and fill in your actual values
# NEVER commit real credentials to version control

# =================================================================
# DATABASE CONFIGURATION
# =================================================================
# MongoDB Atlas connection string
# Get from: MongoDB Atlas Dashboard → Connect → Drivers
MONGODB_URI=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0

# =================================================================
# NEXTAUTH CONFIGURATION
# =================================================================
# Generate with: openssl rand -base64 32
# Or use: https://generate-secret.vercel.app/32
NEXTAUTH_SECRET=your-32-character-random-secret-here

# Your app's URL (different for each environment)
# Local: http://localhost:3000
# Production: https://templateinvoices.com
NEXTAUTH_URL=http://localhost:3000

# =================================================================
# GOOGLE OAUTH CONFIGURATION
# =================================================================
# Get from: Google Cloud Console → APIs & Services → Credentials
# Create OAuth 2.0 Client ID for web application
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your-google-client-secret

# =================================================================
# STRIPE CONFIGURATION (PAYMENTS)
# =================================================================
# Get from: Stripe Dashboard → Developers → API Keys
# Use test keys for development, live keys for production
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key

# Webhook secret from: Stripe Dashboard → Developers → Webhooks
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Pro plan price ID from: Stripe Dashboard → Products
# Create a $9.99/month recurring product and copy its price ID
STRIPE_PRO_PRICE_ID=price_your-pro-plan-price-id

# =================================================================
# EMAIL CONFIGURATION (INVOICE SENDING)
# =================================================================
# SMTP settings for sending invoice emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
# For Gmail: Generate App Password in Google Account settings
SMTP_PASS=your-app-password

# =================================================================
# AI CONFIGURATION
# =================================================================
# Get from: https://console.anthropic.com/
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key

# Alternative: OpenAI API (if using ChatGPT instead)
# OPENAI_API_KEY=sk-proj-your-openai-api-key

# =================================================================
# APPLICATION SETTINGS
# =================================================================
# Environment: development, production, test
NODE_ENV=development

# Your app's base URL (accessible in client-side code)
# Must match NEXTAUTH_URL value
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# =================================================================
# PUPPETEER CONFIGURATION (PDF GENERATION)
# =================================================================
# Set to true in production (Vercel), false for local development
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=false

# PDF generation timeout (milliseconds)
PDF_GENERATION_TIMEOUT=30000

# =================================================================
# SECURITY SETTINGS
# =================================================================
# Enable rate limiting for API endpoints
RATE_LIMIT_ENABLED=true

# Maximum file upload size (in MB)
MAX_FILE_SIZE_MB=5

# Session configuration
SESSION_MAX_AGE=86400

# File upload settings
UPLOAD_MAX_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# =================================================================
# FEATURE FLAGS
# =================================================================
# Enable/disable major features
ENABLE_AI_FEATURES=true
ENABLE_PDF_GENERATION=true
ENABLE_EMAIL_SENDING=true
ENABLE_STRIPE_PAYMENTS=true

# Public feature flags (accessible in client code)
NEXT_PUBLIC_AI_CONFIGURED=true
NEXT_PUBLIC_EMAIL_CONFIGURED=true
NEXT_PUBLIC_STRIPE_CONFIGURED=true

# Allow mock data for development/testing
NEXT_PUBLIC_ALLOW_MOCK_DATA=true

# =================================================================
# LOGGING AND MONITORING
# =================================================================
# Log level: error, warn, info, debug
LOG_LEVEL=info

# Enable detailed debug logging
ENABLE_DEBUG_LOGGING=false

# =================================================================
# PERFORMANCE SETTINGS
# =================================================================
# Email sending timeout (milliseconds)
EMAIL_SEND_TIMEOUT=15000

# API request timeouts
API_TIMEOUT=10000

# =================================================================
# DEVELOPMENT SETTINGS
# =================================================================
# Disable SSL verification (development only)
DISABLE_SSL_VERIFY=false

# Mock external services for testing
MOCK_EMAIL_SENDING=false
MOCK_PDF_GENERATION=false
MOCK_STRIPE_PAYMENTS=false

# =================================================================
# ENVIRONMENT-SPECIFIC EXAMPLES
# =================================================================

# LOCAL DEVELOPMENT (.env.local):
# NEXTAUTH_URL=http://localhost:3000
# NEXT_PUBLIC_BASE_URL=http://localhost:3000
# STRIPE_SECRET_KEY=sk_test_...
# NODE_ENV=development

# VERCEL PRODUCTION (Environment Variables):
# NEXTAUTH_URL=https://templateinvoices.com
# NEXT_PUBLIC_BASE_URL=https://templateinvoices.com
# STRIPE_SECRET_KEY=sk_live_...
# NODE_ENV=production
# PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# =================================================================
# SETUP CHECKLIST
# =================================================================
# □ MongoDB Atlas cluster created and connection string added
# □ Google OAuth client created and credentials added
# □ Stripe account setup with API keys and webhook configured
# □ Email SMTP settings configured (Gmail app password)
# □ Anthropic API key obtained
# □ All URLs updated for your domain
# □ Environment variables added to Vercel
# □ .env.local added to .gitignore
# □ Test all integrations work

# =================================================================
# IMPORTANT SECURITY NOTES
# =================================================================
# 1. Never commit this file with real values to Git
# 2. Use different databases for development/production
# 3. Use Stripe test keys in development
# 4. Regenerate all secrets if accidentally exposed
# 5. Enable 2FA on all service accounts
# 6. Regularly rotate API keys and passwords
# 7. Monitor usage and billing on all services