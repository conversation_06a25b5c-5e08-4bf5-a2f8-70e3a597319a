import { NextRequest, NextResponse } from 'next/server';
import { runMonthlyResetJob } from '@/lib/cron-jobs';

/**
 * API Route: Monthly Invoice Usage Reset Cron Job
 * GET /api/cron/monthly-reset
 * 
 * This endpoint should be called by a cron service (e.g., Vercel Cron)
 * to reset invoice usage for users whose billing cycle has ended.
 * 
 * Schedule: Daily at 00:00 UTC
 * Cron Expression: 0 0 * * *
 */
export async function GET(request: NextRequest) {
  try {
    // Verify this is an authorized cron request
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    // In production, enforce authentication
    if (process.env.NODE_ENV === 'production' && cronSecret) {
      if (authHeader !== `Bearer ${cronSecret}`) {
        console.warn('[CRON] Unauthorized monthly reset attempt');
        return new NextResponse('Unauthorized', { status: 401 });
      }
    }
    
    console.log('[CRON] Monthly reset cron job triggered');
    
    // Run the monthly reset job
    const result = await runMonthlyResetJob();
    
    // Log results
    if (result.success) {
      console.log(`[CRON] Monthly reset completed successfully: ${result.processed} users processed`);
    } else {
      console.error('[CRON] Monthly reset failed');
    }
    
    // Return results
    return NextResponse.json({
      success: result.success,
      timestamp: new Date().toISOString(),
      processed: result.processed,
      errors: result.errors,
      executionTimeMs: result.executionTime,
      message: result.success 
        ? `Successfully processed ${result.processed} users` 
        : 'Monthly reset job failed'
    });
    
  } catch (error) {
    console.error('[CRON] Fatal error in monthly reset job:', error);
    
    return NextResponse.json({
      success: false,
      timestamp: new Date().toISOString(),
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * POST method for manual triggering (admin only)
 * Useful for testing or manual intervention
 */
export async function POST(request: NextRequest) {
  try {
    // This should be heavily restricted in production
    const session = await request.json();
    
    // In production, verify admin access
    if (process.env.NODE_ENV === 'production') {
      // Add your admin verification logic here
      return new NextResponse('Forbidden', { status: 403 });
    }
    
    console.log('[CRON] Manual monthly reset triggered');
    
    const result = await runMonthlyResetJob();
    
    return NextResponse.json({
      success: result.success,
      timestamp: new Date().toISOString(),
      processed: result.processed,
      errors: result.errors,
      executionTimeMs: result.executionTime,
      message: 'Manual reset completed',
      triggeredBy: 'manual'
    });
    
  } catch (error) {
    console.error('[CRON] Error in manual monthly reset:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to execute manual reset'
    }, { status: 500 });
  }
}