// Payment Links Service for Template Invoice System
import Stripe from 'stripe';
import { InvoiceDocument } from './models';
import { ObjectId } from 'mongodb';
import clientPromise from './mongodb';
import { COLLECTIONS } from './models';
import { logActivity } from './user-service';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

// Payment link configuration
interface PaymentLinkConfig {
  currency?: string;
  allowPromotionCodes?: boolean;
  automaticTax?: boolean;
  collectionMethod?: 'send_invoice' | 'charge_automatically';
  paymentMethodTypes?: string[];
  submitType?: 'auto' | 'book' | 'donate' | 'pay';
}

// Payment link result
interface PaymentLinkResult {
  success: boolean;
  paymentLink?: Stripe.PaymentLink;
  url?: string;
  error?: string;
}

// Payment status update
interface PaymentUpdate {
  invoiceId: string | ObjectId;
  userId: string | ObjectId;
  stripePaymentIntentId?: string;
  stripeSessionId?: string;
  amountPaid: number;
  currency: string;
  paidAt: Date;
  paymentMethod?: string;
}

// Create Stripe payment link for invoice
export async function createPaymentLink(
  invoice: InvoiceDocument,
  config: PaymentLinkConfig = {}
): Promise<PaymentLinkResult> {
  try {
    if (!process.env.STRIPE_SECRET_KEY) {
      return {
        success: false,
        error: 'Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.'
      };
    }

    const {
      currency = invoice.currency || 'USD',
      allowPromotionCodes = false,
      automaticTax = false,
      paymentMethodTypes = ['card'],
      submitType = 'pay'
    } = config;

    // Create or get Stripe product for this invoice
    const product = await createOrGetStripeProduct(invoice);
    
    // Create price for the invoice total
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: Math.round(invoice.totals.total * 100), // Convert to cents
      currency: currency.toLowerCase(),
      metadata: {
        invoice_id: invoice._id?.toString() || '',
        invoice_number: invoice.invoiceNumber,
        user_id: invoice.userId?.toString() || ''
      }
    });

    // Create payment link
    const paymentLink = await stripe.paymentLinks.create({
      line_items: [
        {
          price: price.id,
          quantity: 1,
        },
      ],
      payment_method_types: paymentMethodTypes as any,
      allow_promotion_codes: allowPromotionCodes,
      automatic_tax: automaticTax ? { enabled: true } : undefined,
      submit_type: submitType,
      after_completion: {
        type: 'redirect',
        redirect: {
          url: `${process.env.NEXT_PUBLIC_BASE_URL}/payment/success?invoice=${invoice.invoiceNumber}`
        }
      },
      metadata: {
        invoice_id: invoice._id?.toString() || '',
        invoice_number: invoice.invoiceNumber,
        user_id: invoice.userId?.toString() || '',
        client_email: invoice.clientInfo.email || ''
      }
    });

    // Store payment link reference in database
    await storePaymentLinkReference(invoice, paymentLink);

    return {
      success: true,
      paymentLink,
      url: paymentLink.url
    };

  } catch (error) {
    console.error('Error creating payment link:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create payment link'
    };
  }
}

// Create or get Stripe product for invoice
async function createOrGetStripeProduct(invoice: InvoiceDocument): Promise<Stripe.Product> {
  const productName = `Invoice ${invoice.invoiceNumber}`;
  const productDescription = `Payment for invoice ${invoice.invoiceNumber} from ${invoice.businessInfo.name}`;

  try {
    // Create a new product for each invoice to ensure accurate tracking
    const product = await stripe.products.create({
      name: productName,
      description: productDescription,
      metadata: {
        invoice_id: invoice._id?.toString() || '',
        invoice_number: invoice.invoiceNumber,
        user_id: invoice.userId?.toString() || '',
        type: 'invoice_payment'
      }
    });

    return product;
  } catch (error) {
    console.error('Error creating Stripe product:', error);
    throw error;
  }
}

// Store payment link reference in database
async function storePaymentLinkReference(
  invoice: InvoiceDocument,
  paymentLink: Stripe.PaymentLink
): Promise<void> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const invoicesCollection = db.collection(COLLECTIONS.INVOICES);

    await invoicesCollection.updateOne(
      { _id: invoice._id },
      {
        $set: {
          'payment.stripePaymentLinkId': paymentLink.id,
          'payment.stripePaymentLinkUrl': paymentLink.url,
          'payment.linkCreatedAt': new Date(),
          updatedAt: new Date()
        }
      }
    );

    // Log activity
    await logActivity({
      userId: invoice.userId,
      action: 'payment_link_created',
      resourceType: 'invoice',
      resourceId: invoice._id,
      details: {
        description: `Payment link created for invoice ${invoice.invoiceNumber}`,
        metadata: {
          stripePaymentLinkId: paymentLink.id,
          amount: invoice.totals.total,
          currency: invoice.currency
        }
      }
    });

  } catch (error) {
    console.error('Error storing payment link reference:', error);
    throw error;
  }
}

// Get payment link for invoice
export async function getPaymentLink(
  invoiceId: string | ObjectId,
  userId: string | ObjectId
): Promise<{ url?: string; isActive?: boolean; error?: string }> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const invoicesCollection = db.collection(COLLECTIONS.INVOICES);

    const invoiceObjectId = typeof invoiceId === 'string' ? new ObjectId(invoiceId) : invoiceId;
    const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;

    const invoice = await invoicesCollection.findOne({
      _id: invoiceObjectId,
      userId: userObjectId
    });

    if (!invoice) {
      return { error: 'Invoice not found' };
    }

    const paymentLinkId = invoice.payment?.stripePaymentLinkId;
    if (!paymentLinkId) {
      return { error: 'No payment link found for this invoice' };
    }

    // Check if payment link is still active in Stripe
    try {
      const paymentLink = await stripe.paymentLinks.retrieve(paymentLinkId);
      return {
        url: paymentLink.url,
        isActive: paymentLink.active
      };
    } catch (stripeError) {
      return { error: 'Payment link is no longer valid' };
    }

  } catch (error) {
    console.error('Error retrieving payment link:', error);
    return { error: 'Failed to retrieve payment link' };
  }
}

// Update invoice payment status from Stripe webhook
export async function updatePaymentStatus(
  paymentUpdate: PaymentUpdate
): Promise<{ success: boolean; error?: string }> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const invoicesCollection = db.collection(COLLECTIONS.INVOICES);

    const invoiceObjectId = typeof paymentUpdate.invoiceId === 'string' 
      ? new ObjectId(paymentUpdate.invoiceId) 
      : paymentUpdate.invoiceId;
    
    const userObjectId = typeof paymentUpdate.userId === 'string' 
      ? new ObjectId(paymentUpdate.userId) 
      : paymentUpdate.userId;

    // Update invoice status
    const result = await invoicesCollection.findOneAndUpdate(
      { 
        _id: invoiceObjectId,
        userId: userObjectId 
      },
      {
        $set: {
          status: 'paid',
          paidAt: paymentUpdate.paidAt,
          'payment.stripePaymentIntentId': paymentUpdate.stripePaymentIntentId,
          'payment.stripeSessionId': paymentUpdate.stripeSessionId,
          'payment.amountPaid': paymentUpdate.amountPaid,
          'payment.currency': paymentUpdate.currency,
          'payment.paymentMethod': paymentUpdate.paymentMethod,
          'payment.paidAt': paymentUpdate.paidAt,
          updatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    if (!result) {
      return { success: false, error: 'Invoice not found' };
    }

    // Log payment activity
    await logActivity({
      userId: userObjectId,
      action: 'payment_received',
      resourceType: 'invoice',
      resourceId: invoiceObjectId,
      details: {
        description: `Payment received for invoice ${result.value?.invoiceNumber || 'Unknown'}`,
        metadata: {
          amountPaid: paymentUpdate.amountPaid,
          currency: paymentUpdate.currency,
          paymentMethod: paymentUpdate.paymentMethod,
          stripePaymentIntentId: paymentUpdate.stripePaymentIntentId
        }
      }
    });

    return { success: true };

  } catch (error) {
    console.error('Error updating payment status:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update payment status' 
    };
  }
}

// Handle Stripe webhook events
export async function handleStripeWebhook(
  event: Stripe.Event
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        return await handleCheckoutSessionCompleted(session);
      }

      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        return await handlePaymentIntentSucceeded(paymentIntent);
      }

      case 'payment_link.payment.completed' as any: {
        const paymentLink = event.data.object as any; // Stripe type might not be complete
        return await handlePaymentLinkCompleted(paymentLink);
      }

      default:
        return {
          success: true,
          message: `Unhandled event type: ${event.type}`
        };
    }

  } catch (error) {
    console.error('Error handling Stripe webhook:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Webhook processing failed'
    };
  }
}

// Handle completed checkout session
async function handleCheckoutSessionCompleted(
  session: Stripe.Checkout.Session
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    const invoiceId = session.metadata?.invoice_id;
    const userId = session.metadata?.user_id;

    if (!invoiceId || !userId) {
      return {
        success: false,
        error: 'Missing invoice or user ID in session metadata'
      };
    }

    const paymentUpdate: PaymentUpdate = {
      invoiceId,
      userId,
      stripeSessionId: session.id,
      stripePaymentIntentId: session.payment_intent as string,
      amountPaid: (session.amount_total || 0) / 100, // Convert from cents
      currency: session.currency || 'USD',
      paidAt: new Date(),
      paymentMethod: 'stripe_checkout'
    };

    const result = await updatePaymentStatus(paymentUpdate);
    
    if (result.success) {
      return {
        success: true,
        message: `Payment processed for invoice ${invoiceId}`
      };
    } else {
      return result;
    }

  } catch (error) {
    console.error('Error handling checkout session:', error);
    return {
      success: false,
      error: 'Failed to process checkout session'
    };
  }
}

// Handle successful payment intent
async function handlePaymentIntentSucceeded(
  paymentIntent: Stripe.PaymentIntent
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    const invoiceId = paymentIntent.metadata?.invoice_id;
    const userId = paymentIntent.metadata?.user_id;

    if (!invoiceId || !userId) {
      return {
        success: true,
        message: 'Payment intent not related to invoice'
      };
    }

    const paymentUpdate: PaymentUpdate = {
      invoiceId,
      userId,
      stripePaymentIntentId: paymentIntent.id,
      amountPaid: paymentIntent.amount_received / 100, // Convert from cents
      currency: paymentIntent.currency,
      paidAt: new Date(),
      paymentMethod: paymentIntent.payment_method_types?.[0] || 'card'
    };

    const result = await updatePaymentStatus(paymentUpdate);
    
    if (result.success) {
      return {
        success: true,
        message: `Payment intent processed for invoice ${invoiceId}`
      };
    } else {
      return result;
    }

  } catch (error) {
    console.error('Error handling payment intent:', error);
    return {
      success: false,
      error: 'Failed to process payment intent'
    };
  }
}

// Handle payment link completion
async function handlePaymentLinkCompleted(
  paymentLinkEvent: any
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    const invoiceId = paymentLinkEvent.metadata?.invoice_id;
    const userId = paymentLinkEvent.metadata?.user_id;

    if (!invoiceId || !userId) {
      return {
        success: false,
        error: 'Missing invoice or user ID in payment link metadata'
      };
    }

    // Payment link events might need different handling
    // This is a placeholder for the actual implementation
    return {
      success: true,
      message: `Payment link completed for invoice ${invoiceId}`
    };

  } catch (error) {
    console.error('Error handling payment link completion:', error);
    return {
      success: false,
      error: 'Failed to process payment link completion'
    };
  }
}

// Create payment button HTML for embedding
export function createPaymentButtonHTML(
  paymentUrl: string,
  invoice: InvoiceDocument,
  options: {
    buttonText?: string;
    buttonColor?: string;
    buttonSize?: 'small' | 'medium' | 'large';
  } = {}
): string {
  const {
    buttonText = 'Pay Now',
    buttonColor = '#3b82f6',
    buttonSize = 'medium'
  } = options;

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const sizeStyles = {
    small: 'padding: 8px 16px; font-size: 14px;',
    medium: 'padding: 12px 24px; font-size: 16px;',
    large: 'padding: 16px 32px; font-size: 18px;'
  };

  return `
    <div style="text-align: center; margin: 20px 0;">
      <a 
        href="${paymentUrl}" 
        style="
          display: inline-block;
          background-color: ${buttonColor};
          color: white;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 500;
          ${sizeStyles[buttonSize]}
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          transition: background-color 0.2s;
        "
        onmouseover="this.style.backgroundColor='#2563eb'"
        onmouseout="this.style.backgroundColor='${buttonColor}'"
      >
        ${buttonText} - ${formatCurrency(invoice.totals.total, invoice.currency)}
      </a>
      <p style="margin-top: 10px; color: #6b7280; font-size: 14px;">
        Secure payment powered by Stripe
      </p>
    </div>
  `;
}

// Get payment analytics for user
export async function getPaymentAnalytics(
  userId: string | ObjectId,
  dateRange?: { start: Date; end: Date }
): Promise<{
  totalPaid: number;
  totalPending: number;
  paymentMethods: Record<string, number>;
  monthlyRevenue: Array<{ month: string; amount: number }>;
}> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const invoicesCollection = db.collection(COLLECTIONS.INVOICES);

    const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    const matchStage: any = { userId: userObjectId };
    
    if (dateRange) {
      matchStage.createdAt = {
        $gte: dateRange.start,
        $lte: dateRange.end
      };
    }

    const analytics = await invoicesCollection.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalPaid: {
            $sum: {
              $cond: [{ $eq: ['$status', 'paid'] }, '$totals.total', 0]
            }
          },
          totalPending: {
            $sum: {
              $cond: [{ $in: ['$status', ['sent', 'draft']] }, '$totals.total', 0]
            }
          },
          paymentMethods: {
            $push: {
              $cond: [
                { $eq: ['$status', 'paid'] },
                '$payment.paymentMethod',
                null
              ]
            }
          }
        }
      }
    ]).toArray();

    const result = analytics[0] || {
      totalPaid: 0,
      totalPending: 0,
      paymentMethods: []
    };

    // Process payment methods
    const paymentMethodCounts: Record<string, number> = {};
    result.paymentMethods.filter(Boolean).forEach((method: string) => {
      paymentMethodCounts[method] = (paymentMethodCounts[method] || 0) + 1;
    });

    // Get monthly revenue (placeholder - would need more complex aggregation)
    const monthlyRevenue: Array<{ month: string; amount: number }> = [];

    return {
      totalPaid: result.totalPaid,
      totalPending: result.totalPending,
      paymentMethods: paymentMethodCounts,
      monthlyRevenue
    };

  } catch (error) {
    console.error('Error getting payment analytics:', error);
    return {
      totalPaid: 0,
      totalPending: 0,
      paymentMethods: {},
      monthlyRevenue: []
    };
  }
}

// Validate Stripe configuration
export async function validateStripeConfig(): Promise<{ isValid: boolean; error?: string }> {
  try {
    if (!process.env.STRIPE_SECRET_KEY) {
      return {
        isValid: false,
        error: 'STRIPE_SECRET_KEY environment variable is not set'
      };
    }

    // Test Stripe connection
    await stripe.accounts.retrieve();
    
    return { isValid: true };

  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Stripe configuration error'
    };
  }
}