// Invoice-related type definitions

export interface LineItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

export interface InvoiceData {
  businessDescription: string;
  template: any | null;
  
  // Business Info
  businessName: string;
  businessEmail: string;
  businessAddress: string;
  
  // Client Info
  clientName: string;
  clientEmail: string;
  clientAddress: string;
  
  // Invoice Details
  invoiceNumber: string;
  invoiceDate: string;
  dueDate: string;
  
  // Line Items
  lineItems: LineItem[];
  
  // Totals
  subtotal: number;
  tax: number;
  taxRate: number;
  total: number;
  
  // Payment
  paymentTerms: string;
  notes: string;
}

export interface StepProps {
  data: InvoiceData;
  updateData: (updates: Partial<InvoiceData>) => void;
  onNext: () => void;
  onPrev?: () => void;
}

export interface ProgressStep {
  name: string;
  icon: any;
}