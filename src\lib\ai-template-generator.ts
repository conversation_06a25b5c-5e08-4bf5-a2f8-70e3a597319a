/**
 * AI-Powered Template Generator
 * 
 * Comprehensive system for analyzing business descriptions and generating 
 * customized invoice templates using AI analysis and template optimization.
 */

// Note: All database operations should be handled via API routes
// This file should only contain client-safe code

// ==================== INTERFACES ====================

interface BusinessAnalysis {
  industry: IndustryCategory;
  businessType: BusinessType;
  serviceTypes: ServiceType[];
  billingStructure: BillingStructure;
  customFieldSuggestions: CustomFieldSuggestion[];
  professionalTerminology: ProfessionalTerminology;
  confidence: number;
  keywordMatches: KeywordMatch[];
  aiInsights: AIInsight[];
}

type IndustryCategory = 
  | 'technology' | 'creative' | 'consulting' | 'construction' | 'healthcare'
  | 'education' | 'legal' | 'automotive' | 'beauty' | 'hospitality'
  | 'retail' | 'real_estate' | 'finance' | 'marketing' | 'photography'
  | 'music' | 'food_service' | 'fitness' | 'cleaning' | 'pet_care'
  | 'nonprofit' | 'government' | 'entertainment' | 'manufacturing' | 'general';

type BusinessType = 
  | 'freelancer' | 'agency' | 'consultant' | 'service_provider'
  | 'product_business' | 'creative_professional' | 'technical_professional'
  | 'trade_professional' | 'brick_and_mortar' | 'online_business';

type ServiceType =
  | 'hourly_services' | 'project_based' | 'retainer_services' | 'product_sales'
  | 'subscription' | 'package_deals' | 'consulting' | 'creative_deliverables'
  | 'maintenance' | 'training' | 'milestone' | 'emergency_services';

interface BillingStructure {
  primaryModel: BillingModel;
  secondaryModel?: BillingModel;
  paymentSchedule: PaymentSchedule;
  typicalInvoiceAmount: AmountRange;
  invoiceFrequency: InvoiceFrequency;
  suggestedTerms: string[];
}

type BillingModel = 
  | 'hourly' | 'fixed_project' | 'retainer' | 'subscription'
  | 'per_unit' | 'milestone' | 'package' | 'hybrid' | 'value_based';

type PaymentSchedule = 
  | 'upfront' | 'net_15' | 'net_30' | 'net_60' | '50_50'
  | 'milestone_based' | 'monthly' | 'upon_completion' | 'due_on_receipt';

interface AmountRange {
  min: number;
  max: number;
  typical: number;
  currency: string;
}

type InvoiceFrequency = 
  | 'one_time' | 'weekly' | 'bi_weekly' | 'monthly'
  | 'quarterly' | 'project_completion' | 'milestone_based' | 'as_needed';

interface CustomFieldSuggestion {
  fieldName: string;
  fieldType: FieldType;
  required: boolean;
  category: FieldCategory;
  defaultValue?: string;
  options?: string[];
  validation?: FieldValidation;
  aiReasoning: string;
  industryStandard: boolean;
}

type FieldType = 
  | 'text' | 'number' | 'currency' | 'date' | 'time' | 'datetime'
  | 'select' | 'multiselect' | 'textarea' | 'checkbox' | 'file' | 'signature';

type FieldCategory = 
  | 'client_info' | 'project_details' | 'service_specifics' | 'billing_info'
  | 'legal_compliance' | 'delivery_info' | 'industry_specific' | 'custom_branding';

interface FieldValidation {
  min?: number;
  max?: number;
  pattern?: string;
  required?: boolean;
  customMessage?: string;
}

interface ProfessionalTerminology {
  clientTitle: string;
  projectTitle: string;
  serviceTitle: string;
  invoiceTitle: string;
  lineItemTitle: string;
  additionalTerms: Record<string, string>;
}

interface KeywordMatch {
  keyword: string;
  category: string;
  weight: number;
  confidence: number;
}

interface AIInsight {
  type: 'recommendation' | 'warning' | 'optimization' | 'compliance';
  message: string;
  actionable: boolean;
  priority: 'low' | 'medium' | 'high';
}

interface GeneratedTemplate {
  templateId: string;
  templateName: string;
  description: string;
  industry: IndustryCategory;
  businessType: BusinessType;
  baseTemplateId?: string;
  customizations: TemplateCustomization[];
  fieldDefinitions: FieldDefinition[];
  sampleContent: SampleContent;
  stylingPreferences: StylingPreferences;
  industrySpecificElements: IndustrySpecificElements;
  aiConfidence: number;
  fallbackApplied: boolean;
}

interface TemplateCustomization {
  section: string;
  modification: 'add' | 'remove' | 'modify' | 'reorder';
  details: any;
  reasoning: string;
}

interface FieldDefinition {
  id: string;
  label: string;
  type: FieldType;
  category: FieldCategory;
  required: boolean;
  defaultValue?: any;
  placeholder?: string;
  helpText?: string;
  validation?: FieldValidation;
  displayCondition?: string;
  options?: FieldOption[];
  aiGenerated: boolean;
}

interface FieldOption {
  value: string;
  label: string;
  isDefault?: boolean;
}

interface SampleContent {
  businessInfo: Record<string, string>;
  clientInfo: Record<string, string>;
  lineItems: SampleLineItem[];
  customFields: Record<string, any>;
  industrySpecificData: Record<string, any>;
}

interface SampleLineItem {
  description: string;
  quantity?: number;
  rate?: number;
  amount: number;
  category?: string;
  date?: string;
  unit?: string;
}

interface StylingPreferences {
  colorScheme: ColorScheme;
  typography: Typography;
  branding: BrandingOptions;
  visualElements: VisualElements;
  layoutPreferences: LayoutPreferences;
}

interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  text: string;
  background: string;
  borders: string;
  success: string;
  warning: string;
}

interface Typography {
  headingFont: string;
  bodyFont: string;
  fontSize: {
    base: number;
    heading1: number;
    heading2: number;
    small: number;
  };
  fontWeights: Record<string, number>;
}

interface BrandingOptions {
  logoPlacement: boolean;
  brandColors: boolean;
  customFonts: boolean;
  watermark: boolean;
  professionalLevel: 'minimal' | 'standard' | 'premium';
}

interface VisualElements {
  borderRadius: number;
  shadowDepth: 'none' | 'light' | 'medium' | 'heavy';
  dividerStyle: 'none' | 'solid' | 'dashed' | 'dotted';
  iconSet: 'none' | 'minimal' | 'detailed';
  tableStyle: 'minimal' | 'bordered' | 'striped' | 'modern';
}

interface LayoutPreferences {
  headerStyle: 'minimal' | 'detailed' | 'corporate';
  itemsTableLayout: 'simple' | 'detailed' | 'categorized';
  totalsPosition: 'right' | 'center' | 'full_width';
  notesPosition: 'top' | 'bottom' | 'sidebar';
}

interface IndustrySpecificElements {
  specialClauses: string[];
  requiredFields: string[];
  commonAddOns: AddOn[];
  complianceNotes: string[];
  industryTerms: Record<string, string>;
  paymentMethods: string[];
  typicalDeliveryTerms: string[];
}

interface AddOn {
  id: string;
  name: string;
  description: string;
  defaultPrice?: number;
  frequency?: string;
}

// ==================== BUSINESS ANALYSIS ENGINE ====================

/**
 * Main business analysis function with comprehensive keyword detection
 */
function analyzeBusinessDescription(description: string): BusinessAnalysis {
  const cleanDesc = cleanDescription(description);
  const keywords = extractKeywords(cleanDesc);
  
  // Core analysis
  const industry = detectIndustryAdvanced(cleanDesc, keywords);
  const businessType = detectBusinessTypeAdvanced(cleanDesc, keywords);
  const serviceTypes = detectServiceTypesAdvanced(cleanDesc, keywords, industry);
  const billingStructure = analyzeBillingStructureAdvanced(cleanDesc, industry, businessType);
  
  // AI-powered suggestions
  const customFieldSuggestions = generateSmartFieldSuggestions(
    industry, businessType, serviceTypes, cleanDesc
  );
  const professionalTerminology = getProfessionalTerminology(industry, businessType);
  
  // Generate insights
  const aiInsights = generateAIInsights(cleanDesc, industry, businessType, serviceTypes);
  const keywordMatches = analyzeKeywordMatches(keywords, industry);
  
  // Calculate confidence
  const confidence = calculateAdvancedConfidence(
    industry, businessType, serviceTypes, keywordMatches, cleanDesc
  );

  return {
    industry,
    businessType,
    serviceTypes,
    billingStructure,
    customFieldSuggestions,
    professionalTerminology,
    confidence,
    keywordMatches,
    aiInsights
  };
}

/**
 * Clean and normalize business description
 */
function cleanDescription(description: string): string {
  return description
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Extract meaningful keywords from description
 */
function extractKeywords(description: string): string[] {
  const stopWords = new Set([
    'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
    'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has',
    'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
    'might', 'must', 'can', 'a', 'an', 'this', 'that', 'these', 'those'
  ]);

  return description
    .split(' ')
    .filter(word => word.length > 2 && !stopWords.has(word))
    .slice(0, 50); // Limit for performance
}

/**
 * Advanced industry detection with weighted scoring
 */
function detectIndustryAdvanced(description: string, keywords: string[]): IndustryCategory {
  const industryDatabase: Partial<Record<IndustryCategory, {
    primary: string[];
    secondary: string[];
    weight: number;
  }>> = {
    technology: {
      primary: ['software', 'developer', 'programming', 'app', 'website', 'IT', 'tech', 'coding', 'database', 'API', 'cloud', 'cyber', 'data', 'algorithm', 'artificial intelligence', 'machine learning'],
      secondary: ['digital', 'online', 'computer', 'system', 'platform', 'automation', 'integration', 'mobile', 'web'],
      weight: 1.0
    },
    creative: {
      primary: ['design', 'creative', 'art', 'graphic', 'branding', 'logo', 'illustration', 'animation', 'UX', 'UI', 'video editing', 'advertising', 'content creation'],
      secondary: ['visual', 'aesthetic', 'creative', 'artistic', 'portfolio', 'brand'],
      weight: 1.0
    },
    consulting: {
      primary: ['consulting', 'consultant', 'advisory', 'strategy', 'management', 'business analysis', 'coach', 'mentor', 'expert advice'],
      secondary: ['guidance', 'analysis', 'optimization', 'improvement', 'strategic'],
      weight: 1.0
    },
    healthcare: {
      primary: ['health', 'medical', 'therapy', 'clinic', 'patient', 'treatment', 'wellness', 'nursing', 'dental', 'mental health', 'healthcare', 'medicine'],
      secondary: ['care', 'healing', 'diagnosis', 'prescription', 'recovery'],
      weight: 1.0
    },
    construction: {
      primary: ['construction', 'contractor', 'building', 'renovation', 'remodel', 'carpentry', 'plumbing', 'electrical', 'HVAC', 'roofing', 'masonry'],
      secondary: ['repair', 'installation', 'maintenance', 'infrastructure', 'project'],
      weight: 1.0
    },
    education: {
      primary: ['education', 'teaching', 'tutor', 'training', 'course', 'lesson', 'workshop', 'instructor', 'academic', 'school', 'learning'],
      secondary: ['knowledge', 'skills', 'development', 'curriculum', 'student'],
      weight: 1.0
    },
    legal: {
      primary: ['legal', 'law', 'attorney', 'lawyer', 'paralegal', 'litigation', 'contract', 'compliance', 'notary', 'legal advice'],
      secondary: ['court', 'case', 'documentation', 'regulation', 'rights'],
      weight: 1.0
    },
    // Add more industries...
    general: {
      primary: [],
      secondary: [],
      weight: 0.1
    }
  };

  let bestMatch: IndustryCategory = 'general';
  let highestScore = 0;

  for (const [industry, data] of Object.entries(industryDatabase)) {
    const primaryScore = data.primary.filter(keyword => 
      keywords.some(k => k.includes(keyword) || keyword.includes(k))
    ).length * 3;
    
    const secondaryScore = data.secondary.filter(keyword => 
      keywords.some(k => k.includes(keyword) || keyword.includes(k))
    ).length * 1;
    
    const totalScore = (primaryScore + secondaryScore) * data.weight;
    
    if (totalScore > highestScore) {
      highestScore = totalScore;
      bestMatch = industry as IndustryCategory;
    }
  }

  return bestMatch;
}

/**
 * Advanced business type detection
 */
function detectBusinessTypeAdvanced(description: string, keywords: string[]): BusinessType {
  const typePatterns: Partial<Record<BusinessType, {
    indicators: string[];
    weight: number;
  }>> = {
    freelancer: {
      indicators: ['freelance', 'freelancer', 'independent', 'solo', 'individual', 'self-employed'],
      weight: 2.0
    },
    agency: {
      indicators: ['agency', 'studio', 'firm', 'company', 'team', 'group', 'collective'],
      weight: 2.0
    },
    consultant: {
      indicators: ['consultant', 'consulting', 'advisor', 'expert', 'specialist', 'advisory'],
      weight: 2.0
    },
    service_provider: {
      indicators: ['service', 'provider', 'professional', 'contractor', 'services'],
      weight: 1.0
    },
    product_business: {
      indicators: ['product', 'sell', 'retail', 'e-commerce', 'shop', 'store', 'merchandise'],
      weight: 1.5
    },
    creative_professional: {
      indicators: ['creative', 'artist', 'designer', 'photographer', 'creative professional'],
      weight: 1.5
    },
    technical_professional: {
      indicators: ['developer', 'engineer', 'technical', 'IT professional', 'programmer'],
      weight: 1.5
    },
    trade_professional: {
      indicators: ['plumber', 'electrician', 'carpenter', 'contractor', 'builder', 'tradesperson'],
      weight: 1.5
    }
  };

  let bestMatch: BusinessType = 'service_provider';
  let highestScore = 0;

  for (const [type, data] of Object.entries(typePatterns)) {
    const score = data.indicators.filter(indicator => 
      keywords.some(k => k.includes(indicator) || indicator.includes(k))
    ).length * data.weight;
    
    if (score > highestScore) {
      highestScore = score;
      bestMatch = type as BusinessType;
    }
  }

  return bestMatch;
}

/**
 * Advanced service type detection with context awareness
 */
function detectServiceTypesAdvanced(
  description: string, 
  keywords: string[], 
  industry: IndustryCategory
): ServiceType[] {
  const serviceTypes: ServiceType[] = [];
  
  const patterns: Partial<Record<ServiceType, {
    indicators: string[];
    industries: IndustryCategory[];
  }>> = {
    hourly_services: {
      indicators: ['hourly', 'per hour', 'hour rate', 'time-based', 'hourly billing'],
      industries: ['consulting', 'legal', 'creative', 'technology']
    },
    project_based: {
      indicators: ['project', 'fixed price', 'flat rate', 'per project', 'one-time'],
      industries: ['construction', 'creative', 'technology', 'marketing']
    },
    retainer_services: {
      indicators: ['retainer', 'monthly fee', 'ongoing', 'recurring', 'monthly retainer'],
      industries: ['consulting', 'legal', 'marketing', 'creative']
    },
    subscription: {
      indicators: ['subscription', 'monthly plan', 'membership', 'recurring payment'],
      industries: ['technology', 'fitness', 'education']
    },
    package_deals: {
      indicators: ['package', 'bundle', 'combo', 'plan', 'tier', 'package deal'],
      industries: ['marketing', 'photography', 'consulting']
    },
    consulting: {
      indicators: ['consulting', 'advisory', 'consultation', 'advice', 'strategic guidance'],
      industries: ['consulting', 'finance', 'legal', 'technology']
    },
    // Add more patterns...
  };

  for (const [type, data] of Object.entries(patterns)) {
    const hasKeywords = data.indicators.some(indicator => 
      keywords.some(k => k.includes(indicator) || indicator.includes(k))
    );
    
    const industryMatch = data.industries.includes(industry);
    
    if (hasKeywords || (industryMatch && Math.random() > 0.7)) {
      serviceTypes.push(type as ServiceType);
    }
  }

  // Default fallback
  if (serviceTypes.length === 0) {
    serviceTypes.push('project_based');
  }

  return Array.from(new Set(serviceTypes)); // Remove duplicates
}

/**
 * Advanced billing structure analysis
 */
function analyzeBillingStructureAdvanced(
  description: string,
  industry: IndustryCategory,
  businessType: BusinessType
): BillingStructure {
  const industryDefaults: Partial<Record<IndustryCategory, {
    primaryModel: BillingModel;
    paymentSchedule: PaymentSchedule;
    frequency: InvoiceFrequency;
    amount: { min: number; max: number; typical: number; currency: string; };
  }>> = {
    technology: {
      primaryModel: 'hourly' as BillingModel,
      paymentSchedule: 'net_30' as PaymentSchedule,
      frequency: 'monthly' as InvoiceFrequency,
      amount: { min: 1000, max: 25000, typical: 5000, currency: 'USD' }
    },
    consulting: {
      primaryModel: 'retainer' as BillingModel,
      paymentSchedule: 'net_15' as PaymentSchedule,
      frequency: 'monthly' as InvoiceFrequency,
      amount: { min: 2000, max: 50000, typical: 10000, currency: 'USD' }
    },
    creative: {
      primaryModel: 'fixed_project' as BillingModel,
      paymentSchedule: '50_50' as PaymentSchedule,
      frequency: 'project_completion' as InvoiceFrequency,
      amount: { min: 500, max: 15000, typical: 3000, currency: 'USD' }
    },
    // Add more industry defaults...
  };

  const defaults = industryDefaults[industry] || {
    primaryModel: 'fixed_project' as BillingModel,
    paymentSchedule: 'net_30' as PaymentSchedule,
    frequency: 'project_completion' as InvoiceFrequency,
    amount: { min: 500, max: 10000, typical: 2500, currency: 'USD' }
  };

  const suggestedTerms = generatePaymentTerms(industry, businessType);

  return {
    primaryModel: defaults.primaryModel,
    paymentSchedule: defaults.paymentSchedule,
    typicalInvoiceAmount: defaults.amount,
    invoiceFrequency: defaults.frequency,
    suggestedTerms
  };
}

/**
 * Generate smart field suggestions based on AI analysis
 */
function generateSmartFieldSuggestions(
  industry: IndustryCategory,
  businessType: BusinessType,
  serviceTypes: ServiceType[],
  description: string
): CustomFieldSuggestion[] {
  const suggestions: CustomFieldSuggestion[] = [];

  // Industry-specific fields
  const industryFields = getIndustrySpecificFields(industry);
  suggestions.push(...industryFields);

  // Service type fields
  for (const serviceType of serviceTypes) {
    const serviceFields = getServiceTypeFields(serviceType);
    suggestions.push(...serviceFields);
  }

  // AI-generated custom fields based on description analysis
  const aiFields = generateAICustomFields(description, industry);
  suggestions.push(...aiFields);

  // Remove duplicates and prioritize
  return prioritizeAndDeduplicateFields(suggestions);
}

/**
 * Get industry-specific field suggestions
 */
function getIndustrySpecificFields(industry: IndustryCategory): CustomFieldSuggestion[] {
  const fieldDatabase: Partial<Record<IndustryCategory, CustomFieldSuggestion[]>> = {
    healthcare: [
      {
        fieldName: 'Patient ID',
        fieldType: 'text' as FieldType,
        required: true,
        category: 'client_info' as FieldCategory,
        aiReasoning: 'Healthcare requires patient identification for compliance',
        industryStandard: true
      },
      {
        fieldName: 'Insurance Provider',
        fieldType: 'text' as FieldType,
        required: false,
        category: 'billing_info' as FieldCategory,
        aiReasoning: 'Insurance information is crucial for healthcare billing',
        industryStandard: true
      },
      {
        fieldName: 'Treatment Date',
        fieldType: 'date' as FieldType,
        required: true,
        category: 'service_specifics' as FieldCategory,
        aiReasoning: 'Treatment dates are required for medical billing',
        industryStandard: true
      }
    ],
    legal: [
      {
        fieldName: 'Case Number',
        fieldType: 'text' as FieldType,
        required: true,
        category: 'project_details' as FieldCategory,
        aiReasoning: 'Case numbers are essential for legal matter tracking',
        industryStandard: true
      },
      {
        fieldName: 'Attorney License Number',
        fieldType: 'text' as FieldType,
        required: true,
        category: 'legal_compliance' as FieldCategory,
        aiReasoning: 'License numbers may be required for compliance',
        industryStandard: true
      }
    ],
    construction: [
      {
        fieldName: 'Permit Number',
        fieldType: 'text' as FieldType,
        required: false,
        category: 'legal_compliance' as FieldCategory,
        aiReasoning: 'Permit numbers important for construction projects',
        industryStandard: true
      },
      {
        fieldName: 'Project Address',
        fieldType: 'textarea' as FieldType,
        required: true,
        category: 'project_details' as FieldCategory,
        aiReasoning: 'Project location is crucial for construction billing',
        industryStandard: true
      }
    ],
    // Add more industries...
  };

  return fieldDatabase[industry] || [];
}

/**
 * Get service type specific fields
 */
function getServiceTypeFields(serviceType: ServiceType): CustomFieldSuggestion[] {
  const serviceFields: Partial<Record<ServiceType, CustomFieldSuggestion[]>> = {
    hourly_services: [
      {
        fieldName: 'Hours Worked',
        fieldType: 'number' as FieldType,
        required: true,
        category: 'service_specifics' as FieldCategory,
        aiReasoning: 'Essential for hourly billing calculations',
        industryStandard: true
      },
      {
        fieldName: 'Hourly Rate',
        fieldType: 'currency' as FieldType,
        required: true,
        category: 'billing_info' as FieldCategory,
        aiReasoning: 'Required for hourly service pricing',
        industryStandard: true
      }
    ],
    subscription: [
      {
        fieldName: 'Subscription Period',
        fieldType: 'select' as FieldType,
        required: true,
        category: 'billing_info' as FieldCategory,
        options: ['Monthly', 'Quarterly', 'Annual'],
        aiReasoning: 'Subscription billing requires defined periods',
        industryStandard: true
      }
    ],
    // Add more service types...
  };

  return serviceFields[serviceType] || [];
}

/**
 * Generate AI-powered custom fields based on description analysis
 */
function generateAICustomFields(
  description: string,
  industry: IndustryCategory
): CustomFieldSuggestion[] {
  // This would integrate with an AI service like Claude API
  // For now, we'll use heuristic analysis
  
  const fields: CustomFieldSuggestion[] = [];
  
  // Look for specific keywords that suggest custom fields
  const keywords = extractKeywords(description);
  
  if (keywords.includes('deadline') || keywords.includes('rush')) {
    fields.push({
      fieldName: 'Rush Delivery',
      fieldType: 'checkbox',
      required: false,
      category: 'service_specifics',
      aiReasoning: 'Business mentions deadlines/rush work, suggesting need for rush delivery tracking',
      industryStandard: false
    });
  }

  if (keywords.includes('location') || keywords.includes('travel')) {
    fields.push({
      fieldName: 'Service Location',
      fieldType: 'text',
      required: false,
      category: 'delivery_info',
      aiReasoning: 'Business mentions location-based services',
      industryStandard: false
    });
  }

  // Future: Integrate with actual AI API for more sophisticated analysis
  
  return fields;
}

/**
 * Prioritize and remove duplicate field suggestions
 */
function prioritizeAndDeduplicateFields(fields: CustomFieldSuggestion[]): CustomFieldSuggestion[] {
  const seen = new Set<string>();
  const deduplicated = fields.filter(field => {
    const key = field.fieldName.toLowerCase();
    if (seen.has(key)) return false;
    seen.add(key);
    return true;
  });

  // Sort by industry standard first, then required fields
  return deduplicated.sort((a, b) => {
    if (a.industryStandard !== b.industryStandard) {
      return a.industryStandard ? -1 : 1;
    }
    if (a.required !== b.required) {
      return a.required ? -1 : 1;
    }
    return 0;
  });
}

/**
 * Get professional terminology for industry
 */
function getProfessionalTerminology(
  industry: IndustryCategory,
  businessType: BusinessType
): ProfessionalTerminology {
  const terminologyDatabase: Partial<Record<IndustryCategory, ProfessionalTerminology>> = {
    healthcare: {
      clientTitle: 'Patient',
      projectTitle: 'Treatment',
      serviceTitle: 'Medical Services',
      invoiceTitle: 'Medical Bill',
      lineItemTitle: 'Service',
      additionalTerms: {
        provider: 'Healthcare Provider',
        session: 'Appointment',
        diagnosis: 'Diagnosis Code'
      }
    },
    legal: {
      clientTitle: 'Client',
      projectTitle: 'Case',
      serviceTitle: 'Legal Services',
      invoiceTitle: 'Legal Invoice',
      lineItemTitle: 'Legal Service',
      additionalTerms: {
        matter: 'Legal Matter',
        consultation: 'Legal Consultation',
        representation: 'Legal Representation'
      }
    },
    education: {
      clientTitle: 'Student',
      projectTitle: 'Course',
      serviceTitle: 'Educational Services',
      invoiceTitle: 'Tuition Invoice',
      lineItemTitle: 'Educational Service',
      additionalTerms: {
        session: 'Lesson',
        curriculum: 'Course Material',
        assessment: 'Evaluation'
      }
    },
    // Add more industries...
  };

  return terminologyDatabase[industry] || {
    clientTitle: 'Client',
    projectTitle: 'Project',
    serviceTitle: 'Services',
    invoiceTitle: 'Invoice',
    lineItemTitle: 'Service',
    additionalTerms: {}
  };
}

/**
 * Generate AI insights and recommendations
 */
function generateAIInsights(
  description: string,
  industry: IndustryCategory,
  businessType: BusinessType,
  serviceTypes: ServiceType[]
): AIInsight[] {
  const insights: AIInsight[] = [];

  // Industry-specific compliance insights
  if (industry === 'healthcare') {
    insights.push({
      type: 'compliance',
      message: 'Healthcare invoices may require HIPAA-compliant handling and patient privacy protection.',
      actionable: true,
      priority: 'high'
    });
  }

  if (industry === 'legal') {
    insights.push({
      type: 'compliance',
      message: 'Legal invoices should include detailed time tracking and matter descriptions for transparency.',
      actionable: true,
      priority: 'medium'
    });
  }

  // Service type insights
  if (serviceTypes.includes('hourly_services')) {
    insights.push({
      type: 'recommendation',
      message: 'Consider adding time tracking fields and detailed work descriptions for hourly billing.',
      actionable: true,
      priority: 'medium'
    });
  }

  if (serviceTypes.includes('subscription')) {
    insights.push({
      type: 'optimization',
      message: 'Subscription services benefit from automated recurring invoice generation.',
      actionable: true,
      priority: 'low'
    });
  }

  // Business type insights
  if (businessType === 'freelancer') {
    insights.push({
      type: 'recommendation',
      message: 'As a freelancer, consider adding personal branding elements and clear payment terms.',
      actionable: true,
      priority: 'low'
    });
  }

  return insights;
}

/**
 * Analyze keyword matches for confidence scoring
 */
function analyzeKeywordMatches(keywords: string[], industry: IndustryCategory): KeywordMatch[] {
  // This would analyze which keywords matched and their relevance
  return keywords.slice(0, 10).map(keyword => ({
    keyword,
    category: 'general',
    weight: 1.0,
    confidence: 0.8
  }));
}

/**
 * Calculate advanced confidence score
 */
function calculateAdvancedConfidence(
  industry: IndustryCategory,
  businessType: BusinessType,
  serviceTypes: ServiceType[],
  keywordMatches: KeywordMatch[],
  description: string
): number {
  let confidence = 0.5; // Base confidence

  // Industry confidence boost
  if (industry !== 'general') {
    confidence += 0.2;
  }

  // Keyword match boost
  const strongMatches = keywordMatches.filter(m => m.confidence > 0.8).length;
  confidence += Math.min(strongMatches * 0.05, 0.2);

  // Description length and detail boost
  if (description.length > 50) {
    confidence += 0.1;
  }

  // Service type specificity boost
  if (serviceTypes.length > 0 && !serviceTypes.includes('project_based')) {
    confidence += 0.1;
  }

  return Math.min(confidence, 0.95); // Cap at 95%
}

/**
 * Generate payment terms suggestions
 */
function generatePaymentTerms(industry: IndustryCategory, businessType: BusinessType): string[] {
  const industryTerms: Partial<Record<IndustryCategory, string[]>> = {
    healthcare: [
      'Payment due within 30 days of service',
      'Insurance claims processed within 60 days',
      'Patient portion due at time of service'
    ],
    legal: [
      'Payment due within 15 days of invoice date',
      'Retainer required before work begins',
      'Late fees of 1.5% per month may apply'
    ],
    construction: [
      '50% deposit required, balance due upon completion',
      'Net 30 payment terms',
      'Lien rights reserved'
    ],
    technology: [
      'Net 30 payment terms',
      'Monthly retainer due in advance',
      '2/10 Net 30 (2% discount if paid within 10 days)'
    ]
  };

  return industryTerms[industry] || [
    'Payment due within 30 days',
    'Late fees may apply',
    'Thank you for your business'
  ];
}

// ==================== TEMPLATE CUSTOMIZATION SYSTEM ====================

/**
 * Main template customization function - client-safe version
 */
function customizeTemplateForBusiness(
  businessAnalysis: BusinessAnalysis,
  baseTemplateId?: string
): GeneratedTemplate {
  try {
    // Generate customizations without database access
    const customizations = generateTemplateCustomizations(businessAnalysis, null);
    
    // Create field definitions
    const fieldDefinitions = createFieldDefinitions(businessAnalysis);
    
    // Generate sample content
    const sampleContent = generateSampleContent(businessAnalysis);
    
    // Create styling preferences
    const stylingPreferences = generateStylingPreferences(businessAnalysis);
    
    // Get industry specific elements
    const industrySpecificElements = getIndustrySpecificElements(businessAnalysis.industry);
    
    return {
      templateId: generateTemplateId(),
      templateName: generateTemplateName(businessAnalysis),
      description: generateTemplateDescription(businessAnalysis),
      industry: businessAnalysis.industry,
      businessType: businessAnalysis.businessType,
      baseTemplateId: baseTemplateId,
      customizations,
      fieldDefinitions,
      sampleContent,
      stylingPreferences,
      industrySpecificElements,
      aiConfidence: businessAnalysis.confidence,
      fallbackApplied: false
    };
  } catch (error) {
    console.error('Template customization error:', error);
    return createFallbackTemplate(businessAnalysis);
  }
}

// Note: Template selection should be done via API routes

// Removed - template scoring should be done server-side

/**
 * Generate template customizations
 */
function generateTemplateCustomizations(
  analysis: BusinessAnalysis,
  baseTemplate: any
): TemplateCustomization[] {
  const customizations: TemplateCustomization[] = [];

  // Add industry-specific sections
  if (analysis.industry === 'healthcare') {
    customizations.push({
      section: 'patient_info',
      modification: 'add',
      details: {
        title: 'Patient Information',
        fields: ['patient_id', 'insurance_provider', 'treatment_date']
      },
      reasoning: 'Healthcare requires patient-specific information for compliance'
    });
  }

  // Modify terminology based on industry
  customizations.push({
    section: 'terminology',
    modification: 'modify',
    details: {
      clientTitle: analysis.professionalTerminology.clientTitle,
      serviceTitle: analysis.professionalTerminology.serviceTitle,
      invoiceTitle: analysis.professionalTerminology.invoiceTitle
    },
    reasoning: 'Using industry-appropriate terminology for professional appearance'
  });

  // Add service-specific fields
  if (analysis.serviceTypes.includes('hourly_services')) {
    customizations.push({
      section: 'line_items',
      modification: 'modify',
      details: {
        addColumns: ['hours', 'hourly_rate'],
        removeColumns: ['quantity']
      },
      reasoning: 'Hourly services require time tracking instead of quantity'
    });
  }

  return customizations;
}

/**
 * Create field definitions from analysis
 */
function createFieldDefinitions(analysis: BusinessAnalysis): FieldDefinition[] {
  return analysis.customFieldSuggestions.map((suggestion, index) => ({
    id: `custom_field_${index}`,
    label: suggestion.fieldName,
    type: suggestion.fieldType,
    category: suggestion.category,
    required: suggestion.required,
    defaultValue: suggestion.defaultValue,
    placeholder: `Enter ${suggestion.fieldName.toLowerCase()}`,
    helpText: suggestion.aiReasoning,
    validation: suggestion.validation,
    options: suggestion.options?.map(opt => ({ value: opt, label: opt })),
    aiGenerated: true
  }));
}

/**
 * Generate sample content for template preview
 */
function generateSampleContent(analysis: BusinessAnalysis): SampleContent {
  const industrySpecificData = getIndustrySampleData(analysis.industry);
  
  return {
    businessInfo: {
      name: `Sample ${analysis.businessType === 'agency' ? 'Agency' : 'Business'}`,
      address: '123 Business St, City, State 12345',
      email: '<EMAIL>',
      phone: '(*************',
      ...industrySpecificData.businessInfo
    },
    clientInfo: {
      name: `Sample ${analysis.professionalTerminology.clientTitle}`,
      address: '456 Client Ave, City, State 67890',
      email: '<EMAIL>',
      ...industrySpecificData.clientInfo
    },
    lineItems: generateSampleLineItems(analysis),
    customFields: {},
    industrySpecificData: industrySpecificData.additional || {}
  };
}

/**
 * Generate sample line items based on service types
 */
function generateSampleLineItems(analysis: BusinessAnalysis): SampleLineItem[] {
  if (analysis.serviceTypes.includes('hourly_services')) {
    return [
      {
        description: 'Consulting Services',
        quantity: 10,
        rate: 150,
        amount: 1500,
        unit: 'hours'
      },
      {
        description: 'Project Planning',
        quantity: 5,
        rate: 150,
        amount: 750,
        unit: 'hours'
      }
    ];
  }

  if (analysis.serviceTypes.includes('subscription')) {
    return [
      {
        description: 'Monthly Subscription - Premium Plan',
        amount: 299,
        category: 'subscription'
      }
    ];
  }

  // Default project-based items
  return [
    {
      description: 'Main Project Deliverable',
      amount: 2500,
      category: 'project'
    },
    {
      description: 'Additional Services',
      amount: 500,
      category: 'additional'
    }
  ];
}

/**
 * Get industry-specific sample data
 */
function getIndustrySampleData(industry: IndustryCategory): any {
  const sampleData: Partial<Record<IndustryCategory, any>> = {
    healthcare: {
      businessInfo: {
        license: 'MD123456',
        npi: '**********'
      },
      clientInfo: {
        patientId: 'P-2024-001',
        insuranceProvider: 'Blue Cross Blue Shield'
      },
      additional: {
        treatmentDate: '2024-01-15',
        diagnosisCode: 'Z00.00'
      }
    },
    legal: {
      businessInfo: {
        barNumber: 'BAR123456',
        firmName: 'Sample Law Firm LLC'
      },
      clientInfo: {
        caseNumber: 'CASE-2024-001'
      },
      additional: {
        matterDescription: 'General Legal Consultation',
        courtJurisdiction: 'Superior Court'
      }
    },
    construction: {
      businessInfo: {
        licenseNumber: 'CONTR123456',
        insurancePolicy: 'INS789012'
      },
      clientInfo: {
        projectAddress: '789 Project Rd, City, State 12345'
      },
      additional: {
        permitNumber: 'PERMIT-2024-001',
        projectType: 'Residential Renovation'
      }
    }
  };

  return sampleData[industry] || { businessInfo: {}, clientInfo: {}, additional: {} };
}

/**
 * Generate styling preferences based on business analysis
 */
function generateStylingPreferences(analysis: BusinessAnalysis): StylingPreferences {
  const industryColorSchemes: Partial<Record<IndustryCategory, ColorScheme>> = {
    healthcare: {
      primary: '#2563eb', // Medical blue
      secondary: '#f8fafc',
      accent: '#10b981',
      text: '#1f2937',
      background: '#ffffff',
      borders: '#e5e7eb',
      success: '#10b981',
      warning: '#f59e0b'
    },
    legal: {
      primary: '#1e40af', // Professional dark blue
      secondary: '#f8fafc',
      accent: '#dc2626',
      text: '#111827',
      background: '#ffffff',
      borders: '#d1d5db',
      success: '#059669',
      warning: '#d97706'
    },
    creative: {
      primary: '#7c3aed', // Creative purple
      secondary: '#faf5ff',
      accent: '#ec4899',
      text: '#1f2937',
      background: '#ffffff',
      borders: '#e5e7eb',
      success: '#10b981',
      warning: '#f59e0b'
    },
    technology: {
      primary: '#0ea5e9', // Tech blue
      secondary: '#f0f9ff',
      accent: '#06b6d4',
      text: '#0f172a',
      background: '#ffffff',
      borders: '#cbd5e1',
      success: '#22c55e',
      warning: '#eab308'
    }
  };

  const defaultScheme = {
    primary: '#3b82f6',
    secondary: '#f8fafc',
    accent: '#6366f1',
    text: '#1f2937',
    background: '#ffffff',
    borders: '#e5e7eb',
    success: '#10b981',
    warning: '#f59e0b'
  };

  return {
    colorScheme: industryColorSchemes[analysis.industry] || defaultScheme,
    typography: {
      headingFont: 'Inter',
      bodyFont: 'Inter',
      fontSize: {
        base: 14,
        heading1: 24,
        heading2: 18,
        small: 12
      },
      fontWeights: {
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      }
    },
    branding: {
      logoPlacement: true,
      brandColors: true,
      customFonts: false,
      watermark: false,
      professionalLevel: analysis.businessType === 'agency' ? 'premium' : 'standard'
    },
    visualElements: {
      borderRadius: 4,
      shadowDepth: 'light',
      dividerStyle: 'solid',
      iconSet: 'minimal',
      tableStyle: analysis.businessType === 'freelancer' ? 'minimal' : 'bordered'
    },
    layoutPreferences: {
      headerStyle: analysis.businessType === 'agency' ? 'corporate' : 'minimal',
      itemsTableLayout: analysis.serviceTypes.includes('hourly_services') ? 'detailed' : 'simple',
      totalsPosition: 'right',
      notesPosition: 'bottom'
    }
  };
}

/**
 * Get industry-specific elements
 */
function getIndustrySpecificElements(industry: IndustryCategory): IndustrySpecificElements {
  const industryElements: Partial<Record<IndustryCategory, IndustrySpecificElements>> = {
    healthcare: {
      specialClauses: [
        'This invoice complies with HIPAA regulations',
        'Patient privacy is protected according to federal law',
        'Insurance billing processed according to CPT codes'
      ],
      requiredFields: ['patient_id', 'treatment_date', 'provider_npi'],
      commonAddOns: [
        { id: 'rush_processing', name: 'Rush Processing', description: 'Expedited insurance claim processing', defaultPrice: 25 },
        { id: 'copy_fee', name: 'Medical Records Copy', description: 'Patient medical record copies', defaultPrice: 15 }
      ],
      complianceNotes: [
        'HIPAA compliant billing required',
        'Patient consent for billing information sharing',
        'Insurance verification completed'
      ],
      industryTerms: {
        client: 'Patient',
        service: 'Treatment',
        session: 'Appointment'
      },
      paymentMethods: ['Insurance', 'Cash', 'Credit Card', 'HSA/FSA'],
      typicalDeliveryTerms: ['Upon completion of treatment', 'Monthly billing cycle']
    },
    legal: {
      specialClauses: [
        'Attorney-client privilege applies to all communications',
        'Time entries are recorded in 6-minute increments',
        'Costs and expenses are billed separately'
      ],
      requiredFields: ['case_number', 'matter_description', 'attorney_bar_number'],
      commonAddOns: [
        { id: 'rush_brief', name: 'Rush Brief Preparation', description: 'Expedited legal document preparation', defaultPrice: 500 },
        { id: 'court_filing', name: 'Court Filing Fees', description: 'Administrative court filing costs', defaultPrice: 200 }
      ],
      complianceNotes: [
        'Professional responsibility rules compliance',
        'Client trust account regulations',
        'State bar billing requirements'
      ],
      industryTerms: {
        project: 'Case',
        service: 'Legal Service',
        consultation: 'Legal Consultation'
      },
      paymentMethods: ['Retainer', 'Trust Account', 'Credit Card', 'Wire Transfer'],
      typicalDeliveryTerms: ['Net 15 days', 'Upon retainer replenishment']
    }
    // Add more industries as needed
  };

  return industryElements[industry] || {
    specialClauses: [],
    requiredFields: [],
    commonAddOns: [],
    complianceNotes: [],
    industryTerms: {},
    paymentMethods: ['Cash', 'Check', 'Credit Card', 'Bank Transfer'],
    typicalDeliveryTerms: ['Net 30 days', 'Upon completion']
  };
}

// ==================== AI PROMPT ENGINEERING SYSTEM ====================

/**
 * Generate optimized AI prompts for template enhancement
 */
function generateAIEnhancedContent(
  businessAnalysis: BusinessAnalysis,
  contentType: 'service_description' | 'payment_terms' | 'invoice_notes'
): string {
  const prompts = {
    service_description: generateServiceDescriptionPrompt(businessAnalysis),
    payment_terms: generatePaymentTermsPrompt(businessAnalysis),
    invoice_notes: generateInvoiceNotesPrompt(businessAnalysis)
  };

  try {
    // This would integrate with Claude API or similar
    // For now, return optimized template content
    return generateOptimizedContent(businessAnalysis, contentType);
  } catch (error) {
    console.error('AI content generation error:', error);
    return getFallbackContent(contentType);
  }
}

/**
 * Generate service description prompt for AI
 */
function generateServiceDescriptionPrompt(analysis: BusinessAnalysis): string {
  return `
Generate a professional service description for a ${analysis.businessType} in the ${analysis.industry} industry.

Business Context:
- Industry: ${analysis.industry}
- Business Type: ${analysis.businessType}
- Service Types: ${analysis.serviceTypes.join(', ')}
- Confidence Level: ${analysis.confidence}

Requirements:
- Professional tone appropriate for ${analysis.industry}
- Include key service benefits
- Maximum 150 words
- Use industry-appropriate terminology
- Highlight unique value proposition

Please provide a compelling service description that would work well on an invoice.
`;
}

/**
 * Generate payment terms prompt for AI
 */
function generatePaymentTermsPrompt(analysis: BusinessAnalysis): string {
  return `
Generate professional payment terms for a ${analysis.businessType} in the ${analysis.industry} industry.

Business Context:
- Industry: ${analysis.industry}
- Business Type: ${analysis.businessType}
- Billing Model: ${analysis.billingStructure.primaryModel}
- Payment Schedule: ${analysis.billingStructure.paymentSchedule}
- Typical Invoice Amount: $${analysis.billingStructure.typicalInvoiceAmount.typical}

Requirements:
- Industry-appropriate payment terms
- Clear and enforceable language
- Include late fee policy if appropriate
- Consider industry standards and regulations
- Professional but firm tone

Please provide complete payment terms suitable for invoices.
`;
}

/**
 * Generate invoice notes prompt for AI
 */
function generateInvoiceNotesPrompt(analysis: BusinessAnalysis): string {
  return `
Generate professional invoice notes for a ${analysis.businessType} in the ${analysis.industry} industry.

Business Context:
- Industry: ${analysis.industry}
- Business Type: ${analysis.businessType}
- Professional Terminology: ${JSON.stringify(analysis.professionalTerminology)}

Requirements:
- Professional and courteous tone
- Thank the client appropriately using industry terminology
- Include any industry-specific compliance notes if relevant
- Encourage future business relationship
- Maximum 100 words

Please provide professional invoice notes that enhance client relationships.
`;
}

/**
 * Generate optimized content based on analysis
 */
function generateOptimizedContent(
  analysis: BusinessAnalysis,
  contentType: 'service_description' | 'payment_terms' | 'invoice_notes'
): string {
  const contentTemplates: {
    service_description: Partial<Record<IndustryCategory, string>>;
    payment_terms: { default: string };
    invoice_notes: { default: string };
  } = {
    service_description: {
      technology: `Professional ${analysis.businessType} providing cutting-edge technology solutions. Specializing in ${analysis.serviceTypes.join(' and ')} with a focus on innovation, quality, and client satisfaction. Our expertise ensures efficient project delivery and ongoing support for all your technology needs.`,
      healthcare: `Licensed healthcare professional providing comprehensive ${analysis.serviceTypes.join(' and ')} services. Committed to patient care excellence with evidence-based practices and personalized treatment plans. HIPAA-compliant billing and insurance processing available.`,
      legal: `Experienced legal professional offering expert ${analysis.serviceTypes.join(' and ')} services. Dedicated to protecting client interests with thorough preparation, strategic thinking, and ethical practice. All communications protected by attorney-client privilege.`,
      creative: `Creative professional specializing in ${analysis.serviceTypes.join(' and ')} services. Bringing innovative design solutions and artistic excellence to every project. Collaborative approach ensures your vision becomes reality with professional execution.`
    },
    payment_terms: {
      default: analysis.billingStructure.suggestedTerms.join('\n')
    },
    invoice_notes: {
      default: `Thank you for choosing our ${analysis.professionalTerminology.serviceTitle.toLowerCase()}. We appreciate your business and look forward to serving you again.`
    }
  };

  if (contentType === 'service_description') {
    return contentTemplates.service_description[analysis.industry] || '';
  } else {
    return contentTemplates[contentType].default || '';
  }
}

/**
 * Get fallback content for when AI generation fails
 */
function getFallbackContent(contentType: string): string {
  const fallbacks: Record<string, string> = {
    service_description: 'Professional services provided with quality and expertise.',
    payment_terms: 'Payment due within 30 days of invoice date. Late fees may apply.',
    invoice_notes: 'Thank you for your business. We appreciate the opportunity to serve you.'
  };

  return fallbacks[contentType] || '';
}

// ==================== INTEGRATION WITH EXISTING TEMPLATES ====================

// Note: Template customization should be done via API routes

// Removed - template manipulation should be done server-side

// Removed - template manipulation should be done server-side

// Removed - template manipulation should be done server-side

// Removed - template manipulation should be done server-side

// Removed - template manipulation should be done server-side

// ==================== COMPREHENSIVE FALLBACK SYSTEM ====================

/**
 * Create fallback template when AI generation fails
 */
function createFallbackTemplate(analysis: BusinessAnalysis): GeneratedTemplate {
  return {
    templateId: generateTemplateId(),
    templateName: `${analysis.industry} Template (Fallback)`,
    description: `Basic template for ${analysis.industry} businesses`,
    industry: analysis.industry,
    businessType: analysis.businessType,
    customizations: [],
    fieldDefinitions: [],
    sampleContent: getBasicSampleContent(),
    stylingPreferences: getBasicStyling(),
    industrySpecificElements: {
      specialClauses: [],
      requiredFields: [],
      commonAddOns: [],
      complianceNotes: [],
      industryTerms: {},
      paymentMethods: ['Cash', 'Check', 'Credit Card'],
      typicalDeliveryTerms: ['Net 30 days']
    },
    aiConfidence: 0.3,
    fallbackApplied: true
  };
}

/**
 * Get basic sample content for fallback
 */
function getBasicSampleContent(): SampleContent {
  return {
    businessInfo: {
      name: 'Your Business Name',
      address: 'Your Business Address',
      email: '<EMAIL>',
      phone: 'Your Phone Number'
    },
    clientInfo: {
      name: 'Client Name',
      address: 'Client Address',
      email: '<EMAIL>'
    },
    lineItems: [
      {
        description: 'Service Description',
        amount: 1000
      }
    ],
    customFields: {},
    industrySpecificData: {}
  };
}

/**
 * Get basic styling for fallback
 */
function getBasicStyling(): StylingPreferences {
  return {
    colorScheme: {
      primary: '#3b82f6',
      secondary: '#f8fafc',
      accent: '#6366f1',
      text: '#1f2937',
      background: '#ffffff',
      borders: '#e5e7eb',
      success: '#10b981',
      warning: '#f59e0b'
    },
    typography: {
      headingFont: 'Inter',
      bodyFont: 'Inter',
      fontSize: {
        base: 14,
        heading1: 24,
        heading2: 18,
        small: 12
      },
      fontWeights: {
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      }
    },
    branding: {
      logoPlacement: true,
      brandColors: false,
      customFonts: false,
      watermark: false,
      professionalLevel: 'standard'
    },
    visualElements: {
      borderRadius: 4,
      shadowDepth: 'light',
      dividerStyle: 'solid',
      iconSet: 'minimal',
      tableStyle: 'minimal'
    },
    layoutPreferences: {
      headerStyle: 'minimal',
      itemsTableLayout: 'simple',
      totalsPosition: 'right',
      notesPosition: 'bottom'
    }
  };
}

// ==================== UTILITY FUNCTIONS ====================

/**
 * Generate unique template ID
 */
function generateTemplateId(): string {
  return `ai_template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generate template name based on analysis
 */
function generateTemplateName(analysis: BusinessAnalysis): string {
  const industryName = analysis.industry.replace('_', ' ').split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  
  const businessTypeName = analysis.businessType.replace('_', ' ').split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

  return `${industryName} ${businessTypeName} Template`;
}

/**
 * Generate template description based on analysis
 */
function generateTemplateDescription(analysis: BusinessAnalysis): string {
  return `AI-generated template optimized for ${analysis.industry} businesses offering ${analysis.serviceTypes.join(', ')} services. Includes industry-specific fields and professional terminology.`;
}

// Note: Template saving should be done via API routes

/**
 * Main convenience function that combines analysis and template generation
 */
function generateInvoiceTemplate(businessDescription: string): GeneratedTemplate {
  const analysis = analyzeBusinessDescription(businessDescription);
  return customizeTemplateForBusiness(analysis);
}

// Export main functions (client-safe only)
export {
  analyzeBusinessDescription,
  customizeTemplateForBusiness,
  generateAIEnhancedContent,
  generateInvoiceTemplate
};