// Enhanced Analytics Service for Template Invoice System
import { ObjectId } from 'mongodb';
import clientPromise from './mongodb';
import { COLLECTIONS } from './models';

// Enhanced event categories for comprehensive tracking
export type EventCategory = 
  | 'user'
  | 'invoice'
  | 'payment'
  | 'ai_template'
  | 'subscription'
  | 'system'
  | 'error'
  | 'performance'
  | 'onboarding'
  | 'email'
  | 'pdf';

export type EventAction = 
  | 'signup'
  | 'login'
  | 'logout'
  | 'create'
  | 'update'
  | 'delete'
  | 'view'
  | 'download'
  | 'send'
  | 'generate'
  | 'complete'
  | 'fail'
  | 'upgrade'
  | 'downgrade'
  | 'click'
  | 'error'
  | 'started'
  | 'abandoned'
  | 'retry'
  | 'timeout';

// Enhanced analytics event structure
interface EnhancedAnalyticsEvent {
  userId?: string | ObjectId;
  sessionId?: string;
  category: EventCategory;
  action: EventAction;
  label?: string;
  value?: number;
  metadata?: Record<string, any>;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  utm?: {
    source?: string;
    medium?: string;
    campaign?: string;
    term?: string;
    content?: string;
  };
  performance?: {
    duration?: number;
    status?: 'success' | 'error' | 'timeout';
    errorMessage?: string;
  };
}

// Critical metrics for MVP tracking
interface CriticalMetrics {
  userSignups: number;
  firstInvoiceCreated: number;
  aiTemplateSuccess: number;
  aiTemplateFailure: number;
  invoiceCompletionRate: number;
  pdfDownloads: number;
  emailsSent: number;
  paymentLinkClicks: number;
  averageResponseTime: number;
  errorRate: number;
}

// Performance metrics
interface PerformanceMetrics {
  endpoint: string;
  method: string;
  avgResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  successRate: number;
  errorCount: number;
  totalRequests: number;
}

// Track enhanced analytics event
export async function trackEvent(
  category: EventCategory,
  action: EventAction,
  data?: {
    userId?: string | ObjectId;
    label?: string;
    value?: number;
    metadata?: Record<string, any>;
    performance?: {
      duration?: number;
      status?: 'success' | 'error' | 'timeout';
      errorMessage?: string;
    };
  }
): Promise<void> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const eventsCollection = db.collection('analytics_events');

    const event: EnhancedAnalyticsEvent = {
      userId: data?.userId,
      sessionId: generateSessionId(),
      category,
      action,
      label: data?.label,
      value: data?.value,
      metadata: data?.metadata,
      performance: data?.performance,
      timestamp: new Date()
    };

    await eventsCollection.insertOne(event);

    // Update real-time metrics
    await updateRealTimeMetrics(category, action, data);

  } catch (error) {
    console.error('Error tracking analytics event:', error);
  }
}

// Track critical user journey events
export async function trackUserJourney(
  userId: string | ObjectId,
  event: 'signup' | 'first_invoice' | 'first_payment' | 'upgrade',
  metadata?: Record<string, any>
): Promise<void> {
  const categoryMap = {
    signup: 'user',
    first_invoice: 'invoice',
    first_payment: 'payment',
    upgrade: 'subscription'
  };

  await trackEvent(categoryMap[event] as EventCategory, 'complete', {
    userId,
    label: event,
    metadata: {
      ...metadata,
      journey_event: event,
      timestamp: new Date()
    }
  });

  // Update user journey stage
  await updateUserJourneyStage(userId, event);
}

// Track AI template generation performance
export async function trackAITemplateGeneration(
  userId: string | ObjectId,
  success: boolean,
  duration: number,
  industry?: string,
  error?: string
): Promise<void> {
  await trackEvent('ai_template', success ? 'complete' : 'fail', {
    userId,
    label: industry,
    value: duration,
    metadata: {
      industry,
      success,
      error,
      model_used: 'gpt-4',
      prompt_tokens: 0, // Would be filled with actual token count
      completion_tokens: 0
    },
    performance: {
      duration,
      status: success ? 'success' : 'error',
      errorMessage: error
    }
  });
}

// Track invoice lifecycle events
export async function trackInvoiceLifecycle(
  userId: string | ObjectId,
  invoiceId: string | ObjectId,
  stage: 'created' | 'updated' | 'sent' | 'viewed' | 'paid' | 'abandoned',
  metadata?: Record<string, any>
): Promise<void> {
  const actionMap = {
    created: 'create',
    updated: 'update',
    sent: 'send',
    viewed: 'view',
    paid: 'complete',
    abandoned: 'abandoned'
  };

  await trackEvent('invoice', actionMap[stage] as EventAction, {
    userId,
    label: stage,
    metadata: {
      invoiceId: invoiceId.toString(),
      stage,
      ...metadata,
      is_first_invoice: metadata?.is_first_invoice || false
    }
  });
}

// Track PDF operations with performance metrics
export async function trackPDFOperation(
  userId: string | ObjectId,
  operation: 'generate' | 'download' | 'email',
  success: boolean,
  duration?: number,
  fileSize?: number,
  error?: string
): Promise<void> {
  await trackEvent('pdf', success ? 'complete' : 'fail', {
    userId,
    label: operation,
    value: fileSize,
    metadata: {
      operation,
      success,
      fileSize,
      fileSizeKB: fileSize ? Math.round(fileSize / 1024) : 0
    },
    performance: {
      duration,
      status: success ? 'success' : 'error',
      errorMessage: error
    }
  });
}

// Track email operations
export async function trackEmailOperation(
  userId: string | ObjectId,
  operation: 'send' | 'delivered' | 'opened' | 'clicked' | 'bounced',
  invoiceId?: string,
  metadata?: Record<string, any>
): Promise<void> {
  const actionMap = {
    send: 'send',
    delivered: 'complete',
    opened: 'view',
    clicked: 'click',
    bounced: 'fail'
  };

  await trackEvent('email', actionMap[operation] as EventAction, {
    userId,
    label: operation,
    metadata: {
      invoiceId,
      operation,
      ...metadata
    }
  });
}

// Track payment link performance
export async function trackPaymentLink(
  userId: string | ObjectId,
  action: 'created' | 'clicked' | 'completed' | 'failed',
  amount?: number,
  paymentLinkId?: string,
  processingTime?: number
): Promise<void> {
  const actionMap = {
    created: 'create',
    clicked: 'click',
    completed: 'complete',
    failed: 'fail'
  };

  await trackEvent('payment', actionMap[action] as EventAction, {
    userId,
    label: action,
    value: amount,
    metadata: {
      paymentLinkId,
      action,
      amount,
      currency: 'USD'
    },
    performance: {
      duration: processingTime,
      status: action === 'completed' ? 'success' : action === 'failed' ? 'error' : undefined
    }
  });
}

// Track API performance
export async function trackAPIPerformance(
  endpoint: string,
  method: string,
  duration: number,
  status: 'success' | 'error',
  statusCode?: number,
  error?: string,
  userId?: string | ObjectId
): Promise<void> {
  await trackEvent('performance', status === 'success' ? 'complete' : 'fail', {
    userId,
    label: `${method} ${endpoint}`,
    value: duration,
    metadata: {
      endpoint,
      method,
      statusCode,
      error
    },
    performance: {
      duration,
      status,
      errorMessage: error
    }
  });

  // Update endpoint performance metrics
  await updateEndpointMetrics(endpoint, method, duration, status);
}

// Get critical metrics for monitoring
export async function getCriticalMetrics(
  dateRange?: { start: Date; end: Date }
): Promise<CriticalMetrics> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const eventsCollection = db.collection('analytics_events');
    
    const matchStage: any = {};
    if (dateRange) {
      matchStage.timestamp = {
        $gte: dateRange.start,
        $lte: dateRange.end
      };
    }

    const metrics = await eventsCollection.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          userSignups: {
            $sum: { $cond: [{ $and: [{ $eq: ['$category', 'user'] }, { $eq: ['$action', 'signup'] }] }, 1, 0] }
          },
          firstInvoiceCreated: {
            $sum: { $cond: [{ $and: [{ $eq: ['$category', 'invoice'] }, { $eq: ['$action', 'create'] }, { $eq: ['$metadata.is_first_invoice', true] }] }, 1, 0] }
          },
          aiTemplateSuccess: {
            $sum: { $cond: [{ $and: [{ $eq: ['$category', 'ai_template'] }, { $eq: ['$action', 'complete'] }] }, 1, 0] }
          },
          aiTemplateFailure: {
            $sum: { $cond: [{ $and: [{ $eq: ['$category', 'ai_template'] }, { $eq: ['$action', 'fail'] }] }, 1, 0] }
          },
          invoiceStarted: {
            $sum: { $cond: [{ $and: [{ $eq: ['$category', 'invoice'] }, { $eq: ['$action', 'started'] }] }, 1, 0] }
          },
          invoiceCompleted: {
            $sum: { $cond: [{ $and: [{ $eq: ['$category', 'invoice'] }, { $eq: ['$action', 'complete'] }] }, 1, 0] }
          },
          pdfDownloads: {
            $sum: { $cond: [{ $and: [{ $eq: ['$category', 'pdf'] }, { $eq: ['$action', 'download'] }] }, 1, 0] }
          },
          emailsSent: {
            $sum: { $cond: [{ $and: [{ $eq: ['$category', 'email'] }, { $eq: ['$action', 'send'] }] }, 1, 0] }
          },
          paymentLinkClicks: {
            $sum: { $cond: [{ $and: [{ $eq: ['$category', 'payment'] }, { $eq: ['$action', 'click'] }] }, 1, 0] }
          },
          totalDuration: {
            $sum: { $ifNull: ['$performance.duration', 0] }
          },
          totalAPICalls: {
            $sum: { $cond: [{ $eq: ['$category', 'performance'] }, 1, 0] }
          },
          errorCount: {
            $sum: { $cond: [{ $eq: ['$action', 'error'] }, 1, 0] }
          },
          totalEvents: { $sum: 1 }
        }
      }
    ]).toArray();

    const result = metrics[0] || {};
    
    return {
      userSignups: result.userSignups || 0,
      firstInvoiceCreated: result.firstInvoiceCreated || 0,
      aiTemplateSuccess: result.aiTemplateSuccess || 0,
      aiTemplateFailure: result.aiTemplateFailure || 0,
      invoiceCompletionRate: result.invoiceStarted > 0 
        ? (result.invoiceCompleted / result.invoiceStarted) * 100 
        : 0,
      pdfDownloads: result.pdfDownloads || 0,
      emailsSent: result.emailsSent || 0,
      paymentLinkClicks: result.paymentLinkClicks || 0,
      averageResponseTime: result.totalAPICalls > 0 
        ? result.totalDuration / result.totalAPICalls 
        : 0,
      errorRate: result.totalEvents > 0 
        ? (result.errorCount / result.totalEvents) * 100 
        : 0
    };

  } catch (error) {
    console.error('Error getting critical metrics:', error);
    return {
      userSignups: 0,
      firstInvoiceCreated: 0,
      aiTemplateSuccess: 0,
      aiTemplateFailure: 0,
      invoiceCompletionRate: 0,
      pdfDownloads: 0,
      emailsSent: 0,
      paymentLinkClicks: 0,
      averageResponseTime: 0,
      errorRate: 0
    };
  }
}

// Get endpoint performance metrics
export async function getEndpointMetrics(
  dateRange?: { start: Date; end: Date }
): Promise<PerformanceMetrics[]> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const metricsCollection = db.collection('endpoint_metrics');
    
    const matchStage: any = {};
    if (dateRange) {
      matchStage.lastUpdated = {
        $gte: dateRange.start,
        $lte: dateRange.end
      };
    }

    const results = await metricsCollection
      .find(matchStage)
      .sort({ avgResponseTime: -1 })
      .limit(20)
      .toArray();
    
    return results as unknown as PerformanceMetrics[];

  } catch (error) {
    console.error('Error getting endpoint metrics:', error);
    return [];
  }
}

// Monitor system health
export async function getSystemHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'down';
  services: {
    database: { status: string; responseTime: number };
    ai: { status: string; successRate: number };
    email: { status: string; deliveryRate: number };
    payment: { status: string; successRate: number };
  };
  performance: {
    avgResponseTime: number;
    errorRate: number;
    activeUsers: number;
  };
}> {
  try {
    const client = await clientPromise;
    const db = client.db();
    
    // Check database health
    const dbStart = Date.now();
    await db.admin().ping();
    const dbResponseTime = Date.now() - dbStart;
    
    // Get recent metrics (last 15 minutes)
    const recentMetrics = await getCriticalMetrics({
      start: new Date(Date.now() - 15 * 60 * 1000),
      end: new Date()
    });
    
    // Calculate AI success rate
    const aiTotal = recentMetrics.aiTemplateSuccess + recentMetrics.aiTemplateFailure;
    const aiSuccessRate = aiTotal > 0
      ? (recentMetrics.aiTemplateSuccess / aiTotal) * 100
      : 100;
    
    // Get active users (last 5 minutes)
    const activeUsers = await db.collection('analytics_events').distinct('userId', {
      timestamp: { $gte: new Date(Date.now() - 5 * 60 * 1000) }
    });
    
    // Determine overall status
    let status: 'healthy' | 'degraded' | 'down' = 'healthy';
    if (dbResponseTime > 1000 || recentMetrics.errorRate > 10 || aiSuccessRate < 80) {
      status = 'degraded';
    }
    if (dbResponseTime > 5000 || recentMetrics.errorRate > 50 || aiSuccessRate < 50) {
      status = 'down';
    }
    
    return {
      status,
      services: {
        database: {
          status: dbResponseTime < 1000 ? 'healthy' : dbResponseTime < 5000 ? 'degraded' : 'down',
          responseTime: dbResponseTime
        },
        ai: {
          status: aiSuccessRate >= 90 ? 'healthy' : aiSuccessRate >= 70 ? 'degraded' : 'down',
          successRate: aiSuccessRate
        },
        email: {
          status: 'healthy', // Would calculate based on email metrics
          deliveryRate: 95
        },
        payment: {
          status: 'healthy', // Would calculate based on payment metrics
          successRate: 98
        }
      },
      performance: {
        avgResponseTime: recentMetrics.averageResponseTime,
        errorRate: recentMetrics.errorRate,
        activeUsers: activeUsers.length
      }
    };

  } catch (error) {
    console.error('Error checking system health:', error);
    return {
      status: 'down',
      services: {
        database: { status: 'error', responseTime: 0 },
        ai: { status: 'unknown', successRate: 0 },
        email: { status: 'unknown', deliveryRate: 0 },
        payment: { status: 'unknown', successRate: 0 }
      },
      performance: {
        avgResponseTime: 0,
        errorRate: 100,
        activeUsers: 0
      }
    };
  }
}

// Real-time dashboard metrics
export async function getRealTimeDashboard(): Promise<{
  currentActiveUsers: number;
  todayStats: {
    signups: number;
    invoicesCreated: number;
    revenue: number;
    aiGenerations: number;
  };
  last24Hours: {
    hourlyActivity: Array<{ hour: number; events: number }>;
    topEndpoints: Array<{ endpoint: string; calls: number; avgTime: number }>;
  };
  alerts: Array<{ type: string; message: string; severity: 'low' | 'medium' | 'high' }>;
}> {
  try {
    const client = await clientPromise;
    const db = client.db();
    
    const now = new Date();
    const todayStart = new Date(now.setHours(0, 0, 0, 0));
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    // Get current active users (last 5 minutes)
    const activeUsers = await db.collection('analytics_events').distinct('userId', {
      timestamp: { $gte: new Date(Date.now() - 5 * 60 * 1000) }
    });
    
    // Get today's stats
    const todayStats = await getCriticalMetrics({
      start: todayStart,
      end: new Date()
    });
    
    // Get hourly activity for last 24 hours
    const hourlyActivity = await db.collection('analytics_events').aggregate([
      {
        $match: {
          timestamp: { $gte: yesterday }
        }
      },
      {
        $group: {
          _id: { $hour: '$timestamp' },
          events: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]).toArray();
    
    // Check for alerts
    const alerts = [];
    const health = await getSystemHealth();
    
    if (health.status === 'degraded') {
      alerts.push({
        type: 'performance',
        message: 'System performance is degraded',
        severity: 'medium' as const
      });
    }
    
    if (health.status === 'down') {
      alerts.push({
        type: 'system',
        message: 'System is experiencing issues',
        severity: 'high' as const
      });
    }
    
    if (todayStats.errorRate > 5) {
      alerts.push({
        type: 'errors',
        message: `High error rate detected: ${todayStats.errorRate.toFixed(1)}%`,
        severity: (todayStats.errorRate > 10 ? 'high' : 'medium') as 'high' | 'medium'
      });
    }
    
    return {
      currentActiveUsers: activeUsers.length,
      todayStats: {
        signups: todayStats.userSignups,
        invoicesCreated: todayStats.firstInvoiceCreated,
        revenue: 0, // Would calculate from payment events
        aiGenerations: todayStats.aiTemplateSuccess + todayStats.aiTemplateFailure
      },
      last24Hours: {
        hourlyActivity: hourlyActivity.map(h => ({
          hour: h._id,
          events: h.events
        })),
        topEndpoints: [] // Would get from endpoint metrics
      },
      alerts
    };

  } catch (error) {
    console.error('Error getting real-time dashboard:', error);
    return {
      currentActiveUsers: 0,
      todayStats: {
        signups: 0,
        invoicesCreated: 0,
        revenue: 0,
        aiGenerations: 0
      },
      last24Hours: {
        hourlyActivity: [],
        topEndpoints: []
      },
      alerts: [{
        type: 'system',
        message: 'Unable to fetch analytics data',
        severity: 'high'
      }]
    };
  }
}

// Helper functions
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}

async function updateUserJourneyStage(
  userId: string | ObjectId,
  stage: string
): Promise<void> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const journeyCollection = db.collection('user_journeys');
    
    const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    
    await journeyCollection.updateOne(
      { userId: userObjectId },
      {
        $set: {
          [`stages.${stage}`]: new Date(),
          currentStage: stage,
          updatedAt: new Date()
        },
        $setOnInsert: {
          userId: userObjectId,
          createdAt: new Date()
        }
      },
      { upsert: true }
    );
  } catch (error) {
    console.error('Error updating user journey:', error);
  }
}

async function updateRealTimeMetrics(
  category: EventCategory,
  action: EventAction,
  data?: any
): Promise<void> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const metricsCollection = db.collection('realtime_metrics');
    
    const now = new Date();
    const minuteKey = `${now.getHours()}:${Math.floor(now.getMinutes() / 5) * 5}`;
    
    await metricsCollection.updateOne(
      {
        date: now.toISOString().split('T')[0],
        minute: minuteKey
      },
      {
        $inc: {
          [`events.${category}.${action}`]: 1,
          totalEvents: 1
        },
        $set: { lastUpdated: now }
      },
      { upsert: true }
    );
  } catch (error) {
    console.error('Error updating real-time metrics:', error);
  }
}

async function updateEndpointMetrics(
  endpoint: string,
  method: string,
  duration: number,
  status: 'success' | 'error'
): Promise<void> {
  try {
    const client = await clientPromise;
    const db = client.db();
    const metricsCollection = db.collection('endpoint_metrics');
    
    const key = `${method} ${endpoint}`;
    
    await metricsCollection.updateOne(
      { endpoint: key },
      {
        $inc: {
          totalRequests: 1,
          errorCount: status === 'error' ? 1 : 0,
          totalDuration: duration
        },
        $push: {
          recentDurations: {
            $each: [duration],
            $slice: -100 // Keep last 100 durations for percentile calculations
          }
        },
        $set: {
          lastUpdated: new Date()
        }
      },
      { upsert: true }
    );
  } catch (error) {
    console.error('Error updating endpoint metrics:', error);
  }
}