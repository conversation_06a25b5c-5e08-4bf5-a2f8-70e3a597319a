'use client';

import { useEffect } from 'react';

export default function ProductionWebVitals() {
  useEffect(() => {
    // Only run in production to avoid development overlay conflicts
    if (process.env.NODE_ENV !== 'production') {
      return;
    }
    
    // Only run on client side
    if (typeof window === 'undefined') return;
    
    // Dynamic import with error handling
    const loadWebVitals = async () => {
      try {
        const { onCLS, onLCP, onINP, onFCP, onTTFB } = await import('web-vitals');
        
        const sendToAnalytics = (metric: any) => {
          // Send to Google Analytics
          if ((window as any).gtag) {
            (window as any).gtag('event', metric.name, {
              event_category: 'Web Vitals',
              event_label: metric.id,
              value: Math.round(metric.value),
              non_interaction: true,
            });
          }
          
          // Send to your analytics endpoint
          fetch('/api/analytics/web-vitals', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(metric),
          }).catch(() => {
            // Silently fail if analytics endpoint is not available
          });
        };
        
        // Measure Core Web Vitals
        onCLS(sendToAnalytics);
        onLCP(sendToAnalytics);
        onINP(sendToAnalytics); // Replaces FID
        onFCP(sendToAnalytics);
        onTTFB(sendToAnalytics);
        
      } catch (error) {
        // Silently fail if web-vitals cannot be loaded
        console.warn('Web Vitals not available:', error);
      }
    };
    
    loadWebVitals();
  }, []);

  return null;
}