import { NextRequest } from 'next/server';
import { generateInvoicePDF } from '@/lib/pdf-generator';
import { validateTemplateData } from '@/lib/template-renderer';

export async function POST(request: NextRequest) {
  try {
    const { templateId, invoiceData, options } = await request.json();
    
    // Validate required parameters
    if (!templateId) {
      return new Response('Template ID is required', { status: 400 });
    }
    
    if (!invoiceData) {
      return new Response('Invoice data is required', { status: 400 });
    }
    
    // Validate invoice data
    const validation = validateTemplateData(invoiceData);
    if (!validation.isValid) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid invoice data', 
          errors: validation.errors 
        }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Generate PDF
    const pdfBuffer = await generateInvoicePDF(templateId, invoiceData, options);
    
    // Prepare filename
    const invoiceNumber = invoiceData.invoiceNumber || 'invoice';
    const filename = `invoice-${invoiceNumber}.pdf`;
    
    // Return PDF as response
    return new Response(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
  } catch (error) {
    console.error('PDF generation API error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(
      JSON.stringify({ 
        error: 'PDF generation failed', 
        message: errorMessage 
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// GET endpoint for template preview PDFs
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('template');
    
    if (!templateId) {
      return new Response('Template ID is required', { status: 400 });
    }
    
    // Import template definitions to get sample data
    const { getTemplateById } = await import('@/lib/templates/template-definitions');
    const template = getTemplateById(templateId);
    
    if (!template) {
      return new Response('Template not found', { status: 404 });
    }
    
    // Generate preview PDF with sample data
    const pdfBuffer = await generateInvoicePDF(templateId, template.sampleData);
    
    const filename = `preview-${templateId}.pdf`;
    
    return new Response(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString(),
        'Cache-Control': 'public, max-age=3600' // Cache preview for 1 hour
      }
    });
    
  } catch (error) {
    console.error('Preview PDF generation error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Preview generation failed', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}