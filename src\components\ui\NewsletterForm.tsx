import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { Mail, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from './Button';
import { Input } from './Input';

export interface NewsletterFormProps {
  onSubmit?: (email: string) => Promise<void>;
  title?: string;
  description?: string;
  placeholder?: string;
  buttonText?: string;
  successMessage?: string;
  className?: string;
  variant?: 'default' | 'inline' | 'stacked';
}

const NewsletterForm: React.FC<NewsletterFormProps> = ({
  onSubmit,
  title = 'Subscribe to our newsletter',
  description = 'Get the latest updates and news delivered to your inbox.',
  placeholder = 'Enter your email',
  buttonText = 'Subscribe',
  successMessage = 'Thanks for subscribing!',
  className,
  variant = 'default',
}) => {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [error, setError] = useState('');

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!email) {
      setError('Email is required');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setStatus('loading');

    try {
      if (onSubmit) {
        await onSubmit(email);
      } else {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500));
      }
      setStatus('success');
      setEmail('');
      
      // Reset to idle after 5 seconds
      setTimeout(() => {
        setStatus('idle');
      }, 5000);
    } catch (err) {
      setStatus('error');
      setError('Something went wrong. Please try again.');
    }
  };

  const renderForm = () => {
    if (status === 'success') {
      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center justify-center py-4 text-green-600"
        >
          <CheckCircle className="w-5 h-5 mr-2" />
          <span className="font-medium">{successMessage}</span>
        </motion.div>
      );
    }

    const formContent = (
      <>
        <div className={cn('flex-1', variant === 'stacked' && 'w-full')}>
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder={placeholder}
            error={error}
            disabled={status === 'loading'}
            leftIcon={<Mail className="w-4 h-4" />}
            className={cn(
              variant === 'inline' && 'rounded-r-none',
              variant === 'stacked' && 'mb-4'
            )}
          />
        </div>
        <Button
          type="submit"
          isLoading={status === 'loading'}
          disabled={status === 'loading'}
          className={cn(
            variant === 'inline' && 'rounded-l-none',
            variant === 'stacked' && 'w-full'
          )}
        >
          {buttonText}
        </Button>
      </>
    );

    return (
      <form
        onSubmit={handleSubmit}
        className={cn(
          'flex',
          variant === 'inline' && 'flex-row',
          variant === 'stacked' && 'flex-col',
          variant === 'default' && 'flex-col sm:flex-row gap-4'
        )}
      >
        {formContent}
      </form>
    );
  };

  return (
    <div className={cn('w-full', className)}>
      {(title || description) && (
        <div className="mb-6 text-center">
          {title && (
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{title}</h3>
          )}
          {description && (
            <p className="text-gray-600">{description}</p>
          )}
        </div>
      )}
      
      <div className="max-w-md mx-auto">
        {renderForm()}
      </div>

      {error && status === 'error' && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 flex items-center justify-center text-red-600"
        >
          <AlertCircle className="w-4 h-4 mr-2" />
          <span className="text-sm">{error}</span>
        </motion.div>
      )}
    </div>
  );
};

export { NewsletterForm };