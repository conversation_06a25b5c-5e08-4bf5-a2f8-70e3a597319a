'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Star, 
  Shield, 
  Award, 
  CheckCircle,
  TrendingUp,
  Clock,
  Users,
  Building2
} from 'lucide-react'
import Image from 'next/image'

export default function SocialProof() {
  const [activeTestimonialIndex, setActiveTestimonialIndex] = useState(0)
  
  // Animated counter hook
  const useCountUp = (target: number, duration: number = 2000) => {
    const [count, setCount] = useState(0)
    
    useEffect(() => {
      const increment = target / (duration / 16)
      let current = 0
      
      const timer = setInterval(() => {
        current += increment
        if (current >= target) {
          setCount(target)
          clearInterval(timer)
        } else {
          setCount(Math.floor(current))
        }
      }, 16)
      
      return () => clearInterval(timer)
    }, [target, duration])
    
    return count
  }

  const invoicesProcessed = useCountUp(2400000, 3000)
  const activeUsers = useCountUp(12847, 3000)
  const fasterPayment = useCountUp(98, 2000)
  const rating = useCountUp(4.9, 1500)

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Freelance Photographer",
      company: "Park Photography",
      content: "Before, I was waiting 45+ days for payments. Now I get paid in under 2 weeks. The automated reminders are a game-changer!",
      image: "/testimonials/david.jpg",
      beforeDays: 45,
      afterDays: 12,
      amount: "$4,200"
    },
    {
      name: "Lisa Martinez",
      role: "Web Developer",
      company: "DevCraft Studios",
      content: "My clients actually compliment my invoices now! The professional templates make me look more established. Payments come 3x faster.",
      image: "/testimonials/lisa.jpg",
      beforeDays: 38,
      afterDays: 9,
      amount: "$7,500"
    },
    {
      name: "James Wilson",
      role: "Marketing Consultant",
      company: "Wilson Digital",
      content: "I save 4+ hours every week on invoicing. The AI writes perfect descriptions and the one-click payments mean instant deposits.",
      image: "/testimonials/james.jpg",
      beforeDays: 52,
      afterDays: 14,
      amount: "$5,800"
    }
  ]

  const companyLogos = [
    { name: "Upwork", url: "/logos/upwork.svg" },
    { name: "Fiverr", url: "/logos/fiverr.svg" },
    { name: "Freelancer", url: "/logos/freelancer.svg" },
    { name: "99designs", url: "/logos/99designs.svg" },
    { name: "Toptal", url: "/logos/toptal.svg" },
    { name: "PeoplePerHour", url: "/logos/peopleperhour.svg" }
  ]

  const mediaLogos = [
    { name: "TechCrunch", url: "/logos/techcrunch.svg" },
    { name: "Forbes", url: "/logos/forbes.svg" },
    { name: "Entrepreneur", url: "/logos/entrepreneur.svg" },
    { name: "Inc", url: "/logos/inc.svg" }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTestimonialIndex((prev) => (prev + 1) % testimonials.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [testimonials.length])

  return (
    <section className="py-20 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-grid-gray-100 bg-grid-16 opacity-5" />
      
      <div className="container mx-auto px-4 relative">
        {/* Statistics Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
        >
          <div className="text-center">
            <motion.div
              initial={{ scale: 0.5 }}
              whileInView={{ scale: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1, type: "spring" }}
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-2"
            >
              ${(invoicesProcessed / 1000000).toFixed(1)}M+
            </motion.div>
            <p className="text-gray-600">invoices processed this month</p>
          </div>
          
          <div className="text-center">
            <motion.div
              initial={{ scale: 0.5 }}
              whileInView={{ scale: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, type: "spring" }}
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-2"
            >
              {activeUsers.toLocaleString()}
            </motion.div>
            <p className="text-gray-600">active users</p>
          </div>
          
          <div className="text-center">
            <motion.div
              initial={{ scale: 0.5 }}
              whileInView={{ scale: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, type: "spring" }}
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-2"
            >
              {fasterPayment}%
            </motion.div>
            <p className="text-gray-600">get paid faster</p>
          </div>
          
          <div className="text-center">
            <motion.div
              initial={{ scale: 0.5 }}
              whileInView={{ scale: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4, type: "spring" }}
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-1"
            >
              {rating.toFixed(1)}
              <Star className="w-6 h-6 text-yellow-400 fill-current" />
            </motion.div>
            <p className="text-gray-600">star rating</p>
          </div>
        </motion.div>

        {/* Testimonial Carousel */}
        <div className="max-w-4xl mx-auto mb-16">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="bg-white rounded-2xl shadow-xl p-8 md:p-12 relative"
          >
            {/* Quote icon */}
            <div className="absolute top-6 left-6 text-blue-100">
              <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
              </svg>
            </div>

            <div className="relative">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0 }}
                  animate={{ 
                    opacity: index === activeTestimonialIndex ? 1 : 0,
                    display: index === activeTestimonialIndex ? 'block' : 'none'
                  }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="flex flex-col md:flex-row gap-8 items-center">
                    {/* Before/After visualization */}
                    <div className="flex-shrink-0">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="text-sm font-semibold text-gray-700 mb-3">Payment Timeline</h4>
                        <div className="space-y-3">
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Before</p>
                            <div className="flex items-center gap-2">
                              <div className="h-2 bg-red-200 rounded-full" style={{ width: `${testimonial.beforeDays * 2}px` }} />
                              <span className="text-sm font-medium text-red-600">{testimonial.beforeDays} days</span>
                            </div>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 mb-1">After</p>
                            <div className="flex items-center gap-2">
                              <div className="h-2 bg-green-400 rounded-full" style={{ width: `${testimonial.afterDays * 2}px` }} />
                              <span className="text-sm font-medium text-green-600">{testimonial.afterDays} days</span>
                            </div>
                          </div>
                        </div>
                        <div className="mt-3 text-center">
                          <p className="text-2xl font-bold text-green-600">{testimonial.amount}</p>
                          <p className="text-xs text-gray-500">paid in full</p>
                        </div>
                      </div>
                    </div>

                    {/* Testimonial content */}
                    <div className="flex-1">
                      <p className="text-lg md:text-xl text-gray-700 mb-6 leading-relaxed">
                        "{testimonial.content}"
                      </p>
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full" />
                        <div>
                          <p className="font-semibold text-gray-900">{testimonial.name}</p>
                          <p className="text-sm text-gray-600">{testimonial.role} • {testimonial.company}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Carousel indicators */}
            <div className="flex gap-2 justify-center mt-6">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveTestimonialIndex(index)}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === activeTestimonialIndex
                      ? 'w-8 bg-blue-600'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>
          </motion.div>
        </div>

        {/* Logo Wall */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.2 }}
          className="text-center mb-12"
        >
          <p className="text-gray-600 mb-8 text-lg">Trusted by professionals from:</p>
          <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12 opacity-60">
            {companyLogos.map((logo) => (
              <motion.div
                key={logo.name}
                whileHover={{ scale: 1.05 }}
                className="grayscale hover:grayscale-0 transition-all"
              >
                <div className="h-8 w-24 bg-gray-300 rounded flex items-center justify-center text-xs text-gray-600">
                  {logo.name}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Credibility Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
        >
          {/* Security badges */}
          <div className="text-center">
            <Shield className="w-12 h-12 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Bank-Level Security</h3>
            <p className="text-sm text-gray-600">256-bit SSL encryption protects all your data</p>
          </div>

          {/* Awards */}
          <div className="text-center">
            <Award className="w-12 h-12 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Award-Winning Support</h3>
            <p className="text-sm text-gray-600">24/7 support with 2-minute response time</p>
          </div>

          {/* Guarantee */}
          <div className="text-center">
            <CheckCircle className="w-12 h-12 text-purple-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">60-Day Guarantee</h3>
            <p className="text-sm text-gray-600">Not satisfied? Get a full refund, no questions</p>
          </div>
        </motion.div>

        {/* Featured in */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4 }}
          className="text-center mt-12"
        >
          <p className="text-gray-600 mb-6 text-sm">Featured in:</p>
          <div className="flex justify-center items-center gap-8 opacity-50">
            {mediaLogos.map((logo) => (
              <div key={logo.name} className="h-6 w-20 bg-gray-300 rounded flex items-center justify-center text-xs text-gray-600">
                {logo.name}
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}