import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ObjectId } from 'mongodb';
import { getInvoiceById, markInvoiceAsSent } from '@/lib/invoice-service';
import { sendInvoiceEmail, validateEmailConfig } from '@/lib/email-service';
import { createPaymentLink } from '@/lib/payment-links';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

interface SendInvoiceRequest {
  to?: string;
  cc?: string[];
  bcc?: string[];
  customMessage?: string;
  includePDF?: boolean;
  includePaymentLink?: boolean;
  template?: 'invoice_send' | 'payment_reminder';
}

/**
 * API Route: Send invoice via email
 * POST /api/invoices/[id]/send
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: invoiceId } = await params;

    // Validate invoice ID
    if (!ObjectId.isValid(invoiceId)) {
      return NextResponse.json({ error: 'Invalid invoice ID' }, { status: 400 });
    }

    // Get request body
    const body: SendInvoiceRequest = await request.json();
    const {
      to,
      cc,
      bcc,
      customMessage,
      includePDF = true,
      includePaymentLink = true,
      template = 'invoice_send'
    } = body;

    // Get invoice
    const invoice = await getInvoiceById(invoiceId, session.user.id);
    
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Validate email configuration
    const emailValidation = await validateEmailConfig();
    if (!emailValidation.isValid) {
      return NextResponse.json({ 
        error: 'Email service not configured', 
        details: emailValidation.error 
      }, { status: 500 });
    }

    // Generate payment link if requested
    let paymentLink: string | undefined;
    if (includePaymentLink && invoice.status !== 'paid') {
      try {
        const paymentResult = await createPaymentLink(invoice);
        if (paymentResult.success && paymentResult.url) {
          paymentLink = paymentResult.url;
        }
      } catch (paymentError) {
        console.warn('Failed to create payment link:', paymentError);
        // Continue without payment link
      }
    }

    // Send email
    const emailResult = await sendInvoiceEmail(invoice, {
      to: to || invoice.clientInfo.email,
      cc,
      bcc,
      template,
      customMessage,
      paymentLink,
      senderName: invoice.businessInfo.name,
      includePDF
    });

    if (!emailResult.success) {
      return NextResponse.json({ 
        error: 'Failed to send email', 
        details: emailResult.error 
      }, { status: 500 });
    }

    // Mark invoice as sent if it was a draft
    if (invoice.status === 'draft' && template === 'invoice_send') {
      try {
        await markInvoiceAsSent(invoiceId, session.user.id);
      } catch (updateError) {
        console.warn('Failed to update invoice status:', updateError);
        // Email was sent successfully, so don't fail the request
      }
    }

    return NextResponse.json({
      success: true,
      messageId: emailResult.messageId,
      deliveryTime: emailResult.deliveryTime,
      paymentLinkCreated: !!paymentLink,
      paymentLink: paymentLink
    });

  } catch (error) {
    console.error('Email sending error:', error);
    
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * API Route: Get send options for invoice
 * GET /api/invoices/[id]/send
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: invoiceId } = await params;

    // Validate invoice ID
    if (!ObjectId.isValid(invoiceId)) {
      return NextResponse.json({ error: 'Invalid invoice ID' }, { status: 400 });
    }

    // Get invoice
    const invoice = await getInvoiceById(invoiceId, session.user.id);
    
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Check email configuration
    const emailValidation = await validateEmailConfig();

    // Get existing payment link if available
    let existingPaymentLink: string | undefined;
    if (invoice.payment?.stripePaymentLinkUrl) {
      existingPaymentLink = invoice.payment.stripePaymentLinkUrl;
    }

    return NextResponse.json({
      invoice: {
        id: invoice._id,
        invoiceNumber: invoice.invoiceNumber,
        status: invoice.status,
        clientEmail: invoice.clientInfo.email,
        businessName: invoice.businessInfo.name,
        total: invoice.totals.total,
        currency: invoice.currency
      },
      emailConfigured: emailValidation.isValid,
      existingPaymentLink,
      canCreatePaymentLink: !!process.env.STRIPE_SECRET_KEY,
      suggestedTemplate: invoice.status === 'draft' ? 'invoice_send' : 'payment_reminder'
    });

  } catch (error) {
    console.error('Error getting send options:', error);
    
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}