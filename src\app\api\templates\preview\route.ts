import { NextRequest } from 'next/server';
import { generatePreview, generateThumbnailPreview } from '@/lib/template-renderer';
import { getAllTemplates, getTemplateById } from '@/lib/templates/template-definitions';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('template');
    const type = searchParams.get('type') || 'full'; // 'full' or 'thumbnail'
    
    if (!templateId) {
      // Return list of all available templates
      const templates = getAllTemplates();
      const templateList = templates.map(template => ({
        id: template.id,
        name: template.name,
        category: template.category,
        description: template.description,
        thumbnail: template.thumbnail,
        styling: template.styling
      }));
      
      return new Response(
        JSON.stringify({ templates: templateList }), 
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Check if template exists
    const template = getTemplateById(templateId);
    if (!template) {
      return new Response(
        JSON.stringify({ error: 'Template not found' }), 
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Generate preview HTML
    let previewHtml: string;
    
    if (type === 'thumbnail') {
      previewHtml = generateThumbnailPreview(templateId);
    } else {
      previewHtml = generatePreview(templateId);
    }
    
    // Return HTML preview
    return new Response(previewHtml, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Template-ID': templateId,
        'X-Template-Name': template.name
      }
    });
    
  } catch (error) {
    console.error('Template preview API error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(
      JSON.stringify({ 
        error: 'Preview generation failed', 
        message: errorMessage 
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { templateId, data, type } = await request.json();
    
    if (!templateId) {
      return new Response(
        JSON.stringify({ error: 'Template ID is required' }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Check if template exists
    const template = getTemplateById(templateId);
    if (!template) {
      return new Response(
        JSON.stringify({ error: 'Template not found' }), 
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Generate preview with custom data
    const { renderTemplate, prepareTemplateData } = await import('@/lib/template-renderer');
    
    const templateData = data ? prepareTemplateData(data, template) : template.sampleData;
    const previewHtml = renderTemplate(templateId, templateData);
    
    if (type === 'json') {
      // Return preview data as JSON
      return new Response(
        JSON.stringify({ 
          templateId,
          templateName: template.name,
          html: previewHtml,
          data: templateData
        }), 
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    } else {
      // Return HTML preview
      return new Response(previewHtml, {
        status: 200,
        headers: {
          'Content-Type': 'text/html',
          'X-Template-ID': templateId,
          'X-Template-Name': template.name
        }
      });
    }
    
  } catch (error) {
    console.error('Custom preview generation error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(
      JSON.stringify({ 
        error: 'Custom preview generation failed', 
        message: errorMessage 
      }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}