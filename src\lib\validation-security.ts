// Data Validation and Security for Template Invoice System
import { z } from 'zod';
import { createValidationError, validateEmail, validateRequired } from './error-handling';

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security configuration
export const SECURITY_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_FILE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'],
  MAX_LOGIN_ATTEMPTS: 5,
  LOGIN_LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: 100,
  AI_RATE_LIMIT_MAX_REQUESTS: 10,
  PDF_RATE_LIMIT_MAX_REQUESTS: 20,
  EMAIL_RATE_LIMIT_MAX_REQUESTS: 10
};

// Input sanitization
export function sanitizeHtml(input: string): string {
  if (!input) return '';
  
  // Remove script tags and their content
  let sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove dangerous attributes
  sanitized = sanitized.replace(/\s*on\w+\s*=\s*[^>]*/gi, ''); // Remove on* attributes
  sanitized = sanitized.replace(/\s*javascript:\s*/gi, ''); // Remove javascript: URLs
  sanitized = sanitized.replace(/\s*data:\s*/gi, ''); // Remove data: URLs
  
  // Remove dangerous tags
  const dangerousTags = ['iframe', 'embed', 'object', 'link', 'meta', 'base'];
  dangerousTags.forEach(tag => {
    const regex = new RegExp(`<${tag}\\b[^>]*>.*?<\\/${tag}>`, 'gi');
    sanitized = sanitized.replace(regex, '');
  });
  
  return sanitized.trim();
}

export function sanitizeFilename(filename: string): string {
  if (!filename) return '';
  
  // Remove dangerous characters and normalize
  return filename
    .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace dangerous chars with underscore
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
    .substring(0, 100); // Limit length
}

export function sanitizeText(input: string): string {
  if (!input) return '';
  
  return input
    .trim()
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
    .substring(0, 10000); // Limit length
}

// Validation schemas using Zod
export const BusinessInfoSchema = z.object({
  name: z.string()
    .min(1, 'Business name is required')
    .max(100, 'Business name must be less than 100 characters')
    .transform(sanitizeText),
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email must be less than 255 characters')
    .optional(),
  phone: z.string()
    .max(20, 'Phone must be less than 20 characters')
    .optional()
    .transform((val) => val ? sanitizeText(val) : undefined),
  address: z.string()
    .max(500, 'Address must be less than 500 characters')
    .optional()
    .transform((val) => val ? sanitizeText(val) : undefined),
  city: z.string()
    .max(100, 'City must be less than 100 characters')
    .optional()
    .transform((val) => val ? sanitizeText(val) : undefined),
});

export const ClientInfoSchema = z.object({
  name: z.string()
    .min(1, 'Client name is required')
    .max(100, 'Client name must be less than 100 characters')
    .transform(sanitizeText),
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email must be less than 255 characters')
    .optional(),
  address: z.string()
    .max(500, 'Address must be less than 500 characters')
    .optional()
    .transform((val) => val ? sanitizeText(val) : undefined),
  city: z.string()
    .max(100, 'City must be less than 100 characters')
    .optional()
    .transform((val) => val ? sanitizeText(val) : undefined),
  contactPerson: z.string()
    .max(100, 'Contact person must be less than 100 characters')
    .optional()
    .transform((val) => val ? sanitizeText(val) : undefined),
});

export const LineItemSchema = z.object({
  description: z.string()
    .min(1, 'Description is required')
    .max(500, 'Description must be less than 500 characters')
    .transform(sanitizeText),
  details: z.string()
    .max(1000, 'Details must be less than 1000 characters')
    .optional()
    .transform((val) => val ? sanitizeText(val) : undefined),
  quantity: z.number()
    .min(0.01, 'Quantity must be greater than 0')
    .max(999999, 'Quantity too large'),
  rate: z.number()
    .min(0, 'Rate cannot be negative')
    .max(999999, 'Rate too large'),
  amount: z.number()
    .min(0, 'Amount cannot be negative')
    .max(*********, 'Amount too large'),
});

export const InvoiceTotalsSchema = z.object({
  subtotal: z.number()
    .min(0, 'Subtotal cannot be negative')
    .max(*********, 'Subtotal too large'),
  tax: z.number()
    .min(0, 'Tax cannot be negative')
    .max(*********, 'Tax too large'),
  discount: z.number()
    .min(0, 'Discount cannot be negative')
    .max(*********, 'Discount too large'),
  total: z.number()
    .min(0.01, 'Total must be greater than 0')
    .max(*********, 'Total too large'),
});

export const CreateInvoiceSchema = z.object({
  businessInfo: BusinessInfoSchema,
  clientInfo: ClientInfoSchema,
  invoiceNumber: z.string()
    .min(1, 'Invoice number is required')
    .max(50, 'Invoice number must be less than 50 characters')
    .transform(sanitizeText),
  dueDate: z.date()
    .min(new Date('1900-01-01'), 'Invalid due date')
    .max(new Date('2100-12-31'), 'Invalid due date'),
  lineItems: z.array(LineItemSchema)
    .min(1, 'At least one line item is required')
    .max(100, 'Too many line items'),
  totals: InvoiceTotalsSchema,
  currency: z.string()
    .length(3, 'Currency must be 3 characters')
    .regex(/^[A-Z]{3}$/, 'Invalid currency format'),
  notes: z.string()
    .max(2000, 'Notes must be less than 2000 characters')
    .optional()
    .transform((val) => val ? sanitizeHtml(val) : undefined),
  terms: z.string()
    .max(2000, 'Terms must be less than 2000 characters')
    .optional()
    .transform((val) => val ? sanitizeHtml(val) : undefined),
});

export const EmailSendSchema = z.object({
  to: z.string()
    .email('Invalid email format')
    .optional(),
  cc: z.array(z.string().email('Invalid CC email format'))
    .max(10, 'Too many CC recipients')
    .optional(),
  bcc: z.array(z.string().email('Invalid BCC email format'))
    .max(10, 'Too many BCC recipients')
    .optional(),
  customMessage: z.string()
    .max(2000, 'Message must be less than 2000 characters')
    .optional()
    .transform((val) => val ? sanitizeHtml(val) : undefined),
  includePDF: z.boolean().optional(),
  includePaymentLink: z.boolean().optional(),
});

// Rate limiting functions
export function getRateLimitKey(identifier: string, action: string): string {
  return `${identifier}:${action}`;
}

export function checkRateLimit(
  identifier: string, 
  action: string, 
  maxRequests: number = SECURITY_CONFIG.RATE_LIMIT_MAX_REQUESTS,
  windowMs: number = SECURITY_CONFIG.RATE_LIMIT_WINDOW
): { allowed: boolean; remaining: number; resetTime: number } {
  const key = getRateLimitKey(identifier, action);
  const now = Date.now();
  
  let record = rateLimitStore.get(key);
  
  // Reset if window has expired
  if (!record || now > record.resetTime) {
    record = {
      count: 0,
      resetTime: now + windowMs
    };
  }
  
  // Check if limit exceeded
  if (record.count >= maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: record.resetTime
    };
  }
  
  // Increment counter
  record.count++;
  rateLimitStore.set(key, record);
  
  return {
    allowed: true,
    remaining: maxRequests - record.count,
    resetTime: record.resetTime
  };
}

// Specific rate limit checkers
export function checkAIRateLimit(userId: string) {
  return checkRateLimit(
    userId, 
    'ai_generation', 
    SECURITY_CONFIG.AI_RATE_LIMIT_MAX_REQUESTS,
    SECURITY_CONFIG.RATE_LIMIT_WINDOW
  );
}

export function checkPDFRateLimit(userId: string) {
  return checkRateLimit(
    userId, 
    'pdf_generation', 
    SECURITY_CONFIG.PDF_RATE_LIMIT_MAX_REQUESTS,
    SECURITY_CONFIG.RATE_LIMIT_WINDOW
  );
}

export function checkEmailRateLimit(userId: string) {
  return checkRateLimit(
    userId, 
    'email_send', 
    SECURITY_CONFIG.EMAIL_RATE_LIMIT_MAX_REQUESTS,
    SECURITY_CONFIG.RATE_LIMIT_WINDOW
  );
}

// File upload validation
export function validateFileUpload(
  file: File, 
  maxSize: number = SECURITY_CONFIG.MAX_FILE_SIZE,
  allowedTypes: string[] = SECURITY_CONFIG.ALLOWED_FILE_TYPES
): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size must be less than ${Math.round(maxSize / (1024 * 1024))}MB`
    };
  }
  
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }
  
  // Check filename
  const sanitizedName = sanitizeFilename(file.name);
  if (!sanitizedName || sanitizedName.length < 1) {
    return {
      valid: false,
      error: 'Invalid filename'
    };
  }
  
  return { valid: true };
}

// SQL injection prevention (for any raw queries)
export function escapeSqlString(input: string): string {
  if (!input) return '';
  return input.replace(/'/g, "''").replace(/;/g, '\\;');
}

// NoSQL injection prevention
export function sanitizeMongoQuery(query: any): any {
  if (typeof query !== 'object' || query === null) {
    return query;
  }
  
  const sanitized: any = {};
  
  for (const [key, value] of Object.entries(query)) {
    // Skip dangerous operators
    if (key.startsWith('$') && !['$eq', '$ne', '$in', '$nin', '$gt', '$gte', '$lt', '$lte'].includes(key)) {
      continue;
    }
    
    if (typeof value === 'string') {
      sanitized[key] = sanitizeText(value);
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeMongoQuery(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

// Password strength validation
export function validatePasswordStrength(password: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  // Check for common patterns
  if (/(.)\1{2,}/.test(password)) {
    errors.push('Password cannot contain repeated characters');
  }
  
  if (/^[0-9]+$/.test(password)) {
    errors.push('Password cannot be all numbers');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

// Content Security Policy headers
export const CSP_HEADER = [
  "default-src 'self'",
  "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com",
  "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
  "font-src 'self' https://fonts.gstatic.com",
  "img-src 'self' data: https:",
  "connect-src 'self' https://api.stripe.com",
  "frame-src https://js.stripe.com",
  "object-src 'none'",
  "base-uri 'self'",
  "form-action 'self'",
  "frame-ancestors 'none'",
  "upgrade-insecure-requests"
].join('; ');

// Security headers middleware helper
export function getSecurityHeaders(): Record<string, string> {
  return {
    'Content-Security-Policy': CSP_HEADER,
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'X-XSS-Protection': '1; mode=block'
  };
}

// CSRF token generation and validation
export function generateCSRFToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export function validateCSRFToken(token: string, sessionToken: string): boolean {
  return token === sessionToken;
}

// IP address extraction and validation
export function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const real = request.headers.get('x-real-ip');
  const remote = request.headers.get('remote-addr');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  return real || remote || 'unknown';
}

// Validation middleware wrapper
export function validateRequest<T>(schema: z.ZodSchema<T>) {
  return async (data: unknown): Promise<T> => {
    try {
      return await schema.parseAsync(data);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw createValidationError(
          'Validation failed',
          { zodErrors: error.errors },
          error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        );
      }
      throw error;
    }
  };
}

// Audit logging for security events
export function logSecurityEvent(
  event: string,
  userId?: string,
  ip?: string,
  details?: any
): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    userId,
    ip,
    details,
    severity: 'security'
  };
  
  console.log('[SECURITY EVENT]', JSON.stringify(logEntry));
  
  // In production, send to security monitoring service
}

// Environment validation
export function validateEnvironmentVariables(): { valid: boolean; missing: string[] } {
  const required = [
    'NEXTAUTH_SECRET',
    'MONGODB_URI',
    'NEXTAUTH_URL'
  ];
  
  const optional = [
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'SMTP_HOST',
    'SMTP_USER',
    'SMTP_PASS',
    'OPENAI_API_KEY'
  ];
  
  const missing = required.filter(key => !process.env[key]);
  const missingOptional = optional.filter(key => !process.env[key]);
  
  if (missingOptional.length > 0) {
    console.warn('Optional environment variables missing:', missingOptional);
  }
  
  return {
    valid: missing.length === 0,
    missing
  };
}