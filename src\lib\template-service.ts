// Template Service for Template Invoice System
import { ObjectId } from 'mongodb';
import clientPromise from './mongodb';
import { 
  TemplateDocument, 
  CreateTemplateInput, 
  UpdateTemplateInput, 
  COLLECTIONS 
} from './models';
import { logActivity } from './user-service';

// Get database instance
async function getDatabase() {
  const client = await clientPromise;
  return client.db();
}

// Create template
export async function createTemplate(input: CreateTemplateInput): Promise<TemplateDocument> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const now = new Date();
  
  const template: TemplateDocument = {
    ...input,
    usageCount: 0,
    createdAt: now,
    updatedAt: now
  };
  
  const result = await templatesCollection.insertOne(template);
  
  // Log activity
  await logActivity({
    userId: input.userId,
    action: 'template_created',
    resourceType: 'template',
    resourceId: result.insertedId,
    details: {
      description: `Template '${template.name}' created`,
      metadata: {
        templateName: template.name,
        industry: template.industry,
        isCustom: template.isCustom
      }
    }
  });
  
  return { ...template, _id: result.insertedId };
}

// Update template
export async function updateTemplate(templateId: string | ObjectId, userId: string | ObjectId, updates: UpdateTemplateInput): Promise<TemplateDocument | null> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const templateObjectId = typeof templateId === 'string' ? new ObjectId(templateId) : templateId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const result = await templatesCollection.findOneAndUpdate(
    { _id: templateObjectId, userId: userObjectId },
    { 
      $set: { 
        ...updates,
        updatedAt: new Date()
      }
    },
    { returnDocument: 'after' }
  );
  
  if (result) {
    // Log activity
    await logActivity({
      userId: userObjectId,
      action: 'template_updated',
      resourceType: 'template',
      resourceId: templateObjectId,
      details: {
        description: `Template '${result.value?.name || 'Unknown'}' updated`,
        metadata: { updatedFields: Object.keys(updates) }
      }
    });
  }
  
  return result?.value || null;
}

// Get template by ID
export async function getTemplateById(templateId: string | ObjectId, userId?: string | ObjectId): Promise<TemplateDocument | null> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const templateObjectId = typeof templateId === 'string' ? new ObjectId(templateId) : templateId;
  
  const query: any = { _id: templateObjectId };
  
  // If userId is provided, only return templates owned by user or public templates
  if (userId) {
    const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    query.$or = [
      { userId: userObjectId },
      { isPublic: true }
    ];
  }
  
  return await templatesCollection.findOne(query);
}

// Get user's templates
export async function getUserTemplates(
  userId: string | ObjectId, 
  options: {
    page?: number;
    limit?: number;
    industry?: string;
    isCustom?: boolean;
    search?: string;
    sortBy?: 'name' | 'createdAt' | 'usageCount' | 'lastUsed';
    sortOrder?: 'asc' | 'desc';
  } = {}
): Promise<{ templates: TemplateDocument[]; total: number; hasMore: boolean }> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  const {
    page = 1,
    limit = 20,
    industry,
    isCustom,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = options;
  
  const skip = (page - 1) * limit;
  
  // Build query - include user's templates and public templates
  const query: any = {
    $or: [
      { userId: userObjectId },
      { isPublic: true }
    ]
  };
  
  if (industry) {
    query.industry = industry;
  }
  
  if (typeof isCustom === 'boolean') {
    query.isCustom = isCustom;
  }
  
  if (search) {
    query.$and = [
      query.$or ? { $or: query.$or } : {},
      {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
          { industry: { $regex: search, $options: 'i' } },
          { tags: { $in: [new RegExp(search, 'i')] } }
        ]
      }
    ];
    delete query.$or;
  }
  
  // Build sort
  const sort: any = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
  
  // Execute queries
  const [templates, total] = await Promise.all([
    templatesCollection
      .find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .toArray(),
    templatesCollection.countDocuments(query)
  ]);
  
  const hasMore = skip + templates.length < total;
  
  return { templates, total, hasMore };
}

// Get public templates
export async function getPublicTemplates(
  options: {
    page?: number;
    limit?: number;
    industry?: string;
    category?: string;
    search?: string;
    sortBy?: 'name' | 'usageCount' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
  } = {}
): Promise<{ templates: TemplateDocument[]; total: number; hasMore: boolean }> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const {
    page = 1,
    limit = 20,
    industry,
    category,
    search,
    sortBy = 'usageCount',
    sortOrder = 'desc'
  } = options;
  
  const skip = (page - 1) * limit;
  
  // Build query for public templates only
  const query: any = { isPublic: true };
  
  if (industry) {
    query.industry = industry;
  }
  
  if (category) {
    query.category = category;
  }
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { industry: { $regex: search, $options: 'i' } },
      { tags: { $in: [new RegExp(search, 'i')] } }
    ];
  }
  
  // Build sort
  const sort: any = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
  
  // Execute queries
  const [templates, total] = await Promise.all([
    templatesCollection
      .find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .toArray(),
    templatesCollection.countDocuments(query)
  ]);
  
  const hasMore = skip + templates.length < total;
  
  return { templates, total, hasMore };
}

// Increment template usage count
export async function incrementTemplateUsage(templateId: string | ObjectId): Promise<void> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const templateObjectId = typeof templateId === 'string' ? new ObjectId(templateId) : templateId;
  
  await templatesCollection.updateOne(
    { _id: templateObjectId },
    { 
      $inc: { usageCount: 1 },
      $set: { lastUsed: new Date() }
    }
  );
}

// Clone template for user
export async function cloneTemplate(templateId: string | ObjectId, userId: string | ObjectId, customName?: string): Promise<TemplateDocument> {
  const template = await getTemplateById(templateId);
  
  if (!template) {
    throw new Error('Template not found');
  }
  
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Create a copy of the template for the user
  const clonedTemplate: CreateTemplateInput = {
    userId: userObjectId,
    name: customName || `${template.name} (Copy)`,
    description: template.description,
    industry: template.industry,
    isCustom: true,
    isPublic: false,
    templateStructure: { ...template.templateStructure },
    styling: { ...template.styling },
    previewData: template.previewData ? { ...template.previewData } : undefined,
    tags: template.tags ? [...template.tags] : undefined,
    category: template.category
  };
  
  const result = await createTemplate(clonedTemplate);
  
  // Log activity
  await logActivity({
    userId: userObjectId,
    action: 'template_cloned',
    resourceType: 'template',
    resourceId: result._id!,
    details: {
      description: `Template '${template.name}' cloned as '${result.name}'`,
      metadata: {
        originalTemplateId: template._id?.toString(),
        originalTemplateName: template.name
      }
    }
  });
  
  return result;
}

// Delete template
export async function deleteTemplate(templateId: string | ObjectId, userId: string | ObjectId): Promise<boolean> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const templateObjectId = typeof templateId === 'string' ? new ObjectId(templateId) : templateId;
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Get template details for logging
  const template = await templatesCollection.findOne({ _id: templateObjectId, userId: userObjectId });
  
  if (!template) {
    return false;
  }
  
  const result = await templatesCollection.deleteOne({ _id: templateObjectId, userId: userObjectId });
  
  if (result.deletedCount > 0) {
    await logActivity({
      userId: userObjectId,
      action: 'template_deleted',
      resourceType: 'template',
      details: {
        description: `Template '${template.name}' deleted`,
        metadata: {
          templateName: template.name,
          industry: template.industry
        }
      }
    });
    return true;
  }
  
  return false;
}

// Get template categories
export async function getTemplateCategories(): Promise<string[]> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const categories = await templatesCollection.distinct('category', { isPublic: true });
  return categories.filter((category): category is string => Boolean(category));
}

// Get template industries
export async function getTemplateIndustries(): Promise<string[]> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const industries = await templatesCollection.distinct('industry');
  return industries.filter(Boolean);
}

// Get popular templates
export async function getPopularTemplates(limit: number = 10): Promise<TemplateDocument[]> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  return await templatesCollection
    .find({ isPublic: true })
    .sort({ usageCount: -1 })
    .limit(limit)
    .toArray();
}

// Get recommended templates for user
export async function getRecommendedTemplates(userId: string | ObjectId, limit: number = 5): Promise<TemplateDocument[]> {
  // This could be enhanced with ML/AI recommendations based on user's industry, past usage, etc.
  // For now, we'll return popular templates in industries the user has used
  
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
  
  // Get user's most used industries
  const userTemplates = await templatesCollection
    .find({ userId: userObjectId })
    .toArray();
  
  const userIndustries = Array.from(new Set(userTemplates.map(t => t.industry)));
  
  if (userIndustries.length === 0) {
    // New user, return most popular templates
    return await getPopularTemplates(limit);
  }
  
  // Return popular templates in user's industries
  return await templatesCollection
    .find({ 
      isPublic: true,
      industry: { $in: userIndustries }
    })
    .sort({ usageCount: -1 })
    .limit(limit)
    .toArray();
}

// Search templates
export async function searchTemplates(
  query: string, 
  userId?: string | ObjectId,
  options: {
    limit?: number;
    includePublic?: boolean;
    industry?: string;
  } = {}
): Promise<TemplateDocument[]> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  const { limit = 20, includePublic = true, industry } = options;
  
  const searchQuery: any = {
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } },
      { tags: { $in: [new RegExp(query, 'i')] } }
    ]
  };
  
  if (userId) {
    const userObjectId = typeof userId === 'string' ? new ObjectId(userId) : userId;
    if (includePublic) {
      searchQuery.$and = [
        searchQuery,
        {
          $or: [
            { userId: userObjectId },
            { isPublic: true }
          ]
        }
      ];
    } else {
      searchQuery.userId = userObjectId;
    }
  } else if (includePublic) {
    searchQuery.isPublic = true;
  }
  
  if (industry) {
    searchQuery.industry = industry;
  }
  
  return await templatesCollection
    .find(searchQuery)
    .sort({ usageCount: -1 })
    .limit(limit)
    .toArray();
}

// Create default system templates
export async function createDefaultTemplates(): Promise<void> {
  const db = await getDatabase();
  const templatesCollection = db.collection<TemplateDocument>(COLLECTIONS.TEMPLATES);
  
  // Check if default templates already exist
  const existingCount = await templatesCollection.countDocuments({ isPublic: true });
  if (existingCount > 0) {
    return; // Default templates already created
  }
  
  const systemUserId = new ObjectId(); // System user for default templates
  const now = new Date();
  
  const defaultTemplates: Omit<TemplateDocument, '_id'>[] = [
    {
      userId: systemUserId,
      name: 'Professional',
      description: 'Clean and corporate design perfect for established businesses',
      industry: 'Corporate',
      isCustom: false,
      isPublic: true,
      templateStructure: {
        layout: 'standard',
        sections: [
          { id: 'header', type: 'header', enabled: true, order: 1 },
          { id: 'business', type: 'business', enabled: true, order: 2 },
          { id: 'client', type: 'client', enabled: true, order: 3 },
          { id: 'items', type: 'items', enabled: true, order: 4 },
          { id: 'totals', type: 'totals', enabled: true, order: 5 },
          { id: 'notes', type: 'notes', enabled: true, order: 6 }
        ]
      },
      styling: {
        primaryColor: '#1e40af',
        font: 'Inter',
        fontSize: 14
      },
      usageCount: 0,
      createdAt: now,
      updatedAt: now,
      category: 'Business'
    },
    {
      userId: systemUserId,
      name: 'Modern',
      description: 'Contemporary styling with gradients and modern elements',
      industry: 'Technology',
      isCustom: false,
      isPublic: true,
      templateStructure: {
        layout: 'modern',
        sections: [
          { id: 'header', type: 'header', enabled: true, order: 1 },
          { id: 'business', type: 'business', enabled: true, order: 2 },
          { id: 'client', type: 'client', enabled: true, order: 3 },
          { id: 'items', type: 'items', enabled: true, order: 4 },
          { id: 'totals', type: 'totals', enabled: true, order: 5 },
          { id: 'notes', type: 'notes', enabled: true, order: 6 }
        ]
      },
      styling: {
        primaryColor: '#6366f1',
        font: 'Inter',
        fontSize: 14
      },
      usageCount: 0,
      createdAt: now,
      updatedAt: now,
      category: 'Modern'
    },
    // Add more default templates...
  ];
  
  await templatesCollection.insertMany(defaultTemplates);
}