'use client'

import React, { createContext, useContext, useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { useOnboarding } from '@/hooks/useOnboarding';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';

// Dynamically import the onboarding flow to avoid SSR issues
const OnboardingFlow = dynamic(() => import('./OnboardingFlow'), { ssr: false });

interface OnboardingContextType {
  showOnboarding: boolean;
  setShowOnboarding: (show: boolean) => void;
  triggerOnboarding: () => void;
}

const OnboardingContext = createContext<OnboardingContextType>({
  showOnboarding: false,
  setShowOnboarding: () => {},
  triggerOnboarding: () => {}
});

export const useOnboardingContext = () => useContext(OnboardingContext);

export function OnboardingProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const {
    shouldShowOnboarding,
    shouldShowFirstInvoiceTips,
    completeOnboarding,
    skipOnboarding,
    loading
  } = useOnboarding();
  
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [hasCheckedOnboarding, setHasCheckedOnboarding] = useState(false);

  useEffect(() => {
    // Only check onboarding after session is loaded and user is authenticated
    if (status === 'authenticated' && session?.user && !loading && !hasCheckedOnboarding) {
      // Don't show onboarding on certain pages
      const excludedPaths = ['/auth/signin', '/auth/error', '/api', '/privacy'];
      const shouldExclude = excludedPaths.some(path => pathname?.startsWith(path));
      
      if (!shouldExclude && shouldShowOnboarding()) {
        // Delay showing onboarding to avoid jarring UX
        setTimeout(() => {
          setShowOnboarding(true);
        }, 1000);
      }
      
      setHasCheckedOnboarding(true);
    }
  }, [status, session, loading, shouldShowOnboarding, pathname, hasCheckedOnboarding]);

  const handleComplete = async () => {
    await completeOnboarding();
    setShowOnboarding(false);
  };

  const handleSkip = async () => {
    await skipOnboarding();
    setShowOnboarding(false);
  };

  const triggerOnboarding = () => {
    setShowOnboarding(true);
  };

  return (
    <OnboardingContext.Provider value={{ showOnboarding, setShowOnboarding, triggerOnboarding }}>
      {children}
      
      {/* Render onboarding flow when needed */}
      {showOnboarding && (
        <OnboardingFlow
          isNewUser={true}
          onComplete={handleComplete}
          onSkip={handleSkip}
        />
      )}
      
      {/* Show first invoice tips if needed */}
      {shouldShowFirstInvoiceTips() && pathname === '/create-invoice' && (
        <FirstInvoiceTips />
      )}
    </OnboardingContext.Provider>
  );
}

// First invoice tips component
function FirstInvoiceTips() {
  const [showTips, setShowTips] = useState(true);

  if (!showTips) return null;

  return (
    <div className="fixed bottom-4 right-4 max-w-sm bg-blue-50 border border-blue-200 rounded-lg shadow-lg p-4 z-30">
      <button
        onClick={() => setShowTips(false)}
        className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
      >
        ×
      </button>
      <h3 className="font-semibold text-blue-900 mb-2">
        💡 First Invoice Tips
      </h3>
      <ul className="text-sm text-blue-800 space-y-1">
        <li>• Start with your business details</li>
        <li>• Add clear service descriptions</li>
        <li>• Set appropriate payment terms</li>
        <li>• Preview before sending</li>
      </ul>
      <p className="text-xs text-blue-600 mt-3">
        Need help? Check out our <a href="/help" className="underline">guides</a>
      </p>
    </div>
  );
}