'use client'

import { motion } from 'framer-motion'
import { Check } from 'lucide-react'
import Link from 'next/link'

const plans = [
  {
    name: "Free",
    price: "$0",
    period: "per month",
    features: [
      "Up to 3 invoices/mo",
      "Basic templates",
      "Email support",
      "Google Sign-in",
    ],
    cta: "Get Started",
    highlight: false,
    href: "/create",
  },
  {
    name: "Pro",
    price: "$19.99",
    period: "per month",
    features: [
      "Unlimited invoices",
      "Premium templates",
      "Stripe payments",
      "Priority support",
      "QuickBooks sync",
      "Recurring billing",
      "Multi-currency",
    ],
    cta: "Start Pro",
    highlight: true,
    href: "/upgrade?plan=pro",
  },
  {
    name: "Business",
    price: "$39.99",
    period: "per month",
    features: [
      "Team collaboration",
      "Advanced reporting",
      "API access",
      "White-label options",
      "All Pro features",
      "Custom integrations",
    ],
    cta: "Start Business",
    highlight: false,
    href: "/upgrade?plan=business",
  },
]

export default function PricingCards() {
  return (
    <section className="py-16">
      <motion.h2 
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        className="text-3xl sm:text-4xl font-bold text-center mb-10"
      >
        Pricing for Every Stage
      </motion.h2>
      
      <div className="flex flex-col lg:flex-row gap-10 items-center justify-center">
        {plans.map((plan, i) => (
          <motion.div
            key={plan.name}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: i * 0.1 }}
            className={`w-full max-w-xs rounded-3xl border shadow-xl px-8 py-10 bg-white/70 backdrop-blur-lg flex flex-col items-center relative transition-transform duration-300
              ${plan.highlight ? "scale-105 z-10 border-transparent" : "border-gray-200"}`}
            style={{
              minHeight: 480,
              position: "relative",
              overflow: "visible",
            }}
            whileHover={plan.highlight ? { scale: 1.08 } : { scale: 1.02 }}
          >
            {/* Animated Highlight Border + Glow for Pro */}
            {plan.highlight && (
              <>
                {/* Gradient border animation */}
                <motion.div
                  className="absolute -inset-1.5 rounded-[2rem] pointer-events-none"
                  initial={{ opacity: 0.7 }}
                  animate={{
                    background: [
                      "linear-gradient(135deg, #f7b733 0%, #fc4a1a 100%)",
                      "linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%)",
                      "linear-gradient(135deg, #f7b733 0%, #fc4a1a 100%)",
                    ],
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    repeatType: "mirror",
                  }}
                  style={{
                    filter: "blur(7px)",
                    zIndex: 1,
                  }}
                />
                {/* Soft white glow behind card */}
                <motion.div
                  className="absolute -inset-4 rounded-[2.2rem] bg-white opacity-50 pointer-events-none"
                  animate={{
                    opacity: [0.5, 0.6, 0.5],
                    boxShadow: [
                      "0 0 50px 10px rgba(255,255,255,0.2), 0 0 30px 5px rgba(247,183,51,0.4)",
                      "0 0 80px 16px rgba(255,255,255,0.4), 0 0 36px 8px rgba(252,74,26,0.2)",
                      "0 0 50px 10px rgba(255,255,255,0.2), 0 0 30px 5px rgba(247,183,51,0.4)",
                    ],
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                  }}
                  style={{
                    zIndex: 0,
                  }}
                />
                <span className="absolute -top-5 left-1/2 -translate-x-1/2 bg-gradient-to-r from-yellow-400 via-orange-400 to-pink-500 text-white px-4 py-1 rounded-full text-xs font-semibold shadow-lg">
                  Most Popular
                </span>
              </>
            )}

            <div className="text-2xl font-extrabold mb-2 relative z-10">{plan.name}</div>
            <div className={`text-4xl font-bold mb-1 ${
              plan.highlight 
                ? "bg-gradient-to-r from-yellow-500 via-pink-500 to-black text-transparent bg-clip-text"
                : "text-gray-900"
            }`}>
              {plan.price}
            </div>
            <div className="text-gray-500 mb-6">{plan.period}</div>
            
            <ul className="mb-8 flex-1 space-y-3 w-full">
              {plan.features.map(f => (
                <li key={f} className="flex items-center gap-3 text-gray-700">
                  <span className={`inline-block w-2 h-2 rounded-full flex-shrink-0 ${
                    plan.highlight
                      ? "bg-gradient-to-tr from-yellow-400 via-orange-400 to-pink-500"
                      : "bg-gray-400"
                  }`} />
                  <span className="text-sm">{f}</span>
                </li>
              ))}
            </ul>
            
            <Link
              href={plan.href}
              className={`w-full py-3 rounded-xl text-center font-semibold text-lg shadow transition-all relative z-10 block
                ${plan.highlight
                  ? "bg-gradient-to-r from-yellow-400 via-orange-400 to-pink-500 text-white hover:from-pink-500 hover:to-yellow-400 hover:shadow-lg"
                  : "bg-gray-900/10 text-black hover:bg-black hover:text-white"
                }
              `}
            >
              {plan.cta}
            </Link>
          </motion.div>
        ))}
      </div>
    </section>
  )
}