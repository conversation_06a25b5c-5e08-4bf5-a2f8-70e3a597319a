// Invoice action handlers for PDF download, email sending, and draft management

// Handle PDF download
export async function handleDownloadPDF(templateId: string, invoiceData: any) {
  try {
    const response = await fetch('/api/invoices/pdf', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ templateId, invoiceData })
    })
    
    if (!response.ok) throw new Error('PDF generation failed')
    
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `invoice-${invoiceData.invoiceNumber}.pdf`
    link.click()
    
    window.URL.revokeObjectURL(url)
    return true
    
  } catch (error) {
    console.error('PDF download failed:', error)
    throw error
  }
}

// Handle email sending
export async function handleSendEmail(templateId: string, invoiceData: any, recipientEmail?: string) {
  try {
    const response = await fetch('/api/invoices/email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        templateId, 
        invoiceData,
        recipientEmail: recipientEmail || invoiceData.clientEmail,
        senderEmail: invoiceData.businessEmail
      })
    })
    
    if (!response.ok) throw new Error('Email sending failed')
    
    const result = await response.json()
    return result
    
  } catch (error) {
    console.error('Email sending failed:', error)
    throw error
  }
}

// Handle saving draft to MongoDB
export async function handleSaveDraft(templateId: string, invoiceData: any, userId?: string) {
  try {
    const response = await fetch('/api/invoices', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        templateId, 
        invoiceData,
        userId,
        status: 'draft'
      })
    })
    
    if (!response.ok) throw new Error('Save failed')
    
    const result = await response.json()
    return result.invoiceId || result._id
    
  } catch (error) {
    console.error('Save failed:', error)
    throw error
  }
}

// Logo upload handler
export async function handleLogoUpload(file: File): Promise<string> {
  try {
    // For now, we'll convert to base64 for simplicity
    // In production, you'd upload to a CDN or storage service
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        resolve(result)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
    
  } catch (error) {
    console.error('Logo upload failed:', error)
    throw error
  }
}

// Generate invoice number
export function generateInvoiceNumber(prefix = 'INV'): string {
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.floor(Math.random() * 100).toString().padStart(2, '0')
  return `${prefix}-${timestamp}${random}`
}

// Validate invoice data before submission
export function validateInvoiceData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // Required fields
  if (!data.businessName) errors.push('Business name is required')
  if (!data.businessEmail) errors.push('Business email is required')
  if (!data.clientName) errors.push('Client name is required')
  if (!data.clientEmail) errors.push('Client email is required')
  if (!data.dueDate) errors.push('Due date is required')
  
  // Validate line items
  if (!data.lineItems || data.lineItems.length === 0) {
    errors.push('At least one line item is required')
  } else {
    data.lineItems.forEach((item: any, index: number) => {
      if (!item.description) errors.push(`Line item ${index + 1}: Description is required`)
      if (item.quantity <= 0) errors.push(`Line item ${index + 1}: Quantity must be greater than 0`)
      if (item.rate < 0) errors.push(`Line item ${index + 1}: Rate cannot be negative`)
    })
  }
  
  // Validate email formats
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (data.businessEmail && !emailRegex.test(data.businessEmail)) {
    errors.push('Invalid business email format')
  }
  if (data.clientEmail && !emailRegex.test(data.clientEmail)) {
    errors.push('Invalid client email format')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Format currency
export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// Calculate invoice totals
export function calculateInvoiceTotals(lineItems: any[], taxRate: number) {
  const subtotal = lineItems.reduce((sum, item) => sum + (item.amount || 0), 0)
  const tax = subtotal * (taxRate / 100)
  const total = subtotal + tax
  
  return {
    subtotal,
    tax,
    total
  }
}