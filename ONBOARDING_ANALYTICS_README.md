# Onboarding Flow & Analytics Implementation

## Overview

This document describes the onboarding flow and analytics features implemented for the Template Invoice System.

## Features Implemented

### 1. Onboarding Flow

**Location**: `/src/components/onboarding/`

- **OnboardingFlow.tsx**: Main onboarding component with multi-step tutorial
- **OnboardingProvider.tsx**: Context provider for managing onboarding state
- **useOnboarding.ts**: Custom hook for onboarding logic

**Features**:
- Welcome tutorial highlighting key features
- Guided invoice creation for first invoice
- Sample data demonstrations
- Tips and best practices
- Success celebration when first invoice is sent
- Persistent state tracking (localStorage + database)

**Usage**:
The onboarding flow automatically shows for new users who haven't completed it or created their first invoice.

### 2. Enhanced Analytics Service

**Location**: `/src/lib/analytics-enhanced.ts`

**Critical Metrics Tracked**:
- User signups and first invoice creation
- AI template generation success rates
- Invoice completion rates
- PDF download/email send rates
- Payment link click-through rates
- API response times
- Error rates
- User sessions

**Key Functions**:
- `trackEvent()`: Track any analytics event
- `trackUserJourney()`: Track user progression through key milestones
- `trackAITemplateGeneration()`: Monitor AI performance
- `trackInvoiceLifecycle()`: Track invoice from creation to payment
- `trackPDFOperation()`: Monitor PDF generation performance
- `trackEmailOperation()`: Track email sending and delivery
- `trackPaymentLink()`: Monitor payment link interactions
- `trackAPIPerformance()`: Automatic API performance monitoring

### 3. System Monitoring Dashboard

**Location**: `/src/app/monitoring/page.tsx`

**Views**:
1. **Real-time Dashboard**: Active users, today's stats, alerts
2. **System Health**: Service status for database, AI, email, payment
3. **Critical Metrics**: Detailed performance metrics

**API Endpoint**: `/api/analytics/monitoring`

### 4. MVP Checklist

**Location**: `/src/app/mvp-checklist/page.tsx`

**Test Categories**:
- User Authentication
- AI Template Generation
- Invoice Creation
- PDF Generation
- Email Functionality
- Payment Links
- Mobile Experience

Each test can be run individually or all at once to verify MVP readiness.

## API Endpoints Created

1. **Onboarding Status**: `/api/user/onboarding-status`
   - GET: Fetch user's onboarding status
   - POST: Update onboarding progress
   - DELETE: Reset onboarding

2. **Analytics Monitoring**: `/api/analytics/monitoring`
   - GET: Fetch monitoring data (dashboard, health, metrics)
   - HEAD: Health check endpoint

3. **Test Endpoints**:
   - `/api/ai/test`: Test AI service configuration
   - `/api/email/test`: Test email service configuration
   - `/api/stripe/config`: Check Stripe configuration

## Integration with Existing Code

### Layout Integration
The main layout (`/src/app/layout.tsx`) has been updated to include:
- SessionProvider
- OnboardingProvider

This ensures onboarding is available throughout the application.

### Analytics Integration
To track events in your code:

```typescript
import { trackEvent } from '@/lib/analytics-enhanced';

// Track a simple event
await trackEvent('invoice', 'create', {
  userId: session.user.id,
  metadata: { invoiceNumber: 'INV-001' }
});

// Track with performance metrics
await trackEvent('pdf', 'generate', {
  userId: session.user.id,
  performance: {
    duration: 1500,
    status: 'success'
  }
});
```

### API Middleware
Use the performance tracking wrapper for API routes:

```typescript
import { withPerformanceTracking } from '@/lib/api-middleware';

export const GET = withPerformanceTracking(async (req) => {
  // Your API logic here
  return NextResponse.json({ data });
});
```

## Environment Variables

Add these to your `.env.local` for full functionality:

```env
# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# For frontend checks (Optional)
NEXT_PUBLIC_AI_CONFIGURED=true
NEXT_PUBLIC_EMAIL_CONFIGURED=true
NEXT_PUBLIC_STRIPE_CONFIGURED=true
```

## Usage Instructions

### Viewing Onboarding
1. Sign up as a new user
2. The onboarding flow will automatically appear
3. Complete the tutorial or skip it

### Accessing Monitoring
1. Navigate to `/monitoring` (requires authentication)
2. View real-time metrics and system health
3. Switch between Dashboard, Health, and Metrics views

### Running MVP Tests
1. Navigate to `/mvp-checklist`
2. Click "Run All Tests" or test individually
3. Review results and fix any critical failures

## Best Practices

1. **Track Critical Events**: Always track user signups, first actions, and errors
2. **Monitor Performance**: Use the API middleware for automatic performance tracking
3. **Check System Health**: Regularly review the monitoring dashboard
4. **Test Before Launch**: Use the MVP checklist to ensure all features work

## Troubleshooting

- **Onboarding not showing**: Check if user has `onboarding.completed` in their user document
- **Analytics not tracking**: Ensure MongoDB connection is working
- **Monitoring errors**: Check API endpoint permissions and authentication

## Future Enhancements

- Add more detailed funnel analysis
- Implement A/B testing for onboarding flows
- Add real-time alerting for critical errors
- Create automated health checks
- Add more granular performance metrics