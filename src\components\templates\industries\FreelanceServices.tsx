'use client'

import { motion } from 'framer-motion'
import { PenTool, Code, Palette, Users, Clock, FileText, DollarSign } from 'lucide-react'

// Freelance-focused templates optimized for common pricing models
export const FREELANCE_TEMPLATES = {
  writer: {
    id: 'freelance_writer',
    title: 'Freelance Writer',
    icon: PenTool,
    description: 'Content writing, copywriting, blog posts, articles',
    pricingModels: ['per_word', 'per_hour', 'per_project'],
    
    commonServices: [
      { service: 'Blog Post Writing', rate: '$0.10-$0.50', unit: 'per word', notes: '500-2000 words typical' },
      { service: 'Website Copy', rate: '$75-$150', unit: 'per hour', notes: 'Includes research and revisions' },
      { service: 'Content Strategy', rate: '$1500-$5000', unit: 'per project', notes: '3-month content calendar' },
      { service: 'Ghostwriting', rate: '$0.25-$1.00', unit: 'per word', notes: 'Long-form content premium' },
      { service: 'Rush Delivery', rate: '+50%', unit: 'surcharge', notes: 'Less than 48 hours' }
    ],
    
    templateStructure: {
      projectDetails: {
        title: 'Project Details',
        fields: [
          { id: 'content_type', label: 'Content Type', type: 'select', 
            options: ['Blog Post', 'Website Copy', 'Article', 'Press Release', 'Email Campaign', 'Social Media', 'Other'] },
          { id: 'word_count', label: 'Target Word Count', type: 'number', placeholder: 'Estimated words' },
          { id: 'topic', label: 'Topic/Subject', type: 'text', placeholder: 'Brief description of content topic' },
          { id: 'deadline', label: 'Deadline', type: 'date', placeholder: 'When content is due' },
          { id: 'research_required', label: 'Research Required', type: 'checkbox', placeholder: '' }
        ]
      },
      
      writingServices: {
        title: 'Writing Services',
        fields: [
          { id: 'writing_hours', label: 'Writing Time', type: 'number', placeholder: 'Hours for writing' },
          { id: 'research_hours', label: 'Research Time', type: 'number', placeholder: 'Hours for research' },
          { id: 'revision_rounds', label: 'Revision Rounds', type: 'select', 
            options: ['1 Round', '2 Rounds', '3 Rounds', 'Unlimited'] },
          { id: 'seo_optimization', label: 'SEO Optimization', type: 'checkbox', placeholder: '' },
          { id: 'rush_delivery', label: 'Rush Delivery (48hrs)', type: 'checkbox', placeholder: '' }
        ]
      },
      
      deliverables: {
        title: 'Deliverables',
        fields: [
          { id: 'final_draft', label: 'Final Written Content', type: 'checkbox', placeholder: '' },
          { id: 'outline', label: 'Content Outline', type: 'checkbox', placeholder: '' },
          { id: 'keyword_research', label: 'Keyword Research Report', type: 'checkbox', placeholder: '' },
          { id: 'meta_descriptions', label: 'Meta Descriptions', type: 'checkbox', placeholder: '' },
          { id: 'revisions', label: 'Revision Documentation', type: 'checkbox', placeholder: '' }
        ]
      }
    },
    
    paymentTerms: {
      standard: '50% upfront, 50% on delivery',
      suggestions: ['Net 15 for established clients', '100% upfront for new clients', 'Per milestone for large projects'],
      industryNorm: '50% deposit is standard for content work'
    },
    
    legalClauses: [
      'Content ownership transfers to client upon full payment',
      'Writer retains right to use work samples for portfolio',
      'Additional revisions beyond agreed scope billed at hourly rate',
      'Client responsible for fact-checking and legal compliance',
      'Rush delivery subject to availability and 50% surcharge'
    ]
  },

  developer: {
    id: 'web_developer',
    title: 'Web Developer',
    icon: Code,
    description: 'Websites, web apps, e-commerce, maintenance',
    pricingModels: ['per_hour', 'per_project', 'monthly_retainer'],
    
    commonServices: [
      { service: 'Website Development', rate: '$50-$150', unit: 'per hour', notes: 'Varies by complexity and experience' },
      { service: 'E-commerce Site', rate: '$3000-$15000', unit: 'per project', notes: 'Depends on features and integrations' },
      { service: 'Monthly Maintenance', rate: '$200-$800', unit: 'per month', notes: 'Updates, backups, security monitoring' },
      { service: 'Bug Fixes', rate: '$75-$125', unit: 'per hour', notes: 'Post-launch support' },
      { service: 'Rush Development', rate: '+25%', unit: 'surcharge', notes: 'Tight deadlines' }
    ],
    
    templateStructure: {
      projectDetails: {
        title: 'Project Details',
        fields: [
          { id: 'project_type', label: 'Project Type', type: 'select',
            options: ['Business Website', 'E-commerce Store', 'Web Application', 'Landing Page', 'WordPress Site', 'Custom Development'] },
          { id: 'technology_stack', label: 'Technology Stack', type: 'textarea', placeholder: 'React, Node.js, MongoDB, etc.' },
          { id: 'project_timeline', label: 'Timeline', type: 'select',
            options: ['1-2 weeks', '3-4 weeks', '1-2 months', '3+ months'] },
          { id: 'responsive_design', label: 'Mobile Responsive', type: 'checkbox', placeholder: '' }
        ]
      },
      
      developmentPhases: {
        title: 'Development Phases',
        fields: [
          { id: 'planning_hours', label: 'Planning & Architecture', type: 'number', placeholder: 'Hours for planning' },
          { id: 'design_hours', label: 'UI/UX Design', type: 'number', placeholder: 'Hours for design' },
          { id: 'coding_hours', label: 'Development/Coding', type: 'number', placeholder: 'Hours for coding' },
          { id: 'testing_hours', label: 'Testing & QA', type: 'number', placeholder: 'Hours for testing' },
          { id: 'deployment_hours', label: 'Deployment & Launch', type: 'number', placeholder: 'Hours for deployment' }
        ]
      },
      
      additionalServices: {
        title: 'Additional Services',
        fields: [
          { id: 'domain_setup', label: 'Domain Setup', type: 'checkbox', placeholder: '' },
          { id: 'hosting_configuration', label: 'Hosting Configuration', type: 'checkbox', placeholder: '' },
          { id: 'ssl_certificate', label: 'SSL Certificate Setup', type: 'checkbox', placeholder: '' },
          { id: 'google_analytics', label: 'Analytics Setup', type: 'checkbox', placeholder: '' },
          { id: 'training_session', label: 'Client Training (2 hours)', type: 'checkbox', placeholder: '' },
          { id: 'maintenance_package', label: '3-Month Maintenance', type: 'checkbox', placeholder: '' }
        ]
      }
    },
    
    paymentTerms: {
      standard: '50% upfront, 50% on completion',
      suggestions: ['25% upfront, 25% at milestone, 50% completion', 'Monthly billing for retainer clients'],
      industryNorm: 'Most developers require 50% deposit before starting work'
    },
    
    legalClauses: [
      'Source code ownership transfers upon full payment',
      'Developer retains portfolio rights for completed work',
      'Client responsible for providing content and assets',
      'Additional features beyond scope billed separately',
      'Hosting and domain costs are client responsibility'
    ]
  },

  designer: {
    id: 'graphic_designer',
    title: 'Graphic Designer',
    icon: Palette,
    description: 'Logos, branding, print design, digital graphics',
    pricingModels: ['per_project', 'per_hour', 'package_based'],
    
    commonServices: [
      { service: 'Logo Design', rate: '$300-$2500', unit: 'per project', notes: '3-5 concepts, unlimited revisions' },
      { service: 'Brand Package', rate: '$1500-$5000', unit: 'per project', notes: 'Logo, colors, fonts, guidelines' },
      { service: 'Print Design', rate: '$50-$100', unit: 'per hour', notes: 'Brochures, flyers, business cards' },
      { service: 'Digital Graphics', rate: '$40-$80', unit: 'per hour', notes: 'Social media, web graphics' },
      { service: 'Rush Jobs', rate: '+50%', unit: 'surcharge', notes: 'Under 48 hours' }
    ],
    
    templateStructure: {
      projectDetails: {
        title: 'Project Details',
        fields: [
          { id: 'design_type', label: 'Design Type', type: 'select',
            options: ['Logo Design', 'Brand Identity', 'Print Design', 'Web Graphics', 'Packaging', 'Marketing Materials'] },
          { id: 'project_scope', label: 'Project Scope', type: 'textarea', placeholder: 'Detailed description of design needs' },
          { id: 'target_audience', label: 'Target Audience', type: 'text', placeholder: 'Who is this designed for?' },
          { id: 'brand_style', label: 'Brand Style', type: 'text', placeholder: 'Modern, classic, playful, etc.' }
        ]
      },
      
      designProcess: {
        title: 'Design Process',
        fields: [
          { id: 'initial_concepts', label: 'Initial Concepts', type: 'number', placeholder: 'Number of initial designs' },
          { id: 'revision_rounds', label: 'Revision Rounds', type: 'select',
            options: ['2 Rounds', '3 Rounds', 'Unlimited', 'Per Revision ($)'] },
          { id: 'consultation_hours', label: 'Consultation Time', type: 'number', placeholder: 'Hours for client meetings' },
          { id: 'research_hours', label: 'Research & Planning', type: 'number', placeholder: 'Market research, competitor analysis' }
        ]
      },
      
      deliverables: {
        title: 'Deliverables',
        fields: [
          { id: 'final_files', label: 'Final Design Files', type: 'checkbox', placeholder: '' },
          { id: 'vector_files', label: 'Vector Files (AI, EPS)', type: 'checkbox', placeholder: '' },
          { id: 'high_res_images', label: 'High-Resolution Images', type: 'checkbox', placeholder: '' },
          { id: 'web_optimized', label: 'Web-Optimized Files', type: 'checkbox', placeholder: '' },
          { id: 'print_ready', label: 'Print-Ready Files', type: 'checkbox', placeholder: '' },
          { id: 'brand_guidelines', label: 'Brand Usage Guidelines', type: 'checkbox', placeholder: '' },
          { id: 'color_palette', label: 'Color Palette Guide', type: 'checkbox', placeholder: '' }
        ]
      }
    },
    
    paymentTerms: {
      standard: '50% upfront, 50% on final delivery',
      suggestions: ['100% upfront for projects under $500', '1/3 upfront, 1/3 at concept approval, 1/3 completion'],
      industryNorm: 'Most designers require deposit before starting creative work'
    },
    
    legalClauses: [
      'Design ownership transfers upon full payment',
      'Designer retains portfolio and promotional rights',
      'Additional revisions beyond scope billed hourly',
      'Client responsible for final proofreading and approval',
      'Usage rights limited to agreed scope unless specified'
    ]
  },

  virtual_assistant: {
    id: 'virtual_assistant',
    title: 'Virtual Assistant',
    icon: Users,
    description: 'Administrative support, customer service, data entry',
    pricingModels: ['per_hour', 'monthly_retainer', 'per_task'],
    
    commonServices: [
      { service: 'General Admin Tasks', rate: '$15-$40', unit: 'per hour', notes: 'Email, scheduling, basic admin' },
      { service: 'Customer Service', rate: '$18-$45', unit: 'per hour', notes: 'Chat support, email responses' },
      { service: 'Data Entry', rate: '$12-$25', unit: 'per hour', notes: 'CRM updates, spreadsheet work' },
      { service: 'Social Media Management', rate: '$20-$50', unit: 'per hour', notes: 'Content creation, posting, engagement' },
      { service: 'Monthly Retainer', rate: '$500-$2000', unit: 'per month', notes: '20-80 hours monthly' }
    ],
    
    templateStructure: {
      serviceDetails: {
        title: 'Service Details',
        fields: [
          { id: 'service_type', label: 'Service Type', type: 'select',
            options: ['Administrative Support', 'Customer Service', 'Data Entry', 'Social Media', 'Email Management', 'Research', 'Other'] },
          { id: 'hours_needed', label: 'Hours Needed', type: 'number', placeholder: 'Estimated hours for project' },
          { id: 'deadline', label: 'Deadline', type: 'date', placeholder: 'When work needs completion' },
          { id: 'complexity_level', label: 'Complexity', type: 'select',
            options: ['Basic', 'Intermediate', 'Advanced', 'Specialized'] }
        ]
      },
      
      taskBreakdown: {
        title: 'Task Breakdown',
        fields: [
          { id: 'email_management', label: 'Email Management', type: 'number', placeholder: 'Hours for email tasks' },
          { id: 'calendar_scheduling', label: 'Calendar & Scheduling', type: 'number', placeholder: 'Hours for scheduling' },
          { id: 'data_entry_hours', label: 'Data Entry', type: 'number', placeholder: 'Hours for data work' },
          { id: 'research_hours', label: 'Research Tasks', type: 'number', placeholder: 'Hours for research' },
          { id: 'customer_support', label: 'Customer Support', type: 'number', placeholder: 'Hours for customer service' }
        ]
      },
      
      deliverables: {
        title: 'Deliverables',
        fields: [
          { id: 'completed_tasks', label: 'Completed Task List', type: 'checkbox', placeholder: '' },
          { id: 'time_tracking', label: 'Detailed Time Log', type: 'checkbox', placeholder: '' },
          { id: 'progress_reports', label: 'Progress Reports', type: 'checkbox', placeholder: '' },
          { id: 'organized_files', label: 'Organized Digital Files', type: 'checkbox', placeholder: '' },
          { id: 'handover_notes', label: 'Handover Documentation', type: 'checkbox', placeholder: '' }
        ]
      }
    },
    
    paymentTerms: {
      standard: 'Weekly or bi-weekly invoicing',
      suggestions: ['Monthly retainer for ongoing work', 'Net 15 for project-based work'],
      industryNorm: 'Regular payment schedule important for ongoing relationships'
    },
    
    legalClauses: [
      'Confidentiality agreement for all client information',
      'Work performed during agreed hours only',
      'Additional tasks beyond scope require approval',
      'Client provides necessary access and tools',
      'Communication during business hours unless emergency'
    ]
  }
}

export default function FreelanceServices() {
  return (
    <div className="py-12 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Freelance Service Templates
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Professional invoice templates designed specifically for freelancers. 
            Industry-specific fields, pricing models, and terms that get you paid faster.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {Object.values(FREELANCE_TEMPLATES).map((template, index) => {
            const Icon = template.icon
            return (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">{template.title}</h3>
                    <p className="text-gray-600">{template.description}</p>
                  </div>
                </div>

                {/* Pricing Models */}
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Pricing Options:</p>
                  <div className="flex flex-wrap gap-2">
                    {template.pricingModels.map((model) => (
                      <span
                        key={model}
                        className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs"
                      >
                        {model.replace('_', ' ')}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Common Services Preview */}
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Typical Services:</p>
                  <div className="space-y-2">
                    {template.commonServices.slice(0, 3).map((service, i) => (
                      <div key={i} className="flex justify-between items-center text-sm">
                        <span className="text-gray-700">{service.service}</span>
                        <span className="font-medium text-gray-900">
                          {service.rate} {service.unit}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Payment Terms */}
                <div className="border-t pt-4">
                  <p className="text-sm font-medium text-gray-700 mb-1">Standard Terms:</p>
                  <p className="text-sm text-gray-600">{template.paymentTerms.standard}</p>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Benefits Section */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Why Freelancers Choose Our Templates
          </h3>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Save Time</h4>
              <p className="text-gray-600 text-sm">
                Pre-filled industry terms and common services. Create invoices in under 2 minutes.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Look Professional</h4>
              <p className="text-gray-600 text-sm">
                Industry-specific language and structure that impresses clients.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Get Paid Faster</h4>
              <p className="text-gray-600 text-sm">
                Optimized payment terms and clear deliverables reduce payment delays.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}